<template>
  <c-layout title="实名认证">
    <view class="business-list-box">
      <ordinary-verified-new :options="options"></ordinary-verified-new>
    </view>
  </c-layout>
</template>

<script>
import CTabs from "@/pages/FifthG/components/list/CTabs";
import CLayout from "@/pages/FifthG/components/layout/CLayout";
import { mapState, mapGetters, mapActions } from 'vuex';
import OrdinaryVerifiedNew from "../open-account/ordinaryVerifiedNew";
export default {
  name: 'VerifiedID',
  data() {
    return {
      tabs: [],
      ordVerified: false,
      h5Verified: false,
      options: {}
    };
  },
  components: {
    OrdinaryVerifiedNew,
    CTabs,
    CLayout,
  },
  onLoad(options) {
    console.log('onLoad verifiedId -- options')
    console.log(options)
    this.options = options;
  },
  async onShow() {
    console.log('onShow verifiedId -- options')
    console.log(this.options)
    let poid = this.options.poid
    if (!poid) {
      poid = this.stepInfo.poid
    }
    if (poid) {
      await this.queProcess({
        poid: poid
      })
      this.qryH5AuthResult(poid)
    }
  },
  computed: {
    ...mapGetters({
      custInfo: 'getCustInfo',
      userInfo: 'getUserInfo',
      stepInfo: 'getStepInfo'
    })
  },
  methods: {
    ...mapActions({
      saveProcess: 'saveProcess',
      queProcess: 'queProcess'
    }),
    qryAuthIdetifyType() {
      const vm = this
      vm.$http.post(vm.$apis.qryAuthIdetifyType, {}).then(resp => {
        console.log(resp)
        if (resp && resp.responseBody.type) {
          const type = resp.responseBody.type
          vm.tabs = []
          if (type.indexOf("~") != -1) {
            vm.ordVerified = true
            vm.h5Verified = false
            type.split('~').forEach(item => {
              vm.tabs.push({
                name: item == '0' ? "读卡器认证" : "H5认证",
                value: item
              })
            })
          } else {
            if (type == 0) {
              vm.ordVerified = true
              vm.h5Verified = false
            } else {
              vm.ordVerified = false
              vm.h5Verified = true
            }
          }
        } else {
          // 默认使用 普通认证方式
          vm.ordVerified = true
          vm.h5Verified = false
        }
      });
    },
    // 查询实认证结果
    qryH5AuthResult(poid) {
      const vm = this
      let orderid = ''
      let step = this.stepInfo.data
      if(!step){
        return
      }
      orderid = step.IDCONF.orderid
      vm.$http.post(vm.$apis.qryH5AuthResult, {
        orderid: orderid,
        poid: poid
      }).then(resp => {
        console.log(resp)
        if (resp && resp.responseBody) {
          if (resp.responseBody.code == '0') {
            // 已实名认证
            this.saveData(poid)
          } else {
            this.qryAuthIdetifyType()
          }
        }
      })
    },
    saveData(poid) {
      let step = this.stepInfo.data
      if(!step){
        return
      }
      const idconfig = step.IDCONF
      this.$set(idconfig, 'identifyStatus', '0')
      const data = {
        poid: poid,
        temp: '1',
        cpcode: 'IDCONF',
        data: JSON.stringify(idconfig)
      }
      let keepData = {
        data,
        refreshCustInfo: true,
        custid: this.custInfo.custid
      };
      this.saveProcess(keepData);
    },
    onTabClick(item) {
      if (item.value == 0) {
        this.ordVerified = true
        this.h5Verified = false
      } else {
        this.ordVerified = false
        this.h5Verified = true
      }
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
