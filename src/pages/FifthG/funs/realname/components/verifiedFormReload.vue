<template>
	<uni-forms class="verifiedForms" ref="uniForm" :modelValue="formParams">

		<IDMsg :params="formParams"></IDMsg>

    <uni-forms-item style="border-top: 1px solid #f2f2f2" v-if="isShow" :label="'收入证明'">
      <view class="page"  @click="toIncomeFile()">
        <view class="dateTxt" style="color: #1F8FF7" v-if="Object.values(incomeFileData).length > 0">已上传</view>
        <view class="dateTxt" style="color: #aaa" v-else>请上传收入来源证明</view>
        <view class="icon-arw"></view>
      </view>
    </uni-forms-item>

    <template v-for="item in list">
      <uni-forms-item :key="item.label" :label="item.label">
        <picker  class="picker" v-if="item.tag == 'picker'" :value="item.index" :range-key="'key'"
                 :range="item.range" @change="((event) => newPickerChange (event, item))">
          <view class="pickerTxt">{{  item.index == -1 ? '请选择' : item.range[item.index].key }}</view>
        </picker>
      </uni-forms-item>
    </template>

		<uni-forms-item v-for="(item, index) in data" :key="item.label" :label="item.label">
			<!-- :disabled ="!item.abled" -->
			<uni-easyinput  :autoHeight="true"  v-if="item.tag == 'input'" :type="item.type" :value="formParams[item.field]"
				:placeholder="item.placeholder" :inputBorder="false" @blur="((event) => blur (event, item))">
			</uni-easyinput>
			<!-- :disabled ="!item.abled" -->
			<picker  class="picker" v-if="item.tag == 'picker'" :value="item.index" :range-key="item.rangeKey"
				:range="item.range" @change="((event) => pickerChange (event, item))">
				<view class="pickerTxt">{{item.range[item.index][item.rangeKey] ||  item.range[item.index] }}</view>
			</picker>
			<view :class="['dateTxt', {'not': !formParams[item.field]}]" v-if="item.tag == 'date'">
        <view class="txt" @click="dateClick(item.field)">
					{{ formParams[item.field] || item.placeholder || '请选择有效期' }}
				</view>
			</view>
		</uni-forms-item>

		<view class="datePicker">
			<uni-datetime-picker ref="startDate" v-model="startRange" type="date" :end="startParams.end"
				:clear-icon="false" @change="startChange"></uni-datetime-picker>
			<uni-datetime-picker ref="endDate" v-model="endRange" type="date" :start="endParams.start"
				:clear-icon="false" @change="endChange"></uni-datetime-picker>
		</view>
	</uni-forms>
</template>

<script>
	// import uniForms from '@/components/uni-forms/uni-forms/uni-forms'
	import uniDatetimePicker from '@/components/uni-datetime-picker/uni-datetime-picker'
	import IDMsg from '@/components/FifthG/IDMsg.vue'
  import CommonMsgItem from "../../../components/CommonMsgItem.vue";
	export default {
		name: 'verifiedForms',
		props: {
			data: {
				type: Array,
				default: () => {
					return []
				},
			},
			params: {
				type: Object,
				default: () => {
					return {}
				},
			},
		},
		data() {
			return {
				formParams: {},
				startRange: '',
				endRange: '',
				startParams: {
					start: '',
					end: this.dateFormat(new Date()),
				},
				endParams: {
					start: '',
					end: '',
				},
				field: '',
        incomeFileData: {},
        list: []
			}
		},

    computed: {
      isShow() {
        return this.$util.dataProcess.isAgeBetween16And18(this.formParams.license)
      }
    },
		components: {
			uniDatetimePicker,
			IDMsg,
      CommonMsgItem
		},
		watch: {
			params: {
				handler(newVal, oldVal) {
					this.InitData()
				},
				immediate: true,
				deep: true,
			}
		},
		created() {
      console.log('verifiedForm====>', this.data)
      console.log('this.formParams====>', this.formParams)
      console.log('this.isShow====>', this.isShow)
			this.InitData()
      this.$bus.$on('incomeFileData', (data) => {
        this.incomeFileData = data
        console.log('verifiedForm====>this.incomeFileData===>', this.incomeFileData)
      })
		},
		methods: {
			InitData() {
				this.formParams = JSON.parse(JSON.stringify(this.params))
				let matchStr = this.formParams.validity.match(/\d{4}([\-])\d{1,2}\1\d{1,2}/g)
				this.startRange = matchStr ? matchStr[0] : ""
				this.endRange = matchStr ? matchStr[1] : ""
			},
			blur(event, item) {
				this.formParams[item.field] = event.detail.value
				this.$emit('change', this.formParams)
			},
			pickerChange(event, item) {
				console.log(event)
				console.log(item)
				this.formParams[item.field] = item.range[event.detail.value]
				item.index = event.detail.value
				// this.formParams[item.field] = event.detail.value
				this.$emit('change', this.formParams)
			},
      newPickerChange(event, item) {
        const selectedIndex = event.detail.value;
        item.index = selectedIndex;
        // 将选中的 value 存入 formParams
        this.formParams[item.field] = item.range[selectedIndex].value;
        this.$emit('change', this.formParams);
      },
			dateClick(field) {
				this.field = field
				this.$refs.startDate.show()
				uni.showToast({
					title: '请选择生效日期',
					icon: 'none'
				})
			},
			startChange(val) {
				console.log(val)
				this.endParams.start = val
				this.endRange = val
				this.$refs.endDate.show()
				uni.showToast({
					title: '请选择失效日期',
					icon: 'none'
				})
			},
			endChange(val) {
				console.log(val)
				let endVal = val,
					startVal = this.startRange
				if (endVal == startVal || !endVal) {
					endVal = startVal
				}
				// startVal = startVal.replace(/-|\-/g, '.')
				// endVal = endVal.replace(/-|\-/g, '.')
				this.formParams[this.field] = startVal + ' 至 ' + endVal
				console.log(this.formParams[this.field])
				this.$emit('change', this.formParams)
			},
			dateFormat(date) {
				let year = date.getFullYear()
				let month = date.getMonth() + 1
				let day = date.getDate()
				month = month >= 10 ? month : ('0' + month)
				day = day >= 10 ? day : ('0' + day)
				return year + '-' + month + '-' + day
			},
			refreshUI(){
				console.log("refreshUI")
				console.log(this.params)
				this.$forceUpdate()
			},
      toIncomeFile() {
        this.$rauter.navigateTo({
          url: "incomeFile"
        });
      },
      setListData(data) {
        console.log('verifiedFormNew====>setListData', data)
        this.list = data
      }
		}
	}
</script>

<style lang="scss" scoped>
	.verifiedForms {
		padding: 0;
		position: relative;

		::v-deep .uni-forms-item {
			.uni-forms-item__inner {
				padding-bottom: 0px;
				border-bottom: 1px solid #f2f2f2;

				.uni-easyinput__content {
					min-height: 50px;
				}

				.uni-easyinput__content-textarea {
					min-height: 80rpx !important;
					padding-left: 10px;
				}

				.is-disabled{
					color: #4A4A4A;
					background-color: #fff !important;
				}

				input {
					height: 50px;
				}
			}

			.uni-forms-item__label {
				height: 50px;
			}

			.picker {
				height: 50px;

				.pickerTxt {
					font-size: 14px;
					line-height: 50px;
					height: 50px;
					padding: 0px 10px;
				}
			}
		}

		.datePicker {
			// width: 100%;
			// height: 100%;
			// opacity: 0;
			position: fixed;
			top: -999px;
			left: -999px;
			z-index: 1;
		}

		.dateTxt {
			padding-left: 10px;
			padding-right: 10px;
			height: 50px;
			line-height: 50px;
			font-size: 14px;
			color: #333;
			position: relative;
			overflow: hidden;

			&.not {
				color: #aaa;

				.txt {
					color: #aaa;
				}
			}

			.txt {
				position: relative;
				// z-index: 12;
				height: 50px;
				line-height: 50px;
				font-size: 14px;
				color: #333;
			}
		}

    .page {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;


      .page-txt {
        padding-left: 10px;
        padding-right: 10px;
        height: 50px;
        line-height: 50px;
        font-size: 14px;
        color: #333;
        position: relative;
        overflow: hidden;
      }

      .icon-arw {
        width: 20rpx;
        height: 20rpx;
        background-image: url(../../../../../static/fifthg/2023/icon-arw.png);
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 20rpx 20rpx;
      }
    }
	}
</style>
