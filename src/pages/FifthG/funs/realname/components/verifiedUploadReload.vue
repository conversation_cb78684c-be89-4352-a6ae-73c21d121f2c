<template>
  <view class="verified-upload-box">
    <view class="verified-upload-desc">
      <text>上传身份证照片<span style="color: red;">(国徽面为正面，头像面为反面)</span></text>
    </view>

    <view class="verified-upload-body" v-if="RNAT == 4">
      <view class="img-upload-box">
        <div class="img-box">
          <div class="progress" v-if="aliProcess != 0 && aliProcess != 100">
            <div class="line" :style="`width: ${aliProcess}%;`"></div>
          </div>
          <div class="err" v-if="aliErr"><span>{{ aliErr }}</span></div>
          <div v-if="aliProcess == 100" class="success-class">
            <div class="img-success"></div>
            <div class="text-success">活体检测成功</div>
          </div>
          <div v-else class="img" @click="onAliLivenessDetection" :style="`background-image: url(${aliSrc});}`"></div>
        </div>
        <view class="img-desc">活体检测</view>
        <i v-if="aliClose" class="iconfont icon-qingchushuru delete-img-btn" @click="onReloadAliAuth"/>
      </view>
    </view>
    <view class="verified-upload-body" v-else>
      <view class="img-upload-box">
        <div class="img-box" @click="onOpenCamera('avatarImg')">
          <div class="progress" v-if="avatarImgProcess != 0 && avatarImgProcess != 100">
            <div class="line" :style="`width: ${avatarImgProcess}%;`"></div>
          </div>
          <div class="err" v-if="avatarImgErr"><span>{{ avatarImgErr }}</span></div>
          <div class="img" :style="`background-image: url(${avatarImgSrc});}`"></div>
        </div>
        <view class="img-desc">现场人像照片</view>
        <i v-if="avatarImgClose" class="iconfont icon-qingchushuru delete-img-btn" @click="onDeleteAvatar('avatarImg')"/>
      </view>
      <view class="img-upload-box" v-if="RNAT == 3">
        <div class="img-box" @click="onOpenCamera('avatar2Img')">
          <div class="progress" v-if="avatar2ImgProcess != 0 && avatar2ImgProcess != 100">
            <div class="line" :style="`width: ${avatar2ImgProcess}%;`"></div>
          </div>
          <div class="err" v-if="avatar2ImgErr"><span>{{ avatar2ImgErr }}</span></div>
          <div class="img" :style="`background-image: url(${avatar2ImgSrc});}`"></div>
        </div>
        <view class="img-desc">现场人像照片2</view>
        <i v-if="avatar2ImgClose" class="iconfont icon-qingchushuru delete-img-btn" @click="onDeleteAvatar('avatar2Img')"/>
      </view>
    </view>

    <view class="verified-upload-body">
      <view class="img-upload-box">
        <!-- <img ref="frontImg" class="img-box" :src="imgBase64.frontImg"/> -->
        <div class="img-box" @click="onOpenCamera('frontImg')">
          <div class="progress" v-if="frontImgProcess != 0 && frontImgProcess != 100">
            <div class="line" :style="`width: ${frontImgProcess}%;`"></div>
          </div>
          <div class="err" v-if="frontImgErr"><span>{{ frontImgErr }}</span></div>
          <div class="img" :style="`background-image: url(${frontImgSrc});}`"></div>
        </div>
        <view class="img-desc">国徽面</view>
        <i v-if="frontImgClose" class="iconfont icon-qingchushuru delete-img-btn" @click="onDeleteImg('frontImg')"/>
      </view>
      <view class="img-upload-box">
        <div class="img-box" @click="onOpenCamera('backImg')">
          <div class="progress" v-if="backImgProcess != 0 && backImgProcess != 100">
            <div class="line" :style="`width: ${backImgProcess}%;`"></div>
          </div>
          <div class="err" v-if="backImgErr"><span>{{ backImgErr }}</span></div>
          <div class="img" :style="`background-image: url(${backImgSrc});}`"></div>
        </div>
        <view class="img-desc">人像面</view>
        <i v-if="backImgClose" class="iconfont icon-qingchushuru delete-img-btn" @click="onDeleteImg('backImg')"/>
      </view>
    </view>
    <face-instant v-if="avatarInit" class="faceInstant" ref="faceInstantRef" v-show="!avatarComplete" @cancel="avatarCancel" @success="avatarSuccess" :is-init="avatarInit" :screen-count="avatarCount" />



  </view>
</template>

<script>
import img from '../../../../../components/FifthG/js/img.js';
import * as imageConversion from 'image-conversion'
import { mapGetters } from 'vuex';
import { createfileImg, handleCanvas, createCanvasBlob, getPixel, compressImg } from '@/common/js/drawWaterMarkUtils.js'
import { dateFormat } from '@/common/js/DateUtils.js'
import FaceInstant from '@/pages/FifthG/funs/faceDetect/faceInstant'
import util from "../../../../../common/js/util";
export default {
  name: 'verifiedUpload',
  props: {
    params: {
      type: Object,
      default: () => {
        return {}
      },
    },
    busiType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showFront: false,
      showBack: false,
      showAvatar: false,
      imgBase64: {
        frontImg: img.frontImg,
        backImg: img.backImg,
        avatarImg: img.avatarImg,
      },

      frontImgSrc: img.frontImg,
      frontImgErr: null,
      frontImgProcess: 0,
      frontImgClose: false,

      backImgSrc: img.backImg,
      backImgErr: null,
      backImgProcess: 0,
      backImgClose: false,

      avatarImgSrc: img.avatarImg,
      avatarImgErr: null,
      avatarImgProcess: 0,
      avatarImgClose: false,

      avatar2ImgSrc: img.avatarImg,
      avatar2ImgErr: null,
      avatar2ImgProcess: 0,
      avatar2ImgClose: false,

      aliSrc: img.aliImg,
      aliErr: null,
      aliProcess: 0,
      aliClose: false,


      selectFile: {
        frontImg: {
          name: 'front',
          uploadUrl: '/gridboot5g/realnameauth/uploadfront',
          original: null,
          compress: null,
        },
        backImg: {
          name: 'back',
          uploadUrl: '/gridboot5g/realnameauth/uploadback',
          original: null,
          compress: null,
        },
        avatarImg: {
          name: 'avatar',
          uploadUrl: '/gridboot5g/realnameauth/uploadavatar',
          original: null,
          compress: null,
        },
        avatar2Img: {
          name: 'avatar',
          uploadUrl: '/gridboot5g/realnameauth/uploadavatar',
          original: null,
          compress: null,
        },
      },
      previewSrc: '',

      avatarInit: false,
      avatarComplete: false, // 是否检测完成
      faceBase64: [],
      RNAT: 1,
      avatarCount: 1,
      options: {},
      formParams: {},
      bizCodes: ['BIZ_VPMN_BATCH_ACTIVATION', 'BIZ_FGB_AGENTUSER_NEW'],
      bizCodeMap: {
        'BIZ_VPMN_BATCH_ACTIVATION': '集团预开激活实名认证',
        'BIZ_FGB_AGENTUSER_NEW': '代办人实名认证',
      }
    }
  },
  components: {
    FaceInstant,
  },
  computed: {
    ...mapGetters({
      stepInfo: 'getStepInfo',
      custInfo: 'getCustInfo',
      userInfo: 'getUserInfo'
    })
  },
  watch: {
    params: {
      handler(newVal, oldVal) {
        if (newVal.front !== '' && newVal.back !== '' && (newVal.avatar !== '' || newVal.ali !== '')) {
          const vm = this
          let formParams = JSON.parse(JSON.stringify(newVal))
          vm.imgBase64.frontImg = formParams.front
          vm.imgBase64.backImg = formParams.back
          vm.imgBase64.avatarImg = formParams.avatar
          vm.frontImgSrc = formParams.front
          vm.backImgSrc = formParams.back
          vm.avatarImgSrc = formParams.avatar
          vm.selectFile.frontImg.original = 'original'
          vm.selectFile.backImg.original = 'original'
          vm.selectFile.avatarImg.original ='original'
        }

      },
      immediate: true,
      deep: true,
    }
  },
  mounted() {
    this.REAL_NAME_RELOAD_AUTH_TYPE()
    this.loadAliRedirect()
    this.getResultAlicert()
  },
  methods: {
    // 重新认证
    onReloadAliAuth() {
      this['aliProcess'] = 0
      this['aliClose'] = false
      this['aliErr'] = ''
    },
    // 删除照片
    onDeleteAvatar() {
      this.onDeleteImg('avatarImg')
      this.onDeleteImg('avatar2Img')
      this['avatarImgSrc'] = this.imgBase64['avatarImg']
      this['avatar2ImgSrc'] = this.imgBase64['avatarImg']
    },
    onDeleteImg(imgRef) {
      this[imgRef + 'Src'] = this.imgBase64[imgRef]
      this[imgRef + 'Err'] = null
      this[imgRef + 'Close'] = false

      const fileInfo = this.selectFile[imgRef]
      fileInfo.original = null
      fileInfo.compress = null
      this.$emit('change', {
        name: fileInfo.name,
        url: ''
      })
    },
    // 打开相机或相册
    onOpenCamera(imgRef) {
      const vm = this
      console.log('111', imgRef)
      if((imgRef == 'avatarImg' || imgRef == 'avatar2Img') && this.RNAT != 1) {
        this.avatarInit = true
        this.avatarComplete = false
        return;
      }
      // 如果是回显的图片，直接打开预览
      console.log('vm.selectFile[imgRef].original',vm.selectFile[imgRef].original)
      if (vm.selectFile[imgRef].original === 'original') {
        uni.previewImage({
          current: 0,
          urls: [vm.imgBase64[imgRef]]
        })
        return
      }
      // 如果已经选择过照片，直接打开预览
      if (vm.selectFile[imgRef].original) {
        img.blobToBase64(vm.selectFile[imgRef].compress, (base64) => {
          uni.previewImage({
            current: 0,
            urls: [base64]
          })
        })
        return
      }

      // 调用Uni拍照
      vm.onUniCamera(imgRef)
    },
    // 拍照
    onUniCamera(imgRef) {
      const vm = this
      uni.chooseImage({
        count: 1, // 只能选取一张
        sizeType: ['original', 'compressed'], // original【原图】、compressed【压缩图】
        sourceType: ['camera'],   // album【相册选图】、camera【相机拍照】
        success: async function (res) {
          console.log('====== res =======')
          console.log(res)
          console.log(res.tempFiles[0].size)
          if (res.tempFilePaths.length > 0) {
            if (res.tempFiles[0].size < (80 * 1024)) {
              uni.showToast({
                title: '拍照的图片大小需要大于80kb，请重新拍照！',
                icon: 'none'
              })
              return
            }
            // vm.$refs[imgRef].src = res.tempFilePaths[0]
            vm.selectFile[imgRef].original = res.tempFiles[0]
            const tempFile = res.tempFiles[0]
            // 先调整大小
            const cutImgBlob = await compressImg(res.tempFiles[0])
            const cutNewFile = new File([cutImgBlob], res.tempFiles[0].name, { type: res.tempFiles[0].type })
            console.log(cutNewFile)
            vm.selectFile[imgRef].compress = cutNewFile
            console.log('vm.selectFile[imgRef]', vm.selectFile[imgRef])
            // 调整大小后，上传图片。水印放在上传图片的接口实现，加了水印后再次压缩
            vm.onUploadFile(imgRef, res.tempFilePaths[0])
          } else {
            vm.$refs[imgRef].src = vm.imgBase64[imgRef]
          }
        },
        fail: function (err) {
          console.log('onOpenCamera fail', err)
          vm.$refs[imgRef].src = vm.imgBase64[imgRef]
          uni.showToast({
            title: '图片获取失败！',
            icon: 'none'
          })
        }
      });
    },
    onUploadFile(imgRef, img) {
      const vm = this
      // 获取文件信息
      const fileInfo = vm.selectFile[imgRef]
      // 获取上传链接
      const uploadUrl = process.env.BASE_URL + fileInfo.uploadUrl
      // 获取token
      let accessToken = uni.getStorageSync('userToken') || (window.android && window.android.sendToken())
      // 上传图片
      vm[imgRef + 'Src'] = img
      vm[imgRef + 'Err'] = '上传中'
      const task = uni.uploadFile({
        url: uploadUrl,
        name: fileInfo.name,
        filePath: window.URL.createObjectURL(fileInfo.compress),
        header: {
          'AccessToken': accessToken
        },
        timeout: 30000, // 30秒没有响应，自动关闭
        success: (res) => {
          console.log(res)
          if (res.data) {
            const data = JSON.parse(res.data)
            if (data.responseBody && (data.responseBody.frontUrl || data.responseBody.backUrl || data.responseBody.avatarUrl)) {
              vm.$emit('change', {
                name: fileInfo.name,
                url: data.responseBody.frontUrl || data.responseBody.backUrl || data.responseBody.avatarUrl
              })
              vm[imgRef + 'Process'] = 100
              vm[imgRef + 'Err'] = null
              vm[imgRef + 'Close'] = true
              console.log(data.responseBody.frontpicImgStr)
            } else {
              vm[imgRef + 'Process'] = 0
              vm[imgRef + 'Src'] = null
              vm.selectFile[imgRef].original = null
              vm.selectFile[imgRef].compress = null
              vm[imgRef + 'Err'] = data.returnInfo.message || '上传失败！'
            }
          }
        },
        fail: (err) => {
          console.log(err)
          vm[imgRef + 'Process'] = 0
          vm[imgRef + 'Src'] = null
          vm.selectFile[imgRef].original = null
          vm.selectFile[imgRef].compress = null
          vm[imgRef + 'Err'] = '上传图片失败，请重试！'

          // vm.onDeleteImg(imgRef)
        },
        complete: (res) => {
          console.log(res)
          uni.hideLoading()
        }
      })

      console.log(task)
      task.onProgressUpdate((res) => {
        console.log(res)

        this[imgRef + 'Process'] = res.progress == 100 ? 99 : res.progress
        console.log('上传进度' + res.progress);
        console.log('已经上传的数据长度' + res.totalBytesSent);
        console.log('预期需要上传的数据总长度' + res.totalBytesExpectedToSend);
      });
    },

    async REAL_NAME_RELOAD_AUTH_TYPE() {
      console.log('REAL_NAME_RELOAD_AUTH_TYPE====>',this.busiType)
      let mcode = this.busiType == 'ALI_TYPE' ? 'ALI_TYPE' : 'COMMON_TYPE'

      const {responseBody: {paramdata: [res]}} = await this.$http.post(this.$apis.getdata, {
        gcode: 'REAL_NAME_RELOAD_AUTH_TYPE',
        mcode: mcode
      })
      console.log('REAL_NAME_AUTH_TYPE', res);
      this.RNAT = res.data

      if(res.data == '3') {
        this.avatarCount = 2
      }
    },

    async uploadScreenShot(name, file) {
      const vm = this
      // 获取文件信息
      const fileInfo = vm.selectFile['avatarImg']
      // 获取上传链接
      const uploadUrl = process.env.BASE_URL + fileInfo.uploadUrl
      const imgRef = name + 'Img'

      const cutImgBlob = await compressImg(file)
      const cutNewFile = new File([cutImgBlob], imgRef, { type: 'image/jpeg' })
      const uri = window.URL.createObjectURL(cutNewFile)
      // 上传图片
      vm[imgRef + 'Src'] = uri
      vm[imgRef + 'Err'] = '上传中'
      const task = uni.uploadFile({
        url: uploadUrl,
        name: 'avatar',
        filePath: uri,
        header: {
          'AccessToken': uni.getStorageSync('userToken')
        },
        timeout: 30000, // 30秒没有响应，自动关闭
        success: (res) => {
          console.log(res)
          if (res.data) {
            const data = JSON.parse(res.data)
            if (data.responseBody && data.responseBody.avatarUrl) {
              vm.$emit('change', {
                name: name,
                url: data.responseBody.avatarUrl
              })
              vm[imgRef + 'Process'] = 100
              vm[imgRef + 'Err'] = null
              vm[imgRef + 'Close'] = true

            } else {
              vm[imgRef + 'Process'] = 0
              vm[imgRef + 'Src'] = null
              vm.selectFile[imgRef].original = null
              vm.selectFile[imgRef].compress = null
              vm[imgRef + 'Err'] = data.returnInfo.message || '上传失败！'
            }
          }
        },
        fail: (err) => {
          console.log(err)
          vm[imgRef + 'Process'] = 0
          vm[imgRef + 'Src'] = null
          vm.selectFile[imgRef].original = null
          vm.selectFile[imgRef].compress = null
          vm[imgRef + 'Err'] = '上传图片失败，请重试！'

          // vm.onDeleteImg(imgRef)
        },
        complete: (res) => {
          console.log(res)
          uni.hideLoading()
        }
      })

      console.log(task)
      task.onProgressUpdate((res) => {
        console.log(res)

        this[imgRef + 'Process'] = res.progress == 100 ? 99 : res.progress
        console.log('上传进度' + res.progress);
        console.log('已经上传的数据长度' + res.totalBytesSent);
        console.log('预期需要上传的数据总长度' + res.totalBytesExpectedToSend);
      });
    },
    avatarSuccess(faceBase64) {
      console.log(faceBase64)
      this.avatarComplete = true
      this.avatarInit = false
      this.faceBase64 = faceBase64

      if(this.RNAT == 2) {
        this.uploadScreenShot('avatar', faceBase64[0])
        return
      }

      if(this.RNAT == 3) {
        this.uploadScreenShot('avatar', faceBase64[0])
        setTimeout(() => {
          this.uploadScreenShot('avatar2', faceBase64[1])
        }, 50)
        return
      }
    },
    avatarCancel() {
      this.avatarComplete = false
      this.avatarInit = false
      this.faceBase64 = []
    },
    async onAliLivenessDetection() {
      const vm = this
      if (vm.bizCodes.includes(vm.bizCode) && (!vm.formParams || Object.keys(vm.formParams).length === 0)) {
        let msg = `${vm.bizCodeMap[vm.bizCode]}，请先读取身份证`
        uni.showToast({
          title: msg,
          icon: 'none'
        });
        return
      }

      const metaInfo = window.getMetaInfo()
      console.log('onAliLivenessDetection===>ali-metaInfo====>', metaInfo);


      // let callBackUrl = process.env.SERVICE_URL + `#/pages/FifthG/aliRedirect?custid=${vm.custInfo.custid}&iden_type=${'1'}&iden_nr=${vm.formParams.license}&poid=${vm.stepInfo?.poid}&orderId=${vm.formParams.orderId}`
      // let callBackUrl = `http://localhost:8083/#/pages/FifthG/aliRedirect?custid=${vm.custInfo.custid}&iden_type=${'1'}&iden_nr=${vm.formParams.license}&poid=${vm.stepInfo?.poid}&orderId=${vm.formParams.orderId}`

      let iden_type = ''
      let iden_nr = ''
      let return_url = ''
      let orderId = ''
      let poid = vm.stepInfo?.poid || ''

      if (!vm.formParams && Object.keys(vm.formParams).length > 0) {
        iden_type = vm.formParams.isNewForeginId === 'Y' ? 'w' : '1'
        iden_nr = vm.formParams.license
        orderId = vm.formParams.orderId
        return_url = process.env.SERVICE_URL + `#/pages/FifthG/aliRedirect?custid=${vm.custInfo.custid}&iden_type=${iden_type}&iden_nr=${vm.formParams.license}&poid=${poid}&orderId=${vm.formParams.orderId}`
      } else {
        iden_type = vm.custInfo.cardtype
        iden_nr = vm.custInfo.cardno
        return_url = process.env.SERVICE_URL + `#/pages/FifthG/aliRedirect?custid=${vm.custInfo.custid}&iden_type=${vm.custInfo.cardtype}&iden_nr=${vm.custInfo.cardno}&poid=${poid}`
      }

      console.log('onAliLivenessDetection====>callBackUrl===>', return_url);
      let params = {
        custid: vm.custInfo.custid,
        meta_info: JSON.stringify(metaInfo),
        iden_type: iden_type, //TODO 阿里活体检测国网暂时只支持身份证，接口会提示
        iden_nr: iden_nr,
        return_url: return_url,
        poid: poid,
        orderId: orderId
      }

      console.log('onAliLivenessDetection====>params===>', params);

      vm.$http.post(vm.$apis.applyAlicert, params).then(resp => {
        console.log('onAliLivenessDetection====>applyAlicert===>', resp.responseBody)
        // window.location.href = resp.responseBody.certify_url
        vm.$emit('getAliUrl', resp.responseBody.certify_url)
      })
    },
    getResultAlicert() {
      const vm = this
      let urlObj = util.dataProcess.getGWUrlData() || {};
      console.log('getResultAlicert==========>urlObj:', urlObj)

      let certifyId = urlObj.certifyId

      console.log('getResultAlicert==========>certifyId:', certifyId)
      if (certifyId) {
        vm['aliErr'] = '查询检测结果...'
        vm['aliProcess'] = 50
        vm['aliClose'] = false
        console.log('getResultAlicert==========>certifyId:', certifyId)

        vm.$http.post(vm.$apis.resultAlicert, {
          certify_id: certifyId
        }).then(resp => {
          console.log('getResultAlicert===》resultAlicert====>', resp.responseBody)

          if (resp.responseBody.passed === 'T') {
            vm.$emit('change', {
              name: 'ali',
              url: JSON.stringify({
                certify_id: resp.responseBody.certify_id,
                liveness_biz_no: resp.responseBody.liveness_biz_no,
              })
            })

            vm['aliProcess'] = 100
            vm['aliClose'] = true
            vm['aliErr'] = ''
          } else {
            vm['aliProcess'] = 0
            vm['aliClose'] = true
            vm['aliErr'] = '活体检测失败'
          }

        }).catch(err => {
          vm['aliProcess'] = 0
          vm['aliClose'] = true
          vm['aliErr'] = '活体检测失败'
        })

      }

    },
    loadAliRedirect() {
      const vm = this
      uni.$on("aliRedirect", (data) => {
        console.log('aliRedirect========>getResultAlicert====>', data)
        vm['aliErr'] = data.aliErr
        vm['aliProcess'] = data.aliProcess
        vm['aliClose'] = data.aliClose
        vm.$emit('getAliUrl',data.aliUrl)
        vm.$emit('change', {
          name: 'ali',
          url: data.url
        })
      });
    },
    setFormParams(data) {
      console.log("verifiedUpload====>setformParams", data)
      this.formParams = data
    },
  }
}
</script>

<style lang="scss" scoped>
.verified-upload-box {
  border-bottom: 1px solid #eeeeee;
  padding-bottom: 10px;
}
.verified-upload-body {
  display: flex;
  justify-content: space-between;
  .img-upload-box {
    position: relative;
    width: 310rpx;
    border: 1px solid #eeeeee;
    margin: 5px 0;
    text-align: center;

    .img-box {
      position: relative;
      width: 310rpx;
      height: 168rpx;

      .progress {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 10rpx;
        background-color: #eee;
        z-index: 2;

        .line {
          height: 10rpx;
          background-color: #0099FF;
          transition: width ease 1s;
        }
      }

      .err {
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        width: 100%;
        height: 100%;
        padding: 40rpx;
        background-color: rgba(0, 0, 0, .4);
        color: #fff;
        //z-index: 10;
        box-sizing: border-box;
      }

      .img {
        height: 100%;
        background-size: 310rpx 168rpx;
        background-repeat: no-repeat;
        overflow: hidden;
      }
    }

    .img-desc {
      border-top: 1rpx solid #eeeeee;
      line-height: 60rpx;
    }

    .delete-img-btn {
      position: absolute;
      top: 0;
      right: 3px;
      color: #0099FF;
      //z-index: 10;
    }
  }

}

.faceInstant {
  position: absolute;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 99999;
}

.success-class {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(31, 143, 247, 0.1);
  height: 100%;

  .img-success {
    height: 100rpx;
    width: 100rpx;
    background-size: 100rpx 100rpx;
    background-image: url(../../../../../static/fifthg/imgs/live-success.png);
    background-repeat: no-repeat;
    overflow: hidden;
    margin-top: 20rpx;
  }

  .text-success {
    color: #2B71FF;
    font-size: 24rpx;
    height: 2em;
    line-height: 2em;
  }
}
</style>
