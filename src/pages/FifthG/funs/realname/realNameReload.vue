<template>
  <c-layout title="实名认证">
    <view class="business-list-box">
      <view style="margin-bottom: 20rpx;">
        <c-tabs v-if="tabs.length > 0" :tabs="tabs" :tab-click="onTabClick"></c-tabs>
      </view>
      <ali-verified v-if="aliVerified" :options="options"></ali-verified>
      <common-verified v-if="ordVerified" :options="options"></common-verified>
    </view>
  </c-layout>
</template>

<script>
import CTabs from "@/pages/FifthG/components/list/CTabs";
import CLayout from "@/pages/FifthG/components/layout/CLayout";
import { mapState, mapGetters, mapActions } from 'vuex';
import aliVerified from "./components/aliVerified.vue";
import commonVerified from "./components/commonVerified.vue";
export default {
  name: 'realNameReload',
  data() {
    return {
      tabs: [],
      options: {},
      ordVerified: false,
      aliVerified: false,
    };
  },
  components: {
    aliVerified,
    commonVerified,
    CTabs,
    CLayout,
  },
  onLoad(options) {
    this.options = options;
    this.REAL_NAME_RELOAD_AUTH_TYPE()
  },
  computed: {
    ...mapGetters({
      custInfo: 'getCustInfo',
      userInfo: 'getUserInfo'
    })
  },
  methods: {
    onTabClick(item) {
      console.log('****************************',item)
      if (item.value == 0) {
        this.ordVerified = false
        this.aliVerified = true
      } else {
        this.ordVerified = true
        this.aliVerified = false
      }
    },
    async REAL_NAME_RELOAD_AUTH_TYPE() {
      this.tabs = []
      const {responseBody: {paramdata: [res]}} = await this.$http.post(this.$apis.getdata, {
        gcode: 'REAL_NAME_RELOAD_AUTH_TYPE',
        mcode: 'TAB_TYPE'
      })
      console.log('REAL_NAME_RELOAD_AUTH_TYPE', res);
      let type = res
      if (type && type.data) {
        if (type.data.indexOf('2') > -1) {
          this.tabs.push(
            {
              name: "阿里活体认证",
              value: 0
            }
          )
        }

        if (type.data.indexOf('1') > -1) {
          this.tabs.push(
            {
              name:  "拍照认证",
              value: 1
            }
          )
        }

      }
    },
  }
};
</script>

<style lang="scss" scoped>

</style>
