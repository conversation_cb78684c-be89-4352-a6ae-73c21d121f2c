<template>
<c-layout title="个人中心" :show-bot-bar="true">
  <view class="personal-box">
    <view class="user-info-box">
      <view class="user-img-box">
        <image class="user-img" src="@/static/main/imgs/ic_person_avatar.png"/>
      </view>
      <view class="user-info-item-box">
        <view class="user-name-box">
          <view class="user-name">{{userInfo.name}}</view>
          <view v-if="userInfo.rolename" class="user-post">{{ userInfo.rolename}}</view>
        </view>
        <view class="user-dept-box">
          <text class="user-dept">{{userInfo.deptname}}</text>

          <picker style="flex: 1" ref="selectDept" @change="bindSystemChange" v-if="departments" :value="systemIndex" :range="departments" :range-key="'name'">
            <i class="user-post-dept">切换部门</i>
<!--            <i class="change-dept-btn iconfont icon-qiehuan"></i>-->
          </picker>
        </view>
        <view class="user-grid-box">
          <view class="user-grid">管理网格：{{ grids }}</view>
        </view>

      </view>
    </view>
    <view class="apps-box">
      <view class="app-box" v-for="(app,index) in apps" :key="index" @click="onToCountPage(app)">
        <view class="app-icon-box" :style="{backgroundColor: app.color}">
          <i :class="`app-icon iconfont ${app.icon}`"></i>
        </view>
        <view class="app-name-box">
          {{ app.name }}
        </view>
      </view>
    </view>
    <view class="logout-btn" @click="onLogout">
      退出登录
    </view>
  </view>
</c-layout>
</template>

<script>
import CLayout from "../../components/layout/CLayout";
import {mapActions, mapGetters, mapMutations} from "vuex";
export default {
  name: "Personal",
  components: {CLayout},
  computed: {
    ...mapGetters({
      userInfo: 'getUserInfo'
    })
  },
  data() {
    return {
      apps: [
        // {
        //   icon: 'icon-zhinengbaobiao',
        //   name: '业务量统计',
        //   path: '/pages/FifthG/funs/dashboard/business-count/index',
        //   color: '#FFB800'
        // }
      ],
      departments: [],
      departmentSelect: null,
      department: {},
      systemIndex: 0, // 部门id索引
      grids: null,
      loginname: null,
      password: null,
      loginData: null,
    }
  },
	created() {
	  this.onInitMenu()
	},
  mounted() {
    this.loadDepartment()
    this.loadGrid()
  },
  methods: {
    ...mapMutations({
			setMenus: "setMenus",
      // setUserInfo: 'setUserInfo',
    }),
    ...mapActions({
      queryUserInfo: 'queryUserInfo',
      custLeave: 'custLeave',
      fetchNavs: 'fetchNavs',
      setUserInfo: "setUserInfo",
    }),
    ...mapGetters({
      getCurrentNav: "getCurrentNav",
    }),
    async loadGrid () {
      this.$http.post(this.$apis.queryGridManagerByOp,{ operid: this.userInfo.operid}, {isAutoLoading: false}).then(result => {
        console.log('result',result)
        if (result.responseBody.res && result.responseBody.res.length > 0) {

          if (this.userInfo.areaid != 45) {
            result.responseBody.res = result.responseBody.res.filter(i => i.countyid == this.userInfo.areaid)
          }
          this.grids = result.responseBody.res.map(i => i.gridname).join(',') || '无'
          console.log('this.grids',this.grids)
        }else {
          this.grids = '无'
        }
      })
    },
    onToCountPage(item) {
      if (item.path) {
        this.$rauter.navigateTo({
          url: item.path,
          data: {}
        });
      } else {
        uni.showToast({
          title: '模块正在开发中...',
          icon: 'none'
        })
      }
    },

		// 初始化菜单
		onInitMenu() {
		  const vm = this
		  vm.$http.post(vm.$apis.queryMenu,{ sysid: 26 }, {isAutoLoading: false})
		      .then((resp) => {
		        const menuCompList = resp.responseBody.menuCompList
		        vm.setMenus({data:menuCompList})
		        const menuList = JSON.parse(JSON.stringify(vm.apps))
		        menuCompList.forEach((menuGroup) => {
		          if (menuGroup.menuList && menuGroup.menuList.length > 0) {
								menuGroup.menuList.forEach(menu => {
									menuList.push({
										icon: menu.iconid,
										name: menu.name,
										path: menu.linkurl,
										color: '#FFB800'
									})
								})
		          }
		        })
						vm.apps = menuList

            console.log('apps',this.apps)
		      })
		      .catch((error) => {
		        uni.showToast({
		          title: error.message,
		          icon: 'none'
		        })
		      })
		},

    // 选择部门id
    bindSystemChange(e) {
      console.log('getCurrentNa=====>1',this.getCurrentNav)
      const UA = window.navigator.userAgent.toLowerCase();
      this.systemIndex = e.target.value;
      this.department = this.departments[this.systemIndex];
      console.log('this.department', this.department)
      if (!this.department) {
        this.$util.feeback.showToast({
          title: "部门不能为空",
        })
        return
      }

      let parmams = {
        deptid: this.department.id,
        loginname: this.userInfo.loginname,
        accesskey: process.env.LOGIN_ACCESS_KEY,
        md5: true
      }

      this.$http.post(this.$apis.changeDept, parmams)
        .then(resp => {
          console.log('getCurrentNa=====>2',this.getCurrentNav)
          console.log('changeDept=====>',resp.responseBody)
          this.loginData = resp.responseBody

          this.$util.storage.setStorageSync('userToken', this.loginData.token); // 把登录的token信息缓存到H5中，重要
          console.log('userInfo',	uni.getStorageSync('userInfo'))

          // this.queryUserInfo(); // 查询登录用户信息，并缓存到store里去重要

          this.setUserInfo(this.loginData.loginInfo) // 防止getLoginInfo接口调用失败

          console.log('userInfo',	uni.getStorageSync('userInfo'))
          this.custLeave();// 清除定位客户的信息
          sessionStorage.removeItem('recentFunkList') //清除常用功能

          this.loadGrid() // 切换部门后重新查询网格
          this.fetchNavs(true); // 刷新底部菜单

          if (this.loginData && this.loginData.token && this.loginData.token !== "") {
            if(UA.indexOf('wxwork') > -1) {
              const appid = 'wxbbb07a57efc519cb'
              const redirect_uri = encodeURIComponent(process.env.SERVICE_URL + '#/pages/FifthG/funs/personal/index')
              const agentid = '1000047'
              window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${redirect_uri}&response_type=code&scope=snsapi_base&state=account&agentid=${agentid}#wechat_redirect`
              return
            }
          }

        }).catch(error => {
        console.log('error=====>',error)
        this.$util.feeback.showToast({
          title: "切换部门失败",
        })
      })
    },
    loadDepartment() {
      this.departments = null
      this.departmentSelect = null
      if (this.userInfo.loginname) {
        let queryParam = {
          "loginname": this.userInfo.loginname
        }
        this.$http.postQueryDepartment("queryDepartment", queryParam).then((res) => {
          this.departments = res.responseBody;
          if (this.departments.length > 0) {
            this.department = this.departments[0]
          }
        });
      }
    },
    // 退出登录
    onLogout() {
      // 先提示
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            this.$http.post(this.$apis.loginOut)
            .then(resp => {
              console.log('querySearchRecord=====>',resp)
              // 清除所有缓存
              this.setUserInfo({})
              sessionStorage.clear()
              localStorage.clear()
              // 跳转到登录页
              this.$rauter.navigateTo({
                url: '/',
                data: {}
              });
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.personal-box {
  .user-info-box {
    width: 100vw;
    background-color: #0084FF;
    height: 10em;
    display: flex;
    height: auto;
    .user-img-box {
      .user-img {
        margin: 1em;
        width: 6em;
        height: 6em;
        border-radius: 50%;
      }
    }
    .user-info-item-box {
      //transform: translateY(1.5em);
      margin-top: 1.5em;
      width: 100%;
      .user-name-box {
        display: flex;
        .user-name {
          font-size: 1.4em;
          font-weight: bold;
          color: #fff;
        }
        .user-post {
          font-size: 0.8em;
          color: #fff;
          border: 1px solid #fff;
          border-radius: 1em;
          padding: 0 1em;
          font-weight: bold;
          height: 2em;
          line-height: 2em;
          margin-left: 1em;
          transform: translateY(0.6em);
        }
      }
      .user-dept-box {
        display: flex;
        .user-dept {
          color: #EEEEEE;
          flex: 1;
        }
        .change-dept-btn {
          color: #EEEEEE;
          margin-left: 1em;
        }
      }

      .user-grid-box {
        display: flex;
        .user-grid {
          color: #EEEEEE;
        }
      }
    }
  }
  .apps-box {
    display: flex;
    padding: 0.5em;
    margin: 1em;
    background-color: #fff;
    border-radius: 0.5em;
    justify-content: flex-start;
    flex-wrap: wrap;
    .app-box {
      margin: 0.6em 0.2em;
      width: calc((100% - 1.8em) / 4);
      .app-icon-box {
        margin: 0 auto;
        width: 3em;
        height: 3em;
        background-color: #0084FF;
        border-radius: 0.5em;
        text-align: center;
        line-height: 3em;
        color: #FFFFFF;
        .app-icon {
          font-size: 2em;
        }
      }
      .app-name-box {
        font-size: 0.8em;
        margin-top: 0.2em;
        text-align: center;
      }
    }
  }
  .logout-btn {
    margin: 1em;
    height: 3em;
    line-height: 3em;
    background-color: #fff;
    border-radius: 0.5em;
    border: 1px solid #0084FF;
    color: #0084FF;
    text-align: center;
  }
}

.user-post-dept {
  font-size: 0.8em;
  color: #fff;
  border: 1px solid #fff;
  border-radius: 1em;
  padding: 4px 1em;
  font-weight: bold;
  height: 2em;
  line-height: 2em;
  margin-left: 1em;
  transform: translateY(0.6em);
}



</style>
