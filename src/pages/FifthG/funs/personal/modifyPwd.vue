<template>
  <view class="content">
    <view class="wrap">

      <view class="form">
        <view class="card sim">
          <view class="field">
            <view class="main">
              <view class="perfix"><text class="red">*</text> 原密码</view>
              <view class="body">
                <input class="input" v-model="pwd" type="text" placeholder="请输入原密码" />
              </view>
            </view>
          </view>

          <view class="field">
            <view class="main">
              <view class="perfix"><text class="red">*</text> 新密码</view>
              <view class="body">
                <input class="input" maxlength="20" v-model="newpwd" type="password" placeholder="请输入新密码" />
              </view>
            </view>
          </view>

          <view class="field">
            <view class="main">
              <view class="perfix"><text class="red">*</text> 确认密码</view>
              <view class="body">
                <input class="input" maxlength="20" v-model="cpwd" type="password" placeholder="请输入确认密码" />
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="tips">密码长度为8-20个字符，必须包含：数字、大写字母、小写字母</view>

      <view class="btn-accept" @click="confirm">提交</view>
    </view>
  </view>
</template>

<script>
import {mapGetters} from "vuex";

export default {
  name: "modifyPwd",
  data() {
    return {
      newpwd: '',
      pwd: '',
      cpwd: '',
    };
  },
  computed: {
    ...mapGetters({
      userInfo: "getUserInfo"
    })
  },
  methods: {
    confirm() {
      if(!this.newpwd) {
        this.$util.feeback.showToast({title : '请输入新密码！'})
        return
      }

      if(!this.cpwd) {
        this.$util.feeback.showToast({title : '请输入确认密码！'})
        return
      }

      if(this.newpwd != this.cpwd) {
        this.$util.feeback.showToast({title : '新密码不一致，请重新输入！'})
        return
      }

      this.$http.post(this.$apis.changePassword, {
        "newpwd": this.newpwd,
        "pwd": this.pwd,
        "loginname": this.userInfo.loginname,
      }).then(resp => {
        this.$util.feeback.showToast({title : '修改密码成功'})
        setTimeout(() => {
          this.$rauter.navigateBack({
            delta: 1
          })
        }, 500)

      })


    },
  }
}
</script>

<style lang='scss'>
.content {
  height: 100%;
  overflow: auto;

  .wrap {
    padding: 20rpx;

    .form {
      padding-top: 20rpx;
    }
  }
}

.card {
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  padding: 26rpx 20rpx;
  background-color: #fff;
}

.card.sim {
  display: flex;
  flex-direction: column;

  .field {
    margin-top: 20rpx;
    border-top: solid 1px #EFEFEF;
    padding-top: 20rpx;

    &:nth-child(1) {
      margin-top: 0;
      border-top: none;
      padding-top: 0;
    }

    .main {
      flex: 1;
      display: flex;
      align-items: center;
      font-size: 28rpx;
      justify-content: space-between;

      .perfix {
        position: relative;
        padding-right: 22rpx;

        .red {
          position: absolute;
          top: 4rpx;
          right: 0;
          font-size: 42rpx;
          color: #f00;
        }

        .sub {
          padding-left: 6rpx;
          font-weight: normal;
          color: #F9A63F;
        }
      }

      .body {
        flex: 1;
        padding: 0 20rpx;

        .text {
          line-height: 2em;
          color: #333333;

          &.placeholder {
            font-weight: normal;
            color: #999;
          }
        }

        .input {
          display: block;
          width: 100%;
          border: none;
          height: 2em;
          line-height: 2em;
        }

        .radio-group {
          display: flex;
          align-items: center;

          .item {
            flex: 1;
            display: flex;
            align-items: center;

            .status {
              position: relative;
              width: 26rpx;
              height: 26rpx;
              margin-right: 10rpx;
              border: solid 1px #212121;
              border-radius: 50%;

              &.checked {
                border-color: #1F8FF7;
                background-color: #1F8FF7;
                background-image: url(../../../../static/fifthg/ioa/icon-check.png);
                background-position: center center;
                background-repeat: no-repeat;
                background-size: 20rpx 20rpx;
              }
            }
          }
        }
      }

      .suffix {
        align-self: stretch;

        .btn {
          border: solid 1px #1F8FF7;
          border-radius: 25rpx;
          font-weight: normal;
          color: #1F8FF7;
          text-align: center;
          width: 130rpx;
          height: 45rpx;
          line-height: 45rpx;
        }

        .icon-arw {
          width: 20rpx;
          height: 100%;
          background-image: url(../../../../static/fifthg/ioa/icon-arw.png);
          background-position: center center;
          background-repeat: no-repeat;
          background-size: 20rpx 20rpx;
        }

        .time-img {
          width: 50rpx;
          height: 100%;
          background-image: url(../../../../static/fifthg/2023/time-icon.png);
          background-position: center center;
          background-repeat: no-repeat;
          background-size: 50rpx 46rpx;
        }

        .delete-img {
          width: 20rpx;
          height: 100%;
          background-image: url(../../../../static/fifthg/2023/delete-icon.png);
          background-position: center center;
          background-repeat: no-repeat;
          background-size: 20rpx 20rpx;
        }
      }
    }

    .extra {
      padding-top: 10rpx;
      font-size: 28rpx;
      color: #999;
    }
  }

  .check-group {

    .item {
      display: flex;
      align-items: center;
      margin: 10rpx 0;
      border-radius: 10rpx;
      padding: 20rpx;
      background-color: #F0F8FF;

      .checkbox {
        display: flex;
        align-items: center;
      }

      .status {
        position: relative;
        width: 26rpx;
        height: 26rpx;
        margin-right: 10rpx;
        border: solid 1px #0E67B9;
        border-radius: 50%;

        &.checked {
          border-color: #1F8FF7;
          background-color: #1F8FF7;
          background-image: url(../../../../static/fifthg/ioa/icon-check.png);
          background-position: center center;
          background-repeat: no-repeat;
          background-size: 20rpx 20rpx;
        }
      }

      .label {
        color: #0E67B9;
      }
    }
  }

}

.tips {
  color: #969799;
  font-size: 12px;
}

.btn-accept{
  margin-bottom: 180rpx;
  margin-top: 90rpx;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  border: 1px solid #1F8FF7;
  background: #1F8FF7;
  color: #FFFFFF;
  text-align: center;
  font-size: 32rpx;
}
</style>
