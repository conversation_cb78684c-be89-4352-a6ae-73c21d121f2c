<template>
  <c-layout title="业务量统计">
      <div style="padding-bottom: 1em">
        <dashboard-top-query :handle-refresh="handleRefresh"></dashboard-top-query>
<!--        <account-opening-count ref="accountOpeningCount"></account-opening-count>-->
        <handling-volume-count ref="handlingVolumeCount"></handling-volume-count>
<!--        <handling-volume-top ref="handlingVolumeTop"></handling-volume-top>-->
      </div>
  </c-layout>
</template>

<script>
import CLayout from "../../../components/layout/CLayout";
import CNoData from "../../../components/list/CNoData";
import DashboardTopQuery from "../components/query/DashboardTopQuery";
import AccountOpeningCount from "../components/tables/AccountOpeningCount";
import HandlingVolumeCount from "../components/charts/HandlingVolumeCount";
import HandlingVolumeTop from "../components/charts/HandlingVolumeTop";

export default {
  name: "BusinessCount",
  components: {
    HandlingVolumeTop, HandlingVolumeCount, AccountOpeningCount, DashboardTopQuery, CNoData, CLayout},
  data() {
    return {

    }
  },
  mounted() {

  },
  methods: {
    handleRefresh(query,userIdInfo) {
      // this.$refs.accountOpeningCount.handleRefresh(query)
      this.$refs.handlingVolumeCount.handleRefresh(query)
      // this.$refs.handlingVolumeTop.handleRefresh(query, userIdInfo)
    }
  }
}
</script>

<style scoped>

</style>
