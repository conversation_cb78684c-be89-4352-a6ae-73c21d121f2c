<template>
  <div>
    <c-query-card c-margin="20rpx">
      <div slot="title-left">
        <picker
            @change="onPickerChange"
            :range="modes"
            range-key="name">
          <span style="font-weight: bold;">{{ currentName }}</span><i class="iconfont icon-qiehuan toggle-mode-btn"></i>
        </picker>
      </div>
      <div slot="title-right">
        <span>您当前排名：第<span style="font-weight: bold;color: #0099FF;">{{ ranking }}</span>名</span>
      </div>
      <div class="handling-volume-count-box">
        <div ref="handlingVolumeChart" class="handling-volume-chart"></div>
      </div>
    </c-query-card>
  </div>
</template>

<script>
import countUtils from "../js/countUtils";
import CQueryCard from "../../../../components/list/CQueryCard";
import {mapGetters} from "vuex";
export default {
  name: "HandlingVolumeTop",
  components: {CQueryCard},
  data() {
    return {
      currentMode: "0", // 0【业务量】、1【开户量】、2【收款金额】
      currentName: '业务量英雄榜', // 0【业务量】、1【开户量】、2【收款金额】
      chartsEl: null,
      ranking: 1,
      modes: [
        {
          name: '业务量英雄榜',
          value: '0'
        },
        {
          name: '收款额英雄榜',
          value: '2'
        },
        {
          name: '开户数英雄榜',
          value: '1'
        }
      ],
      tableData: [],
      query: {},
      userIdInfo: {}
    }
  },
  computed: {
    ...mapGetters({
      userInfo: 'getUserInfo'
    })
  },
  mounted() {
    const vm = this
    vm.onInitChart()
    window.addEventListener("resize", function () {
      vm.chartsEl?vm.chartsEl.resize():console.log('not resize');
    });
  },
  methods: {
    handleRefresh(query, userIdInfo) {
      const vm = this
      this.query = query
      this.userIdInfo = userIdInfo
      vm.$http.post(vm.$apis.queBusiSort, {
        service: vm.$serviceId.QUE_BUSI_SORT,
        params: {
          ...query,
          queryBusType: vm.currentMode,
          currentPage: 1,
          querynum: 100 // 默认查询100条
        }
      }).then(res => {
        if (res.responseBody.data.output && res.responseBody.data.output.result) {
          let tableData = res.responseBody.data.output.result
          // 根据开户数量排序
          tableData.sort((a, b) => {
            return a.itemCount - b.itemCount
          })
          vm.tableData = tableData
        } else {
          vm.tableData = [{
            itemname: '',
            itemCount: 0
          }]
        }
        vm.onInitChart()
      })
    },
    onPickerChange(val) {
      this.currentMode = this.modes[val.detail.value].value
      this.currentName = this.modes[val.detail.value].name
      this.handleRefresh(this.query,this.userIdInfo)
    },
    onGotoDetail() {
      this.$router.push({path: "/pages/FifthG/funs/dashboard/components/tables/HandlingVolumeDetail" });
    },
    onInitChart() {
      let element = this.$refs['handlingVolumeChart']
      let chartsObj = this.$echarts.init(element)
      const option = this.onPackData()
      chartsObj.setOption(option)
      this.chartsEl = chartsObj
    },
    onPackData() {
      // 加载数据
      let data = this.tableData
      // 封装数据
      let xAxisData = []
      let seriesData = [
        {
          barMinHeight: 100,
          itemStyle: {
            color: '#0099FF'
          },
          label: {
            show: true
          },
          data: [],
          type: 'bar'
        }
      ]


      let statisticType = this.query.statisticType + ''
      if (!this.query.statisticType || this.query.statisticType === 'undefined') {
        statisticType = '0'
      }

      // 只保留前5个
      for (let i = 0; i < data.length; i++) {
        if (i < 5) {
          let itemname = data[i].itemname
          for (let j = 0; j < itemname.length; j++) {
            // 每6个子换一次行
            if (j % 5 === 0 && j !== 0) {
              itemname = itemname.substring(0, j) + '\n' + itemname.substring(j)
            }
          }
          xAxisData.push(itemname)
          seriesData[0].data.unshift(Number(data[i].itemCount))
        }

        // statisticType: '', // 统计类型：0【个人】、1【部门】、2【分公司】、3【片区】
        if (statisticType === '0') {
          // 则用工号判断当前排行
          if (this.userIdInfo && this.userIdInfo.operid == data[i].itemid) {
            this.ranking = i + 1
          }
        } else if (statisticType === '1') {
          // 则用部门编码判断当前排行
          if (this.userIdInfo && this.userIdInfo.deptid == data[i].itemid) {
            this.ranking = i + 1
          }
        } else if (statisticType === '2') {
          // 则用分公司编码判断当前排行
          if (this.userIdInfo && this.userIdInfo.companyid == data[i].itemid) {
            this.ranking = i + 1
          }
        } else if (statisticType === '3') {
          // 则用片区编码判断当前排行
          if (this.userIdInfo && this.userIdInfo.areaid == data[i].itemid) {
            this.ranking = i + 1
          }
        }
      }
      return {
        grid: {
          top: 20,
          left: 0,
          bottom: 0,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisTick: {
            alignWithLabel: true,
            interval: 0,
            rotate: -30,
            margin: -15
          },
          axisLabel: {
            interval: 0,
            rotate: 40
          }
        },
        yAxis: {
          type: 'value'
        },
        series: seriesData
      }
    }
  },
}
</script>

<style scoped>
.toggle-mode-btn {
  color: #0099FF;
  margin-left: 0.5em;
}
.handling-volume-chart {
  width: 100%;
  height: 18em;
}
</style>
