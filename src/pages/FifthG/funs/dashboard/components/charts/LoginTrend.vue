<template>
  <div>
    <c-query-card c-margin="20rpx" title="登录趋势">
      <div slot="title-right">
        最近30天累计登录：<span style="color: #0099FF;font-weight: bold;">{{count + ' 次'}}</span>
      </div>
      <div class="login-trend-box">
        <div ref="loginTrendChart" class="login-trend-chart"></div>
      </div>
    </c-query-card>
  </div>
</template>

<script>
import countUtils from "../js/countUtils";
import CQueryCard from "../../../../components/list/CQueryCard";
export default {
  name: "LoginTrend",
  components: {CQueryCard},
  data() {
    return {
      chartsEl: null,
      tableData: [],
      query: {},
      count: 0
    }
  },
  mounted() {
    const vm = this
    vm.onInitChart()
    window.addEventListener("resize", function () {
      vm.chartsEl?vm.chartsEl.resize():console.log('not resize');
    });
  },
  methods: {
    handleRefresh(query) {
      const vm = this
      this.query = query
      vm.$http.post(vm.$apis.countLoginTrend, {
        params: {
          ...query
        }
      }).then(res => {
        if (res.responseBody.data.list) {
          vm.tableData = res.responseBody.data.list
        } else {
          vm.tableData = []
        }
        vm.onInitChart()
      })
      vm.$http.post(vm.$apis.countLastMonthLogin, {
        params: {
          ...query
        }
      }).then(res => {
        if (res.responseBody.data.count) {
          vm.count = res.responseBody.data.count
        } else {
          vm.count = 0
        }
      })

    },
    onInitChart() {
      let element = this.$refs['loginTrendChart']
      let chartsObj = this.$echarts.init(element)
      const option = this.onPackData()
      chartsObj.setOption(option)
      this.chartsEl = chartsObj
    },
    onPackData() {
      const xAxisData = []
      const seriesData = []

      // 迭代数据
      this.tableData.forEach(item => {
        xAxisData.push(item.itemname)
        seriesData.push(item.itemcount)
      })

      return {
        grid: {
          top: 50,
          left: 0,
          right: 0,
          bottom: 10,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisTick: {
            alignWithLabel: true,
            interval: 0,
            rotate: -30,
            margin: -15
          },
          axisLabel: {
            interval: 0,
            rotate: 60,
            fontSize: '0.8em',
          }
        },
        yAxis: {
          type: 'value',
          minInterval: 1,
        },
        series: [
          {
            itemStyle: {
              color: 'red'
            },
            data: seriesData,
            type: 'line',
            markPoint: {
              data: [
                { type: 'max', name: 'Max' },
                { type: 'min', name: 'Min' }
              ]
            },
            markLine: {
              data: [{ type: 'average', name: 'Avg' }]
            },
          }
        ]
      }
    }
  },
}
</script>

<style scoped>
.login-trend-chart {
  width: 100%;
  height: 20em;
}
</style>
