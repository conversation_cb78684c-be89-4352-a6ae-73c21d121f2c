<template>
<div>
  <c-query-card c-margin="20rpx">
    <div slot="title-left">
      <span style="font-weight: bold;">{{this.currentMode === 'quantity'?'业务量排行':'收款额排行' }}</span><i class="iconfont icon-qiehuan toggle-mode-btn" @click="onToggleMode"></i>
    </div>
    <div slot="title-right">
      <i style="margin-left: 1em;" class="iconfont icon-jiantou" @click="onGotoDetail"></i>
    </div>
    <div class="handling-volume-count-box">
      <div ref="handlingVolumeChart" class="handling-volume-chart"></div>
    </div>
  </c-query-card>
</div>
</template>

<script>
import countUtils from "../js/countUtils";
import CQueryCard from "../../../../components/list/CQueryCard";
export default {
  name: "HandlingVolumeCount",
  components: {CQueryCard},
  data() {
    return {
      currentMode: "quantity", // quantity(业务量)，turnover（营业额）
      total: {
        mobileBusCount: 0,
        pcBusCount: 0,
        totalBusCount: 0,
        mobileAmount: 0,
        pcAmount: 0,
        totalAmount: 0,
      },
      chartsEl: null,
      tableData: [],
      query: {},
    }
  },
  mounted() {
    const vm = this
    vm.onInitChart()
    window.addEventListener("resize", function () {
      vm.chartsEl?vm.chartsEl.resize():console.log('not resize');
    });
  },
  methods: {
    handleRefresh(query) {
      const vm = this
      this.query = query
      vm.$http.post(vm.$apis.callBoss, {
        service: vm.$serviceId.QUE_BUSI_DETAIL,
        params: {
          ...query,
          currentPage: 1,
          querynum: 100 // 默认查询100条
        }
      }).then(res => {
        if (res.responseBody.data.output && res.responseBody.data.output.result) {
          vm.tableData = res.responseBody.data.output.result
        } else {
          vm.tableData = []
        }
        vm.onInitChart()
      })
    },
    onGotoDetail() {
      const vm = this
      uni.setStorageSync("handlingVolumeDetailQuery", vm.query)
      uni.navigateTo({
        url: "/pages/FifthG/funs/dashboard/components/tables/HandlingVolumeDetail" + '?now=' + new Date().getTime()
      });
    },
    onToggleMode() {
      this.currentMode = this.currentMode === "quantity" ? "turnover" : "quantity";
      this.onInitChart();
    },
    onInitChart() {
      let element = this.$refs['handlingVolumeChart']
      let chartsObj = this.$echarts.init(element)
      const option = this.onPackData()
      chartsObj.setOption(option)
      this.chartsEl = chartsObj
    },
    onPackData() {
      // 加载数据
      let data = this.tableData
      console.log('data.length',data.length)

      // 冒泡排序desc
      for (let i = 0; i < data.length; i++) {
        for (let j = 0; j < data.length - i - 1; j++) {
          if (this.currentMode === "quantity") {
            // 转换乘整数
            let a = parseInt(data[j].totalBusCount)
            let b = parseInt(data[j + 1].totalBusCount)
            if (a < b) {
              let temp = data[j]
              data[j] = data[j + 1]
              data[j + 1] = temp
            }
          } else {
            // 转换乘整数
            let a = parseInt(data[j].totalAmount)
            let b = parseInt(data[j + 1].totalAmount)
            if (a < b) {
              let temp = data[j]
              data[j] = data[j + 1]
              data[j + 1] = temp
            }
          }
        }
      }

      // 封装数据
      let yAxisData = []
      let seriesData = [
        {
          name: this.currentMode === "quantity" ? "移动端业务量" : "移动端收款额",
          type: 'bar',
          barMinHeight: 40,
          label: {
            show: true
          },
          itemStyle: {
            color: '#0099FF'
          },
          data: []
        },
        {
          name: this.currentMode === "quantity" ? "PC业务量" : "PC收款额",
          type: 'bar',
          barMinHeight: 40,
          label: {
            show: true
          },
          itemStyle: {
            color: '#eeeeee'
          },
          data: []
        }
      ]

      this.total = {
        mobileBusCount: 0,
        pcBusCount: 0,
        totalBusCount: 0,
        mobileAmount: 0,
        pcAmount: 0,
        totalAmount: 0,
      }

      for (let i = 0; i < data.length; i++) {
        // 合计总数
        this.total.mobileBusCount += data[i].mobileBusCount?Number(data[i].mobileBusCount):0
        this.total.pcBusCount += data[i].pcBusCount?Number(data[i].pcBusCount):0
        this.total.totalBusCount += data[i].totalBusCount?Number(data[i].totalBusCount):0
        this.total.mobileAmount += data[i].mobileAmount?Number(data[i].mobileAmount):0
        this.total.pcAmount += data[i].pcAmount?Number(data[i].pcAmount):0
        this.total.totalAmount += data[i].totalAmount?Number(data[i].totalAmount):0
      }

      //总数金额只保留2为小数
      this.total.mobileAmount = this.total.mobileAmount.toFixed(2)
      this.total.pcAmount = this.total.pcAmount.toFixed(2)
      this.total.totalAmount = this.total.totalAmount.toFixed(2)

      let showData = []
      let showSize = 5
      if (data.length > showSize) {
        showData = data.slice(0, showSize)
      } else {
        showData = data
      }

      for (let i = 0; i < showData.length; i++) {
        yAxisData.unshift(showData[i].typename)
        seriesData[0].data.unshift(this.currentMode === "quantity" ? showData[i].mobileBusCount : showData[i].mobileAmount)
        seriesData[1].data.unshift(this.currentMode === "quantity" ? showData[i].pcBusCount : showData[i].pcAmount)
      }

      yAxisData.push(this.currentMode === "quantity" ? "总业务数" : "总收款额")
      seriesData[0].data.push(this.currentMode === "quantity" ? this.total.mobileBusCount : this.total.mobileAmount)
      seriesData[1].data.push(this.currentMode === "quantity" ? this.total.pcBusCount : this.total.pcAmount)

      // 计算差值与倍数
      let diff = 0
      if (this.currentMode === "quantity") {
        diff = this.total.mobileBusCount - this.total.pcBusCount
      } else {
        diff = this.total.mobileAmount - this.total.pcAmount
      }

      if (diff > 0) {
        // 移动端大于PC端
        // 计算差值倍数
        let multiple = 0
        if (this.currentMode === "quantity") {
          multiple = this.total.mobileBusCount / this.total.pcBusCount
        } else {
          multiple = this.total.mobileAmount / this.total.pcAmount
        }

        // 如果倍数大于10倍则设置默认长度
        if (multiple > 10) {
          seriesData[0].barMinHeight = 10
          seriesData[1].barMinHeight = 10
        } else {
          seriesData[0].barMinHeight = 30 * multiple
          seriesData[1].barMinHeight = 30
        }

      } else {
        // 移动端小于PC端
        let multiple = 0
        if (this.currentMode === "quantity") {
          multiple = this.total.pcBusCount / this.total.mobileBusCount
        } else {
          multiple = this.total.pcAmount / this.total.mobileAmount
        }

        // 如果倍数大于10倍则设置默认长度
        if (multiple > 10) {
          seriesData[0].barMinHeight = 10
          seriesData[1].barMinHeight = 10
        } else {
          seriesData[0].barMinHeight = 30
          seriesData[1].barMinHeight = 30 * multiple
        }
      }


      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          show: true,
          x: 'center',
          y: 'bottom',
          bottom: 20
        },
        grid: {
          top: 8,
          left: 0,
          bottom: 30,
          containLabel: true
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: yAxisData
        },
        series: seriesData
      }
    }
  },
}
</script>

<style scoped>
.toggle-mode-btn {
  color: #0099FF;
  margin-left: 0.5em;
}
.handling-volume-chart {
  width: 100%;
  height: 20em;
}
</style>
