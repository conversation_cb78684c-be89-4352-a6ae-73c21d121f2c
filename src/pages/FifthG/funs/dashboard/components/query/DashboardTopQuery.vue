<template>
<div class="dashboard-top-query-box">

  <div class="query-area-box">
    <div class="query-area-border-box" @click="onShowDate">
      <span>{{ dateValue?dateValue:'请选择时间'}}</span><i class="iconfont icon-zhankai"></i>
    </div>
    <div class="query-area-border-box" @click="onShowArea">
      <span>{{ areaName?areaName:'请选择业务区' }}</span><i class="iconfont icon-zhankai"></i>
    </div>
  </div>

  <c-popup-tree :tree-data="treeData" :confirm-select="onSelectArea" :selected="selectedIds" ref="treeRef"></c-popup-tree>

  <c-multi-date ref="multiDateRef" :on-select-confirm="onSelectDateConfirm"></c-multi-date>

</div>
</template>

<script>

import CPopupTree from "../../../../components/tree/CPopupTree";
import CWeekly from "../../../../components/weekly/CWeekly";
import {mapGetters} from "vuex";
import CForm from "../../../../components/form/CForm";
import CFormItem from "../../../../components/form/CFormItem";
import CMultiDate from "../../../../components/multidate/CMultiDate";
export default {
  name: "DashboardTopQuery",
  components: {CMultiDate, CFormItem, CForm, CWeekly, CPopupTree},
  props: {
    handleRefresh: {
      type: Function,
      default: () => {
      }
    }
  },
  data() {
    return {
      currentIndex: 0,
      currentConfig: {},
      queryForm: {},
      ready: false, // 这里用于自主控制loading加载状态，避免异步正在加载数据的空档显示“暂无数据”
      activeIds: [],
      activeIndex: 1,
      areaName: '',
      areas: [],
      treeData: [],
      selectedIds: [],
      dateValue: '',
      query: {
        city: '', // 分公司id
        areaid: '', // 业务区id
        deptid: '', // 部门id
        operid: '', // 操作员id
        statisticType: '', // 统计类型：0【个人】、1【部门】、2【分公司】、3【片区】、4【全省】
        queryTimeType: '', // 查询时间类型：0【当天】、1【历史】
        beginTime: '', // 开始时间
        endTime: '' // 结束时间
      },
      userIdInfo: {},
      startTpl: ' 00:00:00',
      endTpl: ' 23:59:59',
      dateType: 'day',
      statisticType: ''
    }
  },
  computed: {
    ...mapGetters({
      userInfo: 'getUserInfo'
    })
  },
  async mounted() {
    await this.loadPowerArea()

    // 默认查询
    await this.defaultQueryFunction()

    // // 默认统计当前用户
    // const now = new Date()
    // const nowYear = now.getFullYear();
    // const nowMonth = now.getMonth() + 1;
    // const nowDay = now.getDate();
    // this.dateValue = nowYear + '-' + nowMonth + '-' + nowDay
    // // 默认统计当前用户
    // this.areaName = this.userInfo.name
    // this.selectedIds = [this.userInfo.operid]
    //
    // this.query = {
    //   operid: this.userInfo.operid,
    //   statisticType: 0,
    //   queryTimeType: 0,
    //   beginTime: this.dateValue + this.startTpl,
    //   endTime: this.dateValue + this.endTpl
    // }

    this.onRefresh()
  },
  methods: {
    onSelectDateConfirm(val) {
      this.query.beginTime = val.beginTime
      this.query.endTime = val.endTime
      this.query.queryTimeType = this.isToday?'0':'1' // 0【当天】、1【历史】
      this.dateValue = val.displayValue
      this.dateType = val.dateType
      this.query.dateType = val.dateType
      this.onRefresh()
    },
    // 刷新数据
    onRefresh() {
      this.handleRefresh(this.query,this.userIdInfo)
    },
    // 加载权限区域
    async loadPowerArea() {
      const vm = this
      // vm.$http.post(vm.$apis.getReportPurview, {}).then(res => {
      //   vm.treeData = res.responseBody.data.tree
      //   this.userIdInfo = res.responseBody.data.userIdInfo
      // })

      const res = await vm.$http.post(vm.$apis.getReportPurview, {})
      vm.treeData = res.responseBody.data.tree
      this.userIdInfo = res.responseBody.data.userIdInfo
      this.statisticType = res.responseBody.data.statisticType
    },
    // 选择区域
    onSelectArea(sections) {
      const vm = this
      if (sections.length === 0) {
        sections = [{}]
      }
      vm.areas = sections
      vm.selectedIds = sections.map(item => item.id)
      if (sections.length > 0) {
        vm.areaName = sections[sections.length - 1].name
      } else {
        vm.areaName = ''
      }

      // 选择区域后，刷新数据 statisticType：0【个人】、1【部门】、2【分公司】、3【片区】、4【全省】
      if (sections[0].type === 'province') {
        vm.query = {
          statisticType: '4',
          queryTimeType: vm.query.queryTimeType,
          beginTime: vm.query.beginTime,
          endTime:  vm.query.endTime
        }
      }else if (sections[0].type === 'area') {
        vm.query = {
          areaid: sections[0].id,
          statisticType: '3',
          queryTimeType: vm.query.queryTimeType,
          beginTime: vm.query.beginTime,
          endTime:  vm.query.endTime
        }
      }else if (sections[0].type === 'company') {
        vm.query = {
          city: sections[0].id,
          statisticType: '2',
          queryTimeType: vm.query.queryTimeType,
          beginTime: vm.query.beginTime,
          endTime:  vm.query.endTime
        }
      } else if (sections[0].type === 'dept') {
        vm.query = {
          deptid: sections[0].id,
          statisticType: '1',
          queryTimeType: vm.query.queryTimeType,
          beginTime: vm.query.beginTime,
          endTime:  vm.query.endTime
        }
      } else if (sections[0].type === 'oper') {
        vm.query = {
          operid: sections[0].id,
          statisticType: '0',
          queryTimeType: vm.query.queryTimeType,
          beginTime: vm.query.beginTime,
          endTime:  vm.query.endTime
        }
      } else {
        uni.showToast({
          title: '请选择区域',
          icon: 'none'
        })
      }
      vm.handleRefresh(vm.query)
    },
    onShowArea() {
      this.$refs.treeRef.onShowTree()
    },
    onShowDate() {
      this.$refs.multiDateRef.onOpen()
    },
    async defaultQueryFunction() {
      const now = new Date()
      const nowYear = now.getFullYear();
      const nowMonth = now.getMonth() + 1;
      const nowDay = now.getDate();
      this.dateValue = nowYear + '-' + nowMonth + '-' + nowDay

      let query = {
        queryTimeType: 0,
        statisticType: this.statisticType,
        beginTime: this.dateValue + this.startTpl,
        endTime: this.dateValue + this.endTpl
      };

      this.areaName = this.treeData[0].name
      this.selectedIds = [this.treeData[0].id]

      switch (this.statisticType) {
        case '4': // 全省
          break;
        case '3': // 片区
          query.areaid = this.treeData[0].id;
          break;
        case '2': // 分公司
          query.city = this.treeData[0].id;
          break;
        case '1': // 部门
          query.deptid = this.treeData[0].id;
          break;
        case '0': // 个人
          query.operid = this.userInfo.operid;
          break;
        default:
          query.operid = this.userInfo.operid;
          return;
      }

      this.query = query
    }
  }
}
</script>

<style lang="scss" scoped>

.query-area-box {
  display: flex;
  background-color: #fff;
  padding: 0.5em;
  .query-area-border-box {
    width: 50%;
    border: 1px solid #eeeeee;
    display: flex;
    justify-content: space-between;
    padding: 0.2em 0.5em;
  }
}

//.dashboard-top-query-box {
//  height: 3em;
//  line-height: 3em;
//  background-color: #fff;
//  display: flex;
//  justify-content: space-between;
//  .query-right-box {
//    display: flex;
//    width: 50%;
//  }
//  .query-left-box {
//    display: flex;
//    width: 50%;
//  }
//  .query-border-box {
//    border: 1px solid #CCCCCC;
//    border-radius: 0.2em;
//    line-height: 1.8em;
//    padding: 0 1.5em 0 0.5em;
//    margin-top: 0.5em;
//    margin-right: 0.5em;
//    position: relative;

//    .query-icon {
//      position: absolute;
//      right: 0.1em;
//    }
//  }
//  .query-label-box {
//    margin: 0 0.5em;
//    width: 4em;
//  }
//  .query-text-box {
//  }
//}
.query-content-box {
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: right;
  padding-right: 2em;
  .query-icon {
    position: absolute;
    right: 0.1em;
  }
}



</style>
