<template>
  <div>
    <c-query-card c-margin="20rpx" title="系统访问量">
      <div class="system-visits-count-box">
        <c-table ref="tableRef" :table-data="tableData" :columns="columns" :border="true" ></c-table>
      </div>
    </c-query-card>
  </div>
</template>

<script>
import countUtils from "../js/countUtils";
import CQueryCard from "../../../../components/list/CQueryCard";
import CTable from "../../../../components/table/CTable";
export default {
  name: "SystemVisitsCount",
  components: {CTable, CQueryCard},
  data() {
    return {
      chartsEl: null,
      tableData: [],
      query: {},
      columns: [
        {
          name: "序号",
          type: "index"
        },
        {
          name: "菜单名称",
          prop: "name"
        },
        {
          name: "访问量",
          prop: "count",
          sort: true
        },
        {
          name: "同比上周期",
          prop: "rate",
          sort: true
        }
      ]
    }
  },
  mounted() {
    const vm = this

  },
  methods: {
    handleRefresh(query) {
      const vm = this
      this.query = query
      vm.$http.post(vm.$apis.countSystemVisits, {
        params: {
          ...query
        }
      }).then(res => {
        if (res.responseBody.data.list) {
          vm.tableData = res.responseBody.data.list
        } else {
          vm.tableData = []
        }
        this.$refs.tableRef.onRefresh(vm.tableData)
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.system-visits-count-box {
  margin-top: 0.5em;
}
</style>
