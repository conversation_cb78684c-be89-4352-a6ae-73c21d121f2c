<template>
<div class="count-content-box">
  <c-query-card title="移网开户" c-margin="20rpx">
    <div slot="title-right">
      <span>总数：<span style="font-weight: bold;color: #0099FF;">{{total}}户</span>，收款：<span style="font-weight: bold;color: red;">{{paymentLabel}}</span></span><i style="margin-left: 1em;" class="iconfont icon-jiantou" @click="onGotoDetail"></i>
    </div>
    <div style="margin-top: 0.5em">
      <c-table ref="tableRef" :table-data="tableData" :columns="columns" :border="true" ></c-table>
    </div>
  </c-query-card>
</div>
</template>

<script>
import CQueryCard from "../../../../components/list/CQueryCard";
import CTable from "../../../../components/table/CTable";
import { yuanToWan } from "../../../../../../common/js/AmountUtils";
export default {
  name: "AccountOpeningCount",
  components: {CTable, CQueryCard},
  data() {
    return {
      total: 0,
      payment: 0,
      columns: [
        {
          name: "套餐类型",
          prop: "salesname"
        },
        {
          name: "开户数量",
          prop: "totalBusCount"
        },
        {
          name: "收款金额",
          prop: "totalAmount"
        }
      ],
      tableData: [],
      query: {}
    };
  },
  computed: {
    paymentLabel() {
      return this.payment > 10000 ? `${yuanToWan(this.payment)}万元`: `${this.payment.toFixed(2)}元`
    }
  },
  methods: {
    onGotoDetail() {
      const vm = this
      uni.setStorageSync("accountOpeningDetailQuery", vm.query)
      uni.navigateTo({
        url: "/pages/FifthG/funs/dashboard/components/tables/AccountOpeningDetail" + '?now=' + new Date().getTime()
      });
    },
    handleRefresh(query) {
      const vm = this
      this.query = query
      vm.$http.post(vm.$apis.callBoss, {
        service: vm.$serviceId.QUE_PROD_DETAIL,
        params: {
          ...query,
          currentPage: 1,
          querynum: 500 // 默认查询500条
        }
      }).then(res => {
        if (res.responseBody.data.output && res.responseBody.data.output.result) {
          let tableData = res.responseBody.data.output.result

          vm.total = 0
          vm.payment = 0
          tableData.forEach(item => {
            vm.total += Number(item.totalBusCount)
            vm.payment += Number(item.totalAmount)
          })
          // 根据开户数量排序
          tableData.sort((a, b) => {
            return b.totalBusCount - a.totalBusCount
          })

          // 如果数量超过5条，则取前五条
          if (tableData.length > 5) {
            tableData = tableData.slice(0, 5)
          }

          vm.tableData = tableData
          vm.$refs.tableRef.onRefresh(vm.tableData)
        } else {
          vm.tableData = []
        }
      })
    },
  }
}
</script>

<style scoped>
@import "../css/index.css";
.title-count-box {
}
</style>
