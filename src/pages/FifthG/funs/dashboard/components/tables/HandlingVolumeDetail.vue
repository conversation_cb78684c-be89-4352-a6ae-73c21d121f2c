<template>
  <c-layout title="业务受理明细">
    <div class="handling-volume-detail-box">
      <div style="padding: 0.5em">
        <span>业务数合计：<span style="font-weight: bold;color: #0099FF;">{{total}}个</span>，收款金额合计：<span style="font-weight: bold;color: red;">{{payment.toFixed(2)}}元</span></span>
      </div>
      <c-table ref="tableRef" :table-data="tableData" :columns="columns" :border="true" ></c-table>
    </div>
  </c-layout>
</template>

<script>
import CLayout from "../../../../components/layout/CLayout";
import CTable from "../../../../components/table/CTable";
export default {
  name: "HandlingVolumeDetail",
  components: {CTable, CLayout},
  data() {
    return {
      columns: [
        {
          name: "业务类型",
          prop: "typename"
        },
        {
          name: "业务数量",
          prop: "totalBusCount",
          sort: true
        },
        {
          name: "收款金额",
          prop: "totalAmount",
          sort: true
        }
      ],
      tableData: [],
      total: 0,
      payment: 0
    }
  },
  mounted() {
    this.onLoadDetail()
  },
  methods: {
    onLoadDetail() {
      const vm = this
      let query = uni.getStorageSync("handlingVolumeDetailQuery")
      vm.$http.post(vm.$apis.callBoss, {
        service: vm.$serviceId.QUE_BUSI_DETAIL,
        params: {
          ...query,
          currentPage: 1,
          querynum: 100 // 默认查询200条
        }
      }).then(res => {
        if (res.responseBody.data.output && res.responseBody.data.output.result) {
          console.log('onLoadDetail')
          this.tableData = res.responseBody.data.output.result
          // 计算总数和总金额
          let total = 0
          let payment = 0
          this.tableData.forEach(item => {
            total += Number(item.totalBusCount)
            payment += Number(item.totalAmount)
          })
          this.total = total
          this.payment = payment
          this.$refs.tableRef.onRefresh(vm.tableData)
        }
      })
    }
  }
}
</script>

<style scoped>
.handling-volume-detail-box {
  background-color: #fff;
}
</style>
