<template>
  <c-layout title="开户套餐明细">
    <div class="account-opening-detail-box">
      <div style="padding: 0.5em">
        <span>开户数量合计：<span style="font-weight: bold;color: #0099FF;">{{total}}户</span>，收款金额合计：<span style="font-weight: bold;color: red;">{{paymentLabel}}</span></span>
      </div>
      <c-table ref="tableRef" :table-data="tableData" :columns="columns" :border="true" ></c-table>
    </div>
  </c-layout>
</template>

<script>
import CLayout from "../../../../components/layout/CLayout";
import CTable from "../../../../components/table/CTable";
import {yuanToWan} from "../../../../../../common/js/AmountUtils";

export default {
  name: "AccountOpeningDetail",
  components: {CTable, CLayout},
  data() {
    return {
      columns: [
        {
          name: "序号",
          type: "index",
          width: "60rpx"
        },
        {
          name: "套餐类型",
          prop: "salesname"
        },
        {
          name: "开户数量",
          prop: "totalBusCount",
          sort: true,
          width: "160rpx"
        },
        {
          name: "收款金额",
          prop: "totalAmount",
          sort: true,
          width: "160rpx"
        }
      ],
      tableData: [],
      total: 0,
      payment: 0
    }
  },
  mounted() {
    this.onLoadDetail()
  },
  computed: {
    paymentLabel() {
      return this.payment > 10000 ? `${yuanToWan(this.payment)}万元`: `${this.payment.toFixed(2)}元`
    }
  },
  methods: {
    onLoadDetail() {
      const vm = this
      let query = uni.getStorageSync("accountOpeningDetailQuery")
      vm.$http.post(vm.$apis.callBoss, {
        service: vm.$serviceId.QUE_PROD_DETAIL,
        params: {
          ...query,
          currentPage: 1,
          querynum: 500 // 默认查询500条
        }
      }).then(res => {
        if (res.responseBody.data.output && res.responseBody.data.output.result) {
          this.tableData = res.responseBody.data.output.result
          // 计算总数和总金额
          let total = 0
          let payment = 0
          this.tableData.forEach(item => {
            total += Number(item.totalBusCount)
            payment += Number(item.totalAmount)
          })
          console.log('this.tableData.length',this.tableData.length)
          this.total = total
          this.payment = payment
          this.$refs.tableRef.onRefresh(vm.tableData)
        }
      })
    }
  }
}
</script>

<style scoped>
.account-opening-detail-box {
  background-color: #fff;
}
</style>
