<template>
  <c-layout title="业务受理明细">
    <div class="handling-volume-detail-box">
      <div style="padding: 0.5em">
        <span>业务数合计：<span style="font-weight: bold;color: #0099FF;">{{total}}笔</span>，收款金额合计：<span style="font-weight: bold;color: red;">{{payment.toFixed(2)}}元</span></span>
      </div>
      <c-table ref="tableRef" :table-data="tableData" :columns="columns" :border="true" ></c-table>
    </div>

    <div class="handling-volume-detail-box">
      <div style="padding: 0.5em">
        <span>开户数合计：<span style="font-weight: bold;color: #0099FF;">{{salesTotal}}笔</span>，收款金额合计：<span style="font-weight: bold;color: red;">{{salesPayment.toFixed(2)}}元</span></span>
      </div>
      <c-table ref="tableRef" :table-data="salesData" :columns="salesColumns" :border="true" ></c-table>
    </div>
  </c-layout>
</template>

<script>
import CLayout from "../../../../components/layout/CLayout";
import CTable from "../../../../components/table/AgentCTable";
export default {
  name: "AgentHandlingVolumeDetail",
  components: {CTable, CLayout},
  data() {
    return {
      columns: [
        {
          name: "业务类型",
          prop: "typename"
        },
        {
          name: "业务数量",
          prop: "totalBusCount",
          sort: true
        },
        {
          name: "收款金额",
          prop: "totalAmount",
          sort: true
        }
      ],
      salesColumns: [
        {
          name: "套餐名称",
          prop: "salesname"
        },
        {
          name: "业务数量",
          prop: "openCount",
          sort: true
        },
        {
          name: "收款金额",
          prop: "mobileAmount",
          sort: true
        }
      ],
      tableData: [],
      salesData: [],
      total: 0,
      salesTotal: 0,
      payment: 0,
      salesPayment: 0,
    }
  },
  mounted() {
    this.onLoadDetail()
    this.onLoadSalesDetail()
  },
  methods: {
    onLoadDetail() {
      const vm = this
      let query = uni.getStorageSync("handlingVolumeDetailQuery")
      vm.$http.post(vm.$apis.callBoss, {
        service: vm.$serviceId.QUE_BUSI_DETAIL_FGB,
        params: {
          ...query,
          currentPage: 1,
          querynum: 100 // 默认查询200条
        }
      }).then(res => {
        if (res.responseBody.data.output && res.responseBody.data.output.result) {
          console.log('onLoadDetail')
          this.tableData = res.responseBody.data.output.result
          // 计算总数和总金额
          let total = 0
          let payment = 0
          this.tableData.forEach(item => {
            total += Number(item.totalBusCount)
            payment += Number(item.totalAmount)
          })
          this.total = total
          this.payment = payment
          // this.$refs.tableRef.onRefresh(vm.tableData)
        }
      })
    },
    onLoadSalesDetail() {
      const vm = this
      let query = uni.getStorageSync("handlingVolumeDetailQuery")
      vm.$http.post(vm.$apis.callBoss, {
        service: vm.$serviceId.QUE_INSTALL_PROP_FGB,
        params: {
          ...query,
          currentPage: 1,
          querynum: 100, // 默认查询200条
        }
      }).then(res => {
        console.log('QUE_INSTALL_PROP_FGB====>',res.responseBody.data.output)
        if (res.responseBody.data.output && res.responseBody.data.output.result) {
          console.log('onLoadDetail==========QUE_INSTALL_PROP_FGB')
          this.salesData = res.responseBody.data.output.result
          // 计算总数和总金额
          let total = 0
          let payment = 0
          this.salesData.forEach(item => {
            total += Number(item.openCount)
            payment += Number(item.mobileAmount)
          })
          this.salesTotal = total
          this.salesPayment = payment
        }
      })
    }
  }
}
</script>

<style scoped>
.handling-volume-detail-box {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 10rpx;
}
/deep/ .layout-scroll-view {
  height: 100% !important;
}
</style>
