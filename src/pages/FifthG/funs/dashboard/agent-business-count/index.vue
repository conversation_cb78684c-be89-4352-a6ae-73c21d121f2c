<template>
  <c-layout title="业务量统计">
      <div style="padding-bottom: 1em">
        <agent-dashboard-top-query :handle-refresh="handleRefresh"></agent-dashboard-top-query>
        <agent-handling-volume-count ref="handlingVolumeCount"></agent-handling-volume-count>
      </div>
  </c-layout>
</template>

<script>
import CLayout from "../../../components/layout/CLayout";
import CNoData from "../../../components/list/CNoData";
import AgentDashboardTopQuery from "../components/query/AgentDashboardTopQuery";
import AgentHandlingVolumeCount from "../components/charts/AgentHandlingVolumeCount";

export default {
  name: "BusinessCount",
  components: {
    AgentDashboardTopQuery, AgentHandlingVolumeCount, CNoData, CLayout
  },
  data() {
    return {

    }
  },
  mounted() {

  },
  methods: {
    handleRefresh(query,userIdInfo) {
      // this.$refs.accountOpeningCount.handleRefresh(query)
      this.$refs.handlingVolumeCount.handleRefresh(query)
      // this.$refs.handlingVolumeTop.handleRefresh(query, userIdInfo)
    }
  }
}
</script>

<style scoped>

</style>
