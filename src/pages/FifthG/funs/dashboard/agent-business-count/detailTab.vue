<template>
  <div class="content">
    <div class="tabs">
      <div class="tab-item" :class="{actived: tabIdx == 0}" @click="changeTab(0)">移网业务量</div>
      <div class="tab-item" :class="{actived: tabIdx == 1}" @click="changeTab(1)">固网业务量</div>
    </div>

    <agent-handling-volume-detail v-if="tabIdx === 0"></agent-handling-volume-detail>

    <agent-handling-volume-fixed-detail v-if="tabIdx === 1"></agent-handling-volume-fixed-detail>
  </div>
</template>

<script>
import AgentHandlingVolumeDetail from '../components/tables/AgentHandlingVolumeDetail.vue'
import AgentHandlingVolumeFixedDetail from '../components/tables/AgentHandlingVolumeFixedDetail.vue'
export default {
  name: "detailTab",
  components: {
    AgentHandlingVolumeDetail,
    AgentHandlingVolumeFixedDetail
  },
  data() {
    return {
      tabIdx: 0,
    }
  },
  methods:{
    changeTab(n) {
      this.tabIdx = n
    },
  }
}
</script>

<style lang="scss" scoped>

.content {
  display: flex;
  flex-direction: column;
  height: 100%;
}


.tabs {
  display: flex;
  align-items: center;
  background-color: #fff;

  .tab-item {
    flex: 1;
    padding: 20rpx 0;
    text-align: center;
    font-size: 32rpx;

    &.actived {
      position: relative;
      color: #2B71FF;

      &::after {
        content: "";
        display: block;
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 80rpx;
        height: 4rpx;
        background-color: #2B71FF;
        transform: translate(-50%, 0);
      }
    }
  }
}



</style>
