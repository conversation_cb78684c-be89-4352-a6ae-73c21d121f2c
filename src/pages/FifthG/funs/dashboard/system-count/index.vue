<template>
  <c-layout title="系统访问量统计">
    <dashboard-top-query :handle-refresh="handleRefresh"></dashboard-top-query>
    <login-trend ref="loginTrend"></login-trend>
    <system-visits-count ref="systemVisitsCount"></system-visits-count>
  </c-layout>
</template>

<script>
import CLayout from "../../../components/layout/CLayout";
import CNoData from "../../../components/list/CNoData";
import LoginTrend from "../components/charts/LoginTrend";
import DashboardTopQuery from "../components/query/DashboardTopQuery";
import SystemVisitsCount from "../components/tables/SystemVisitsCount";
export default {
  name: "SystemCount",
  components: {SystemVisitsCount, DashboardTopQuery, LoginTrend, CNoData, CLayout},
  data() {
    return {
    }
  },
  mounted() {
  },
  methods: {
    handleRefresh(query,userIdInfo) {
      if (!query.dateType) {
        query.dateType = 'day'
      }
      this.$refs.loginTrend.handleRefresh(query)
      this.$refs.systemVisitsCount.handleRefresh(query)
    }
  }
}
</script>

<style scoped>

</style>
