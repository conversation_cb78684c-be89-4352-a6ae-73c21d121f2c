<template>
	<view class="recharge">
		<!-- <action-bar style="width: 100%;" title="移动话费充值" leftIcon="back"></action-bar> -->
		<view class="recharge-top">
			<view class="top">
				<!-- <view class="txt">
          手机号
        </view>
        <view class="input">
          <uni-easyinput type="number" v-model="params.phone" placeholder="请输入手机号" :placeholderStyle="placeholderStyle" :clearable="false" :inputBorder="false" @blur="blur"></uni-easyinput>
        </view>
        <view class="icon">
          <text class="iconfont icon-color" v-html="iconfont.more"></text>
        </view> -->
				<view class="picItem"><PickMsgItem ref="phonePic" title="手机号" :range="phoneList" :defauleVal="defaulePhone" rangeKey="mobileno" defauleValKey="mobileno" @onPick="onPhonePick"></PickMsgItem></view>
			</view>
		</view>
		<view class="recharge-main"><recharge-list :data="rechargeData" :params="params" @change="rechargeChange"></recharge-list></view>
		<view class="recharge-btn">
			<view :class="['btn', { active: params && params.fee && params.phone }]" @click="rechargeBtn()">
				{{ params && params.fee ? params.fee + '元&nbsp;&nbsp;&nbsp;' : '' }}确认支付
			</view>
		</view>
		<view class="record" @click="recordchange">充值记录</view>
	</view>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import rechargeList from '@/components/FifthG/rechargeList.vue';
import sign from '@/components/FifthG/sign.vue';
import PickMsgItem from '@/pages/FifthG/components/PickMsgItem.vue';
export default {
	name: 'recharge',
	data() {
		return {
			placeholderStyle: 'color: #999;font-size: 28rpx',
			iconfont: {
				more: '&#xe602;'
			},
			params: {
				amount: 0,
				phone: '',
				discount: 1,
				fee: 0
			},
			rechargeData: [],
			signVisible: false,
			signImg: '',
			phoneList: [],
			defaulePhone: ''
		};
	},
	components: {
		rechargeList,
		sign,
		PickMsgItem
	},
	computed: {
		...mapGetters({
			userInfo: 'getUserInfo',
			custInfo: 'getCustInfo',
			orderInfo: 'getOrderInfo'
		}),
		custOrderId() {
			return this.orderInfo?.custOrderid || '';
		}
	},
	watch: {
		custOrderId: {
			handler(newVal, oldVal) {},
			immediate: false,
			deep: true
		}
	},
	onLoad(data) {
		if (data && data.phone) {
			this.defaulePhone = data.phone
		}
	},
	created() {
		this.InitData();
	},
	methods: {
		recordchange() {
			this.$rauter.navigateTo({
				url: 'recordchange'
			});
		},
		...mapMutations({
			setOrderInfo: 'setOrderInfo',
			resetOrderInfo: 'resetOrderInfo',
			resetStepInfo: 'resetStepInfo'
		}),
		...mapActions({
			loadOrder: 'loadOrder'
		}),
		InitData() {
			this.resetOrderInfo();
			if (this.custInfo && this.custInfo.servs && this.custInfo.servs.length) {
				let phoneList = [];
				this.custInfo.servs.forEach(val => {
					// servtype ： 0是主卡 1是副卡
					if (val.mobileno && val.servtype === '0') {
						phoneList.push(val);
					}
				});
				this.phoneList = [...phoneList];
			}
			if (this.phoneList && this.phoneList.length) {
				this.params.phone = this.phoneList[0].mobileno;
			}
			this.getData();
		},
		back() {
			window.baseAppJs.back();
		},
		blur(event) {
			let reg = /^((13[0-9])|(14[0,1,4-9])|(15([0-3]|[5-9]))|(16[2,5,6,7])|(17[0,1,3,5,6,7,8])|(18[0-9])|(19[1-3,5-9]))\d{8}$/;
		},
		getData() {
			this.$http
				.post(this.$apis.queM5gRechargeFee)
				.then(response => {
					let res = response.responseBody;
					if (res && res.data && res.data.length) {
						res.data.map(val => {
							val.selling = val.fee;
							val.selling = val.discount ? val.fee * val.discount * 0.1 : val.fee;
							val.selling = val.selling.toFixed(2);
							return val;
						});
						this.rechargeData = [...res.data];
					}
				})
				.catch(err => {})
				.finally(arg => {});
		},
		rechargeChange(obj, type, field) {
			let params = JSON.parse(JSON.stringify(this.params));
			params.amount = obj.amount;
			params.fee = obj.fee;
			this.params = JSON.parse(JSON.stringify(params));
		},
		rechargeBtn() {
			// this.signVisible = true
			let timer = setTimeout(() => {
				clearTimeout(timer);
				let params = JSON.parse(JSON.stringify(this.params));
				if (!params.phone) {
					uni.showToast({
						title: '请先选择手机号',
						icon: 'none'
					});
				} else if (!params.fee) {
					uni.showToast({
						title: '请先选择或者输入充值金额',
						icon: 'none'
					});
				} else {
					if (this.custInfo && this.custInfo.servs) {
						let servDetailObj;
						for (let i = 0, l = this.custInfo.servs.length; i < l; i++) {
							const item = this.custInfo.servs[i];
							if (item.servDetails) {
								servDetailObj = item.servDetails.find(sd => {
									return sd.keyno === params.phone && sd.permark === 'B' && sd.servstatus === '1';
								});
								if (servDetailObj) {
									break;
								}
							}
						}
						if (servDetailObj) {
							uni.showToast({
								title: '国网开户不成功不允许充值，请联系管理员',
								icon: 'none'
							});
							return;
						}
					}

					let processReq = {
						"pcode": 'RECHARGE',
						"custid": this.custInfo ? this.custInfo.custid : "",
						"custname": this.custInfo ? this.custInfo.custname : "",
						"salesid": '',
						"salesname": ''
					}
					let processData = {
						data: JSON.stringify({
							custid: this.custInfo?.custid,
							mobileno: params.phone,
							custname: this.custInfo?.custname,
							fee: String(params.fee) // 原本fee改成amount，改成实际交付的金额
						}),
						temp: '1'
					}

					let data = {
						custid: this.custInfo?.custid,
						mobileno: params.phone,
						custname: this.custInfo?.custname,
						fee: String(params.fee), // 原本fee改成amount，改成实际交付的金额
						processDataReq: processData,
						processReq: processReq
					};
					this.$http
						.post(this.$apis.m5gPhoneRecharge, data)
						.then(response => {
							let res = response.responseBody;
							if (res && Object.keys(res).length && res.custOrderid) {
								if (!res.custorderid) res.custorderid = res.custOrderid;
								if (!res.fees) res.fees = res.sums;
								this.setOrderInfo(res);
								// 充值跳转，清空业务开通的信息
								this.resetStepInfo();
								let nextPage = res.nextPage
								let poid = res.poid
								let cpcode = res.cpcode
								// 跳转到完成提示页面
								// 判断返回的金额，如果金额为0，直接就提交订单
								if (nextPage) {
									// 跳转到下一步
									this.$rauter.navigateTo({
										url: nextPage,
										data: {
											poid: poid,
											currentStep: cpcode,
											params: this.params
										}
									});
								} else {
									this.$rauter.navigateTo({
										url: 'orderDetail',
										data: {
											params: this.params
										}
									});
								}
							}
						})
						.catch(err => {})
						.finally(arg => {});
				}
			}, 10);
		},
		signClose(val) {
			this.signVisible = false;
			this.signImg = val;
		},
		onPhonePick(val) {
			if (val.mobileno) {
				this.params.phone = val.mobileno;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.record {
	text-align: center;
	text-decoration: underline;
	color: #fa8c16;
	width: 30%;
	margin: 0 auto;
}
.recharge {
	width: 100%;
	overflow: hidden;
	box-sizing: border-box;
	view {
		box-sizing: border-box;
	}
	&-top {
		width: 100%;
		overflow: hidden;
		background-color: #fff;
		padding: 0rpx 20rpx 30rpx;
		.top {
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.picItem {
				flex: 1;
			}
			.txt {
				font-size: 28rpx;
				color: #000;
				min-width: 100rpx;
			}
			.input {
				flex: 1;
				::v-deep .uni-easyinput {
					input {
						height: 50rpx;
						font-size: 28rpx;
						color: #101010;
					}
				}
			}
			.icon {
				font-size: 32rpx;
			}
		}
		.tip {
			width: 100%;
			overflow: hidden;
			margin-top: 40rpx;
			font-size: 24rpx;
			line-height: 34rpx;
			color: #999;
		}
	}
	&-main {
		width: 100%;
		overflow: hidden;
		background-color: #fff;
	}
	&-btn {
		width: 100%;
		overflow: hidden;
		padding: 30rpx 40rpx;
		.btn {
			width: 100%;
			text-align: center;
			overflow: hidden;
			color: #fff;
			border-radius: 60rpx;
			line-height: 100rpx;
			height: 100rpx;
			font-size: 32rpx;
			background-color: #bfbfbf;
			&.active {
				background-color: #fa8c16;
			}
		}
	}
}
</style>
