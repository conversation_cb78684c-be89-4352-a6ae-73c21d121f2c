<template>
	<view class="">
		<!-- <action-bar style="width: 100%;" title="充值记录" leftIcon="back" :winHisBack="true"></action-bar> -->
		<uni-forms class="recodechange">
			<uni-forms-item label="客户姓名:" name="name">
				<text>{{ rechargeList.custname }}</text>
			</uni-forms-item>
			<uni-forms-item label="客户编号:" name="name">
				<text class="red">{{ rechargeList.custid }}</text>
			</uni-forms-item>
			<uni-forms-item name="hobby" label="手机号码:">
				<text>{{ rechargeList.phone }}</text>
			</uni-forms-item>
			<uni-forms-item name="hobby" label="充值金额:">
				<text>{{ rechargeList.fees }}</text>
			</uni-forms-item>
			<uni-forms-item name="hobby" label="充值时间:">
				<text>{{ rechargeList.createtime }}</text>
			</uni-forms-item>
			<uni-forms-item name="hobby" label="状态:">
				<text>{{ rechargeList.status }}</text>
			</uni-forms-item>
		</uni-forms>
		<view class="recharge-btn" v-if="isrealdyPay == 7"><view class="btn" @click="rechargeBtn()">重新提交</view></view>
	</view>
</template>

<script>
import { v4 as uuidv4 } from 'uuid';
import { mapGetters, mapActions } from 'vuex';
export default {
	data: function() {
		return {
			isrealdyPay: '',
			rechargeList: ''
		};
	},
	async onLoad(options) {
		this.rechargeList = JSON.parse(options.item);
		// this.status = status[this.rechargeList.status]
		// this.custOrderId=this.rechargeList.custOrderId
		this.checkOrder();
	},
	async onShow() {
		await this.loadOrder({
			orderId: this.custOrderId
		});
	},
	computed: {
		...mapGetters({
			// userInfo: "getUserInfo",
			stepInfo: 'getStepInfo',
			custInfo: 'getCustInfo',
			orderInfo: 'getOrderInfo'
		}),
		custOrderId() {
			return this.orderInfo.custorderid;
		}
	},
	methods: {
		...mapActions({
			loadOrder: 'loadOrder'
		}),
		rechargeBtn() {
			this.pay();
		},
		pay() {
			if (this.isrealdyPay == 7) {
				this.saveData();
			}
			// else{
			// 	console.log('33')
			// 	this.$rauter.navigateTo({
			// 		url: 'businessPay',
			// 	})
			// }
		},
		saveData() {
			let paycode = '000000';
			let payreqid = uuidv4();
			// 业务充值的提交
			let params = {
				paycode: paycode,
				payreqid: payreqid,
				payway: this.rechargeList.payway
			};
			console.log('业务充值');
			params.multipaywayflag = 'N';
			params.custorderid = this.custOrderId;
			this.$http.post(this.$apis.commitOrder, params).then(resp => {
				this.navigateBack(resp);
				// uni.navigateBack()
			});
		},
		async navigateBack(resp, isPay) {
			console.log('88');
			let res = resp.responseBody;
			if ((res && res.serialno) || isPay) {
				if (this.custOrderId) {
					// 只要存在订单id,就load订单对象
					await this.loadOrder({
						orderId: this.custOrderId
					});
				}
				uni.showToast({
					title: '支付成功',
					icon: 'none',
					mask: true
				});
				let timers = setTimeout(() => {
					clearTimeout(timers);
					uni.hideToast();
					if (this.stepInfo?.poid) {
						// 跳转到签名
						// this.$rauter.navigateBack()
						this.$rauter.redirectTo({
							url: 'businessConfirm',
							data: this.options
						});
						// this.$rauter.navigateBack({
						// 	delta: 2
						// })
					} else {
						// 跳转到完成提示页面
						this.$rauter.navigateTo({
							url: 'payComplete',
							data: {
								content: '支付完成',
								url: 'recharge'
							}
						});
					}
					// this.$rauter.navigateBack()
				}, 1000);
			}
		},
		checkOrder() {
			if (this.custOrderId) {
				// let custOrderId = this.stepInfo?.data?.GDKH?.custOrderid || '';
				let payway = this.rechargeList.payway;
				if (payway != 'H' && payway != 'G') {
					//如果不是支付宝和微信支付，则不查询
					return;
				}
				this.$http
					.request({
						method: 'POST',
						api: this.$apis.queQdUnifyPayResultInfo,
						url: this.$baseUrl,
						isAutoLoading: false,
						data: { custorderid: this.custOrderId }
					})
					.then(resp => {
						console.log(resp.responseBody, '00000-');
						this.isrealdyPay = resp.responseBody.data.cmms_status;
						// this.cmms_status = resp.responseBody.data.cmms_status
						// if(isrealdyPay) {
						//         // this.saveData();
						// }
					});
			}
		}
	}
};
</script>

<style>
.recodechange {
	background: #fff;
	padding: 10upx 40upx;
}
.red {
	color: red;
}
/deep/ uni-view {
	font-size: 28upx;
	line-height: 72upx;
}
/deep/.uni-forms-item__inner {
	display: flex;
	padding-bottom: 10upx;
}
/deep/.uni-forms-item__label {
	display: flex;
	flex-shrink: 0;
	box-sizing: border-box;
	flex-direction: row;
	align-items: center;
	width: 130upx;
	/* padding: 5px 0; */
	height: 72upx;
	margin-right: 40upx;
}
.btn {
	margin: 20upx auto;
	width: 90%;
	text-align: center;
	overflow: hidden;
	color: #fff;
	border-radius: 60rpx;
	line-height: 100rpx;
	height: 100rpx;
	font-size: 32rpx;
	background-color: #fa8c16;
	/* &.active {
			background-color: #fa8c16;
		} */
}
</style>
