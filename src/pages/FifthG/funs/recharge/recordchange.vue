<template>
  <c-layout title="充值记录">
    <view class="damages-list-box">
      <view v-if="rechargeList.length > 0" class="damages-body-box">
        <c-query-card v-for="item in rechargeList" :title="`订单编号：${item.custorderid}`" :key="item.custorderid" c-margin="20rpx">
					<text slot="title-right">
					  <text v-if="item.status == 0" style="color: #0084ff;"  @click.stop="toRecharge(item)">去支付</text>
					</text>
          <c-query-info-item label="客户姓名" :value="item.custname"></c-query-info-item>
					<c-query-info-item label="客户编号" :value="item.custid"></c-query-info-item>
          <c-query-info-item label="手机号码" :value="item.phone"></c-query-info-item>
          <c-query-info-item label="充值金额" :value="item.fees"></c-query-info-item>
          <c-query-info-item label="充值时间" :value="item.createtime"></c-query-info-item>
					<c-query-info-item label="状态" :value="item.statusname"></c-query-info-item>
          <c-query-info-item v-if="item.payway === '0'" label="支付方式" :value="'现金支付'"></c-query-info-item>
          <c-query-info-item v-else label="支付方式" :value="item.paywayName"></c-query-info-item>
        </c-query-card>
      </view>
      <c-no-data v-else title="该客户暂无充值记录"></c-no-data>
    </view>
  </c-layout>
</template>

<script>
import CForm from "../../components/form/CForm";
import CNoData from "../../components/list/CNoData";
import CQueryCard from "../../components/list/CQueryCard";
import CQueryInfoItem from "../../components/list/CQueryInfoItem";
import { toName } from "../../enums/enums";
import CLayout from "../../components/layout/CLayout";
// import { mapGetters } from 'vuex';
import { mapGetters, mapMutations } from 'vuex';
const status = {
	0: '未支付',
	1: '订单已删除',
	2: '已支付',
	3: '已过期',
	7: '退款'
};
export default {
	data() {
		return {
			rechargeList: []
			// status:''
		};
	},
	components: {CLayout, CForm,CNoData,CQueryCard,CQueryInfoItem},
	computed: {
		...mapGetters({
			custInfo: 'getCustInfo'
		})
	},
	onLoad(options) {
		this.m5gPhoneRechargeList();
	},
	created() {
		this.InitData();
		// this.getPayWay();
	},
	methods: {
		...mapMutations({
			setOrderInfo: 'setOrderInfo',
			resetOrderInfo: 'resetOrderInfo',
			resetStepInfo: 'resetStepInfo'
		}),
		InitData() {
			this.resetOrderInfo();
		},
		m5gPhoneRechargeList() {
			if (this.custInfo?.custid) {
				let data = {
					custid: this.custInfo?.custid + ''
				};
				this.$http.post(this.$apis.m5gPhoneRechargeList, data).then(response => {
					let res = response.responseBody;
					this.rechargeList = response.responseBody.orderlist;
					for (let i in this.rechargeList) {
						// console.log(this.rechargeList[i].status,'00000')
						// this.rechargeList[i].status = status[this.rechargeList[i].status];
						this.rechargeList[i].statusname = status[this.rechargeList[i].status];
						this.rechargeList[i].paywayName = toName('payway', this.rechargeList[i].subpayway)
					}
				});
			}
		},
		gorecodedetail(item) {
			let res = {
				custOrderid: item.custorderid,
				custorderid: item.custorderid,
				fees: item.fees,
				sums: item.sums
			};
			this.setOrderInfo(res);
			this.resetStepInfo();
			// debugger
			this.$rauter.navigateTo({
				url: 'recoddetail',
				data: {
					item: item
				}
			});
		},
		toRecharge(item) {
			const res = {}
			if (!res.custorderid) res.custorderid = item.custorderid;
			if (!res.fees) res.fees = item.fees;
			this.setOrderInfo(res);
			// 充值跳转，清空业务开通的信息
			this.resetStepInfo();
			// 根据流程号查询
			const poid = item.poid
			if (poid) {
				this.$http.post(this.$apis.getProgess, {
				  poid: poid
				}).then(resp => {
				  console.log(resp)
					if (resp && resp.responseBody) {
						const currentStep = resp.responseBody.currentStep
						const currentStepStatus = resp.responseBody.currentStepStatus
						if (currentStepStatus == 1) {
							this.$rauter.navigateTo({
								url: 'orderDetail',
								data: {
									poid: poid,
									currentStep: currentStep
								}
							});
						} else {
							uni.showModal({
								title: '温馨提示',
								content: '当前业务已完结',
								showCancel: true, //没有取消按钮的弹框
								buttonText: '确定',
								success: function(res) {
									if (res.confirm) {
									}
								}
							})
						}
					}
				})
			} else {
				this.$rauter.navigateTo({
					url: 'orderDetail'
				});
			}
		}
	}
};
</script>

<style>
.recodechange {
	background: #fff;
	padding: 10upx 40upx;
}
.toprecodechange > :nth-child(n + 2) {
	margin-top: 20upx;
}
.red {
	color: red;
}
/deep/ uni-view {
	font-size: 28upx;
	line-height: 72upx;
}
/deep/.uni-forms-item__inner {
	display: flex;
	padding-bottom: 10upx;
}
/deep/.uni-forms-item__label {
	display: flex;
	flex-shrink: 0;
	box-sizing: border-box;
	flex-direction: row;
	align-items: center;
	width: 130upx;
	/* padding: 5px 0; */
	height: 72upx;
	margin-right: 40upx;
}
</style>
