<!-- 订单详情界面 -->
<template>
	<c-layout title="订单详情">
		<view class="damages-detail-box">
			<c-form :disabled="formDisabled" :form-config="formConfig" :form-data="formData" label-width="36%" f-padding="0 20rpx"></c-form>
			<button class="next-btn" v-if="needPay" @click="pay">支付</button>
			<button class="next-btn" v-else @click="skipIndexPage">返回首页</button>
			<button class="next-btn" v-if="false" type="warn">删除</button>
		</view>
	</c-layout>
</template>

<script>
import CForm from '../../components/form/CForm';
import { toName } from '../../enums/enums';
import CLayout from '../../components/layout/CLayout';
import OrderDetailBean from "@/common/js/bean/OrderDetailBean.js"
import { mapGetters, mapActions } from 'vuex';
export default {
	name: 'orderDetail',
	components: {
		CLayout,
		CForm
	},
	data() {
		return {
			formDisabled: false,
			cpcode: '',
			poid: '',
			formData: {
				ordercode: '--/--',
				opcodename: '--/--',
				orderstatusname: '--/--',
				optime: '--/--',
				custid: '--/--',
				custname: '--/--',
				fees: '--/--',
				paystatusname: '--/--'
			},
			formConfig: [
				{
					label: '订单编号',
					key: 'ordercode',
					type: 'text'
				},
				{
					label: '订单类型',
					key: 'opcodename',
					type: 'text'
				},
				{
					label: '订单状态',
					key: 'orderstatusname',
					type: 'text'
				},
				{
					label: '操作时间',
					key: 'optime',
					type: 'text'
				},
				{
					label: '客户编号',
					key: 'custid',
					type: 'text'
				},
				{
					label: '客户姓名',
					key: 'custname',
					type: 'text'
				},
				{
					label: '订单金额',
					key: 'fees',
					type: 'text'
				},
				{
					label: '支付状态',
					key: 'paystatusname',
					type: 'text'
				}
			]
		};
	},
	onLoad(data) {
		if (data) {
			this.params = data.params
			this.poid = data.poid
			this.cpcode = data.currentStep
		}
	},
	async onShow(data) {
		// 查询登录用户信息，并缓存到store里去重要  新添加解决city字段无法获取问题lzt 20220823
		await this.queryUserInfo();
		await this.loadOrder({
			orderId: this.custOrderId
		});
		this.initFormData()
	},
	computed: {
		...mapGetters({
			userInfo: 'getUserInfo',
			custInfo: 'getCustInfo',
			orderInfo: 'getOrderInfo'
		}),
		custOrderId() {
			return this.orderInfo.custorderid;
		},
		//判断是否已支付，0是未支付
		needPay() {
			return this.orderInfo.paystatus == 0;
		}
	},

	methods: {
		...mapActions({
			loadOrder: 'loadOrder',
			queryUserInfo: 'queryUserInfo'
		}),

		pay() {
			const vm = this
			this.$rauter.navigateTo({
				url: 'orderPay',
				data: {
					navigateTo: 'orderDetail',
					params: vm.params,
					poid: vm.poid,
					currentStep: vm.cpcode
				}
			});
		},

		wuzhihua() {
			let serialno = this.orderInfo.bossserialno;
			window.baseAppJs.jumpActivityFromJsWithParams(this.custInfo.custid + '', 'com.maywide.cmms.gcableboss.fg.wuzhihua.AgreementActivity', "{serialno:'" + serialno + "'}");
		},
		initFormData() {
			this.$nextTick(() => {
				for (let key in this.formConfig) {
					const bean = this.formConfig[key]
					this.formData[bean.key] = this.orderInfo[bean.key] ? this.orderInfo[bean.key] : this.formData[bean.key]
				}
			})
		},
		skipIndexPage() {
			this.$rauter.navigateTo({
				url: "FifthG",
			});
		}
	}
};
</script>

<style scoped>

.next-btn {
  margin: 80rpx 30rpx;
  background-color: rgb(0, 132, 255);
  color: #fff;
}
.history-btn {
  color: rgb(0, 132, 255);
  border-bottom: 1px solid rgb(0, 132, 255);
}
.pay-code-mask-box {
  position: fixed;
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  background-color: black;
  z-index: 1001;
  -moz-opacity: 0.8;
  opacity: .80;
  filter: alpha(opacity=88);
}
.pay-code-box {
  position: fixed;
  top: 20%;
  width: 80%;
  height: 50%;
  border: 10px solid white;
  background-color: white;
  z-index: 1002;
  overflow: auto;
}
.pay-code-title {
  height: 2em;
  line-height: 2em;
  text-align: center;
  font-weight: bold;
}
</style>
