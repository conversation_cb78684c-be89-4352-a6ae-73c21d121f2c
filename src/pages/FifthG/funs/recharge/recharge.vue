<template>
  <view class="wrap">
    <view class="content">
      <view class="basic-body">
        <view class="popup">
          <common-popup :mcode.sync="phone" :options="phoneList" :title="'手机号'" :tip="'请选择手机号'"
                        @getData="getPhoneData"></common-popup>
        </view>
      </view>
      <view class="charge-box">
        <view class="charge-body">
          <view class="chargeList">
            <view class="charge-li" :class="{'select':curChargeIndex == index}" v-for="(item,index) in chargeList" :key="index" @click="selectCharge(item,index)">{{ item.mname }} <span class="unit">元</span> </view>
          </view>
          <view class="input-body" @click="inputBodyFn">
            <input type="number" placeholder="输入充值金额，最低1元" class="input" v-model="inputFee" @input="handleInput" onfocus="this.placeholder=''" onblur="this.placeholder='输入充值金额，最低1元'">
          </view>
        </view>
      </view>

      <view class="details-card">
        <developer-selector class="card-dev" @changeDeveloper="changeDeveloper"></developer-selector>
      </view>

      <view class="btn-submit">
        <button class="btn" :class="{'disabled':disabled}" :disabled="disabled" @click="doFeeInCharge">{{ fee }}元 立即支付</button>
      </view>

      <view class="record" @click="recordchange">充值记录</view>
    </view>



    <modalPopup ref="modalPopup" msgContent="该用户不存在5G移网业务，无法办理此业务" confirmText="确定" :isShowCancel="false" @confrim="closeModalPopup"></modalPopup>
  </view>
</template>
<script>
import commonPopup from "../../components/common/commonPopup.vue";
import modalPopup from '../../components/common/modalPopup.vue'
import {mapActions, mapGetters, mapMutations} from "vuex";
import DeveloperSelector from "../../components/common/developerSelector.vue";

export default {
  name: 'ywRecharge',
  data () {
    return {
      phoneList: [],
      developerName: '',
      fee: '10',
      inputFee:null,
      curChargeIndex:0,
      chargeList:[],
      disabled:false,
      phone: '',
      feebooksums:'0',
      developerInfo:{}
    }
  },
  components: {
    DeveloperSelector,
    commonPopup,
    modalPopup
  },
  mounted () {
    this.loadData()
    this.initCharges()
  },
  watch:{
    fee(newValue){
      console.log('new',newValue);
      if(newValue == '0'){
        this.disabled = true
      }else{
        this.disabled = false
      }
    }
  },
  computed: {
    ...mapGetters({
      userInfo: 'getUserInfo',
      custInfo: 'getCustInfo',
      orderInfo: 'getOrderInfo'
    }),
    custOrderId() {
      return this.orderInfo?.custOrderid || '';
    }
  },
  methods: {
    ...mapMutations({
      setOrderInfo: 'setOrderInfo',
      resetOrderInfo: 'resetOrderInfo',
      resetStepInfo: 'resetStepInfo'
    }),
    ...mapActions({
      loadOrder: 'loadOrder'
    }),
    getDeveloper(data) {
      console.log('getDeveloper',data)
      this.developerInfo = data
    },
    getPhoneData(data) {
      this.phone = data ? data.mcode : null
    },
    async loadData() {
      this.loadPhones()
    },
    async initCharges(){
      const {responseBody: {paramdata}} = await this.$http.post('getData', {gcode: 'SYS_RECHARGE'})
      console.log('SYS_RECHARGE',paramdata)
      paramdata.sort((a,b)=>a.mcode - b.mcode)
      this.chargeList = paramdata
    },
    loadPhones() {
      const vm = this
      if (vm.custInfo && vm.custInfo.servs && vm.custInfo.servs.length) {
        vm.custInfo.servs.forEach(val => {
          // servtype : 0是主卡 1是副卡
          if (val.mobileno && val.servtype === '0') {
            let item = {
              mcode: val.mobileno,
              mname: val.mobileno
            }
            this.phone = val.mobileno
            this.phoneList.push(item)
          }
        })
      }
      console.log('phoneList',this.phoneList)
      if(this.phoneList && this.phoneList.length<=0){
        this.$refs.modalPopup.show()
      }
    },
    handleInput(event){
      console.log('event',event.detail.value);
      if(!event.detail.value){
        this.fee = 0
      }else{
        this.fee = event.detail.value
      }
      this.curChargeIndex = -1
    },
    inputBodyFn(){
      document.querySelector('.input').focus()
      this.curChargeIndex = -1
      this.fee = 0
    },
    selectCharge(item,index){
      this.inputFee = null
      this.fee = item.mcode
      this.curChargeIndex = index
    },
    async doFeeInCharge () {
      console.log('this.fee',this.fee, typeof this.fee)
      if (!this.fee || !this.fee.trim()) {
        uni.showToast({
          title: '请输入充值金额',
          icon: 'none'
        })
        return
      }

      if (!this.phone){
        uni.showToast({
          title: '请选择手机号',
          icon: 'none'
        })
        return
      }

      if (isNaN(this.fee)) {
        uni.showToast({
          title: '请输入有效的数字',
          icon: 'none'
        })
        return
      }

      if (parseFloat(this.fee) < 0.01 || parseFloat(this.fee) > 9999999) {
        uni.showToast({
          title: '充值金额不在有效范围内',
          icon: 'none'
        })
        return
      }

      if (this.custInfo && this.custInfo.servs) {
        let servDetailObj;
        for (let i = 0, l = this.custInfo.servs.length; i < l; i++) {
          const item = this.custInfo.servs[i];
          if (item.servDetails) {
            servDetailObj = item.servDetails.find(sd => {
              return sd.keyno === this.phone && sd.permark === 'B' && sd.servstatus === '1';
            });
            if (servDetailObj) {
              break;
            }
          }
        }
        if (servDetailObj) {
          uni.showToast({
            title: '国网开户不成功不允许充值，请联系管理员',
            icon: 'none'
          });
          return;
        }
      }

      let processReq = {
        "pcode": 'RECHARGE',
        "custid": this.custInfo ? this.custInfo.custid : "",
        "custname": this.custInfo ? this.custInfo.custname : "",
        "salesid": '',
        "salesname": ''
      }

      let processData = {
        data: JSON.stringify({
          custid: this.custInfo?.custid,
          mobileno: this.phone,
          custname: this.custInfo?.custname,
          fee: String(this.fee)
        }),
        temp: '1'
      }

      let data = {
        custid: this.custInfo?.custid,
        mobileno: this.phone,
        custname: this.custInfo?.custname,
        fee: String(this.fee), // 原本fee改成amount，改成实际交付的金额
        processDataReq: processData,
        processReq: processReq,
        developer: {
          type: this.developerInfo.type,
          devname: this.developerInfo.devname,
          place: this.developerInfo.place,
          devid: this.developerInfo.id,
        }

      };

      this.params = {
        phone: this.phone,
        amount: String(this.fee),
        fee: String(this.fee),
        discount: 1
      }

      this.$http.post(this.$apis.m5gPhoneRecharge, data).then(response => {
        console.log('m5gPhoneRecharge', response)
        let res = response.responseBody;
        if (res && Object.keys(res).length && res.custOrderid) {
          if (!res.custorderid) res.custorderid = res.custOrderid;
          if (!res.fees) res.fees = res.sums;
          this.setOrderInfo(res);
          // 充值跳转，清空业务开通的信息
          this.resetStepInfo();
          let nextPage = res.nextPage
          let poid = res.poid
          let cpcode = res.cpcode
          // 跳转到完成提示页面
          // 判断返回的金额，如果金额为0，直接就提交订单
          if (nextPage) {
            // 跳转到下一步
            this.$rauter.navigateTo({
              url: nextPage,
              data: {
                poid: poid,
                currentStep: cpcode,
                params: this.params
              }
            });
          } else {
            this.$rauter.navigateTo({
              url: 'orderDetail',
              data: {
                params: this.params
              }
            });
          }
        }
      })
    },
    closeModalPopup(){
      this.$refs.modalPopup.close()
      this.$rauter.navigateTo({
        url:'/pages/index/toolBox'
      })
    },
    changeDeveloper(data){
      console.log('changeDeveloper',data)
      this.developerInfo = data
    },
    recordchange() {
      this.$rauter.navigateTo({
        url: 'recordchange'
      });
    },
  }
}
</script>

<style lang="scss" scoped>

page {
  background-color: #eff1f8;
  height: 100%;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', miui, 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  background: linear-gradient(180deg,#2b71ff,rgba(31,143,247,0) 30%) #f8f8f8;
  overflow: auto;
}

.basic-body{
  //background: linear-gradient(180deg,#2b71ff,rgba(31,143,247,0) 30%) #f8f8f8;
  //padding:30rpx 30rpx 0;
  //box-sizing: border-box;
}

.charge-box{
  box-sizing: border-box;
  margin-top: 20rpx;
}
.charge-body{
  min-height: 45.6rpx;
  border-radius:10rpx;
  background: #FFFFFF;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.02);
  padding: 30rpx;
  box-sizing: border-box;
}
.chargeList{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .charge-li{
    width: 196rpx;
    height: 90rpx;
    border-radius: 6rpx;
    background: #F3F3F3;
    margin-bottom: 30rpx;
    text-align: center;
    line-height: 90rpx;
    font-size: 46rpx;
    color: #1F8FF7;
    font-weight: bold;
    .unit{
      font-size: 24rpx;
    }
  }
  .select{
    background: #1F8FF7;
    color: #FFFFFF;
  }
}
.input-body{
  height: 90rpx;
  border-radius: 60;
  background: #F3F3F3;
  .input{
    border: none;
    background: #F3F3F3;
    height: 90rpx;
    line-height: 90rpx;
    padding: 0;
    margin: 0;
    color: #212121;
    text-align: center;
    font-size: 32rpx;
  }
}
.tip{
  margin-top: 20rpx;
  text-align: left;
  .tip-sp{
    font-size: 24rpx;
    line-height: 46rpx;
    color: #9E9E9E
  }
  .red{
    color: #F62828
  }
}

.btn-submit{
  // padding: 0 30rpx;
  box-sizing: border-box;
  margin-top: 80rpx;
  .btn{
    width: 100%;
    height: 90rpx;
    border-radius: 45rpx;
    background: #1F8FF7;
    line-height: 90rpx;
    text-align: center;
    font-size: 32rpx;
    color: #FFFFFF;
  }
  .disabled{
    background: #212121;
    opacity: .3;
  }
}

.popup {
  background-color: #FFFFFF;
  padding: 0 28rpx;
  border-radius: 10rpx;
  border-bottom: unset;
}

.develop-body{
  box-sizing: border-box;
  margin-top: 20rpx;
  .card-body{
    border-radius: 10rpx;
    background: #FFFFFF;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.03);
    padding: 30rpx;
    box-sizing: border-box;
  }
}
::v-deep .card-item .card-right{
  flex: 2;
}

.details-card{
  border-radius: 10rpx;
  background: #FFFFFF;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.03);
  padding:0 30rpx;
  box-sizing: border-box;
  margin-top: 20rpx;

  .card-item{
    display: flex;
    align-items: center;
    border-bottom: 1rpx solid #D8D8D8;
    // height: 80rpx;
    padding: 30rpx 0;
    box-sizing: border-box;
    .card-left{
      flex: 1;
      font-size: 28rpx;
      line-height: 28rpx;
      color: #212121;
      text-align: left;
    }
    .card-right{
      flex: 1;
      border: none;
      background: #FFFFFF;
      font-size: 28rpx;
      color: #8C8C8C;
      text-align: left;
      display: flex;
      align-items: center;
      .card-input{
        border: none;
        background: #FFFFFF;
        font-size: 28rpx;
        color: #212121;
        width: 100%;
      }
      .sp-post-on{
        width: 100rpx;
        height: 50rpx;
        line-height: 50rpx;
        border-radius: 25rpx;
        border: 1px solid #8C8C8C;
        text-align: center;
        margin: 0;
        margin-right: 10rpx;
      }
      .sp-post-select{
        background: #FFF5E5;
        border: 1px solid #FFAE35;
        color: #F9A63F;
      }
      .date-sty{
        width: 300rpx;
        height: 30rpx;
      }
    }
    .time-right{
      justify-content: space-between;
    }
    .icon-right{
      font-size: 24rpx;
    }
  }
  .card-item:last-child{
    border-bottom: none;
  }
}

.card-dev{
  padding: 20rpx 0;
}

.record {
  text-align: center;
  text-decoration: underline;
  color: #1F8FF7;
  width: 30%;
  margin: 0 auto;
  margin-top: .8rem;
}

</style>
