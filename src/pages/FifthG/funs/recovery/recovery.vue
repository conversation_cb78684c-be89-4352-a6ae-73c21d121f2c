<template>
  <c-layout title="复机">
    <view class="recovery-box">
      <view class="phone-query-box">
        <view>手机号码</view>
        <view>
          <picker
              class="phone-picker-box"
              @change="onPhoneChange"
              :value="selectPhone"
              :range="phoneList">
            <view class="select-phone-box">
              <text class="fc9">{{selectPhone}}</text>
              <text class="iconfont icon-xia"></text>
            </view>
          </picker>
        </view>
        <view>
          <button class="query-detail-btn" @click="onRecovery()">复机</button>
        </view>
      </view>

      <view class="result-box" v-if="showResult">
        <view class="result-desc-box">
          <view><i class="iconfont icon-zhengque result-desc-icon"></i></view>
          <view class="result-desc">复机成功</view>
        </view>
        <view class="result-info-box" >
          <view class="result-info-item">
            <view class="result-info-item-label">省BOSS订单标识</view>
            <view>{{ result.order_id }}</view>
          </view>
          <view class="result-info-item">
            <view class="result-info-item-label">国网订单流水号</view>
            <view>{{ result.done_code }}</view>
          </view>
          <view class="result-info-item">
            <view class="result-info-item-label">手机号</view>
            <view>{{ result.access_num }}</view>
          </view>
        </view>
      </view>

    </view>
  </c-layout>

</template>

<script>
import '../../../../static/iconfont/iconfont.js'
import { mapGetters } from "vuex";
import CNoData from "../../components/list/CNoData";
import CLayout from "../../components/layout/CLayout";

export default {
  name: "DamagesQuery",
  components: {CLayout, CNoData},
  data() {
    return {
      // 用户手机号
      phoneList: [],
      // 选择的手机号
      selectPhone: "",
      phoneServId: {},
      queryUserInfo: {},
      phoneMap: {},
      phone: "",
      result: {
        order_id: '',
        done_code: '',
        access_num: ''
      },
      showResult: false
    };
  },
  computed: {
    ...mapGetters({
      custInfo: 'getCustInfo',
      userInfo: "getUserInfo"
    }),
  },

  methods: {
    /**
     * 获取选中违约金
     */
    onPhoneChange(val) {
      this.selectPhone = this.phoneList[val.detail.value]
      this.showResult = false
    },
    /**
     * 查询手机号违约金
     */
    onRecovery() {
      this.onCheckUserStatus()
    },
    onCallRecovery() {
      const vm = this
      if (vm.phone) {
        vm.$http.post(vm.$apis.callBoss, {
          service: vm.$serviceId.BIZ_FGB_RESTORESUBSCRIBER,
          params: {
            querytype: 'B', // 目前只支持B，5G移动业务手机号码
            queryvalue: vm.phone
          }
        }).then(resp => {
          if (resp.responseBody.data.status == '400') {
            uni.showToast({
              icon: "none",
              title: resp.responseBody.data.message,
            });
          } else {
            vm.result =  resp.responseBody.data.output
            vm.showResult = true
          }
        })
      } else {
        uni.showToast({
          title: '请选择手机号！',
          icon: 'none'
        })
      }
    },
    // 校验是否欠费
    onCheckArrearages() {
      const vm = this
      if (vm.selectPhone) {
        // 切割字符串
        vm.$http.post(vm.$apis.callBoss, {
          service: vm.$serviceId.CHECK_OWNFEE_AND_UNFIBISHORDER,
          params: {
            serv_id: vm.phoneServId[this.selectPhone],
          }
        }).then(resp => {
          if (resp.responseBody.data.output) {
            /**
             * 0-校验通过
             * 1-存在欠费
             * 2-存在在途工单
             * 3-存在违约金
             * 4-同时存在欠费和违约金
             */
            const checkResult = resp.responseBody.data.output.check_result
            if (checkResult == '0') {
              // 复机
              vm.onCallRecovery()
            } else if (checkResult == '1') {
              uni.showToast({
                title: '存在欠费，无法复机！',
                icon: 'none'
              })
            } else if (checkResult == '2') {
              uni.showToast({
                title: '存在在途工单，无法复机！',
                icon: 'none'
              })
            } else if (checkResult == '3') {
              uni.showToast({
                title: '存在违约金，无法复机！',
                icon: 'none'
              })
            } else if (checkResult == '4') {
              uni.showToast({
                title: '同时存在欠费和违约金，无法复机！',
                icon: 'none'
              })
            }
          }

        });
      } else {
        uni.showToast({
          title: '请选择手机号！',
          icon: 'none'
        })
      }
    },
    // 校验用户状态
    onCheckUserStatus(call) {
      const vm = this
      console.log(vm.selectPhone)
      if (this.selectPhone) {
        // 切割字符串
        const phone = this.selectPhone.split("-")[0]
        // 获取手机号
        vm.phone = phone

        // 查询用户状态
        let info = vm.phoneMap.get(phone)
        if (info && info.state && info.state === 'C') {
          // 校验欠费状态
          this.onCheckArrearages()
        } else {
          uni.showToast({
            title: '当前用户未预约销户，不需要复机！',
            icon: 'none'
          })
        }

      } else {
        uni.showToast({
          title: '请选择手机号！',
          icon: 'none'
        })
      }
    },
    /**
     * 获取客户手机号
     */
    loadPhones() {
      const vm = this
      if (vm.custInfo && vm.custInfo.servs && vm.custInfo.servs.length) {
        let phoneList = []
        vm.custInfo.servs.forEach(val => {
          console.log(val)
          if (val.mobileno) {
            // servtype ： 0是主卡 1是副卡
            let type = ''
            if (val.servtype === '0') {
              type = '主卡'
            } else if(val.servtype === '1') {
              type = '副卡'
            }
            phoneList.push(val.mobileno + '-' + type)
            vm.phoneServId[val.mobileno + '-' + type] = val.servid
          }
        })
        vm.phoneList = [...phoneList]
        vm.selectPhone = phoneList[0]
      }
    },
    // 加载手机号
    onLoadPhone() {
      const vm = this
      uni.showLoading({
        title: '请稍候...'
      })
      vm.$http.post(vm.$apis.m5gQueCustinfo, { custId: vm.custInfo.custid })
          .then((resp) => {
            vm.queryUserInfo = resp.responseBody
            if (vm.queryUserInfo.userInfos && vm.queryUserInfo.userInfos.length > 0) {
              vm.phoneMap = new Map()
              vm.queryUserInfo.userInfos.forEach(info => {
                if (info.servId) {
                  vm.phoneMap.set(info.accessNum,info)
                }
              })
              uni.hideLoading()
            }
          }).catch((err) => {
        uni.showToast({
          title: err.returnInfo.message,
          icon: 'none'
        })
      })
    },
  },
  created() {
    this.loadPhones()
    this.onLoadPhone()
  },
}
</script>

<style lang="scss" scoped>

.result-box {
  background-color: #fff;
  margin-top: 1em;
  text-align: center;
  .result-desc-box {
    padding-top: 3em;
    padding-bottom: 1em;
    .result-desc-icon {
      font-size: 4em;
      color: green;
    }
    .result-desc {
      font-size: 1.2em;
    }
  }

  .result-info-box {
    .result-info-item {
      display: flex;
      justify-content: space-between;
      padding: 0.2em 1em;
      .result-info-item-label {
        color: #999999;
      }
    }
  }


}

.phone-query-box {
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
}
.phone-picker-box {
  height: 2em;
  width: 50vw;
  border: 1px solid #CCCCCC;
  border-radius: 10rpx;
}
.query-detail-btn {
  height: 60rpx;
  font-size: 24rpx;
  background-color: rgb(0, 132, 255);
  color: #fff;
  text-align: center;
  line-height: 60rpx;
}
.select-phone-box {
  display: flex;
  justify-content: space-between;
  padding: 0 14rpx;
}
.detail-info-box {
  text-align: center;
  background-color: #fff;
  margin-top: 30rpx;
  padding-top: 4em;
  padding-bottom: 20rpx;
}
.pay-btn {
  font-size: 1.2em;
  background-color: rgb(0, 132, 255);
  color: #fff;
  text-align: center;
  font-weight: bold;
  width: 50vw;
  margin-top: 3em;
  margin-bottom: 2em;
}
.total-amount-box {
  font-size: 2.8em;
  font-weight: bold;
}
.detail-box {
  padding: 0 60rpx;
}
.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 5rpx 0;
}
.item-fs {
  font-size: 1.2em;
}
.fc9 {
  color: #999999;
}
.detail-title {
  border-bottom: 1px solid #CCCCCC;
  color: #333333;
}

</style>
