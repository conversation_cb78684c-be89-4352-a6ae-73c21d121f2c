<!-- 流程界面 -->
<template>
  <c-layout title="以固促移">
    <div class="layout">
      <view class="cust-content">
        <view class="cust-left"></view>
        <view class="cust-right">
          <view class="cust-top">
            <view class="custname">{{ custInfo.custname }}</view>
            <view class="custid">{{ custInfo.custid }}</view>
          </view>
          <view class="custaddr" v-if="custInfo.linkaddr">{{ custInfo.linkaddr }}</view>
        </view>
      </view>

      <view class="prompt">
        <view class="line left"></view>
        <view class="prompt-text">请按照以下指引为客户开户</view>
        <view class="line right"></view>
      </view>
      <view class="step-group">
        <view class="step-item" :class="{finish: stepBean.done, expire: !stepBean.done}" v-for="(stepBean, index) in stepInfo.process" :key="index">
          <div class="summary" @click="onClickStepBean(stepBean, index, stepInfo.process)">
            <div class="step-icon"></div>
            <div class="step-name">{{ stepBean.name }}</div>
            <div class="step-right"></div>
          </div>
          <div class="body">
            <div class="step-line" v-if="index != stepInfo.process.length - 1"></div>
            <div class="ctn">
              <div class="realname-prompt" v-if="!stepBean.done">{{ stepBean.tips }}</div>
              <div class="step-content" v-if="stepBean.extData && stepBean.done">
                <div class="selected-package" v-for="(n, i) in stepBean.extData" :key="i" v-if="n.value">
                  <div class="text1">{{ n.label }}</div>
                  <div class="text2">{{ n.value }}</div>
                </div>
              </div>
            </div>
          </div>
        </view>
      </view>

      <!-- 暂时不需要放弃办理 -->
      <view class="giveup-btn" v-if="false">放弃办理</view>

      <van-popup class="popup" v-model="showAuth">
        <view class="title">请选择认证方式</view>
        <view class="ctn">
          <view class="type" :class="'t' + n.value" v-for="n, i in tabs" :key="i" @click="toVerifiedPage(n)">
            <view class="h1">{{ n.name }}</view>
            <view class="icons">
              <view v-if="n.value != '1'" class="reader">读卡器</view>
              <view class="idcard">身份证</view>
            </view>
          </view>
        </view>
      </van-popup>
    </div>
  </c-layout>
</template>

<script>
import Vue from 'vue'
import {
  mapGetters,
  mapActions,
  mapMutations
} from 'vuex';
import CLayout from "../../components/layout/CLayout";
import { Popup, Step, Steps } from 'vant'
Vue.use(Popup)
Vue.use(Step)
Vue.use(Steps)

import {
  v4 as uuidv4
} from 'uuid';
export default {
  data() {
    return {
      showAuth: false,
      tabs: [],
      verifiedData: {},
      textProcess: [
        {
          code: 'GDM_GWDX',
          tips: '请选择固网用户',
          err: null,
          extData: [
            { label: '固网用户', data: {}, value: '', key: 'broadband' },
          ]
        },
        {
          code: 'GDM_XZTC',
          tips: '请选择需要办理的移网套餐',
          err: null,
          extData: [
            { label: '已选套餐', data: {}, value: '', key: 'salesname' },
            { label: '促销套餐', data: {}, value: '', key: 'knowname' },
          ]
        },
        {
          code: 'GDM_M5G',
          tips: '号码选占时间为10分钟，请尽快完成办理',
          err: null,
          extData: [
            { label: '已选号码', data: {}, value: '', key: 'resNum' },
            { label: 'SIM卡号', data: {}, value: '', key: 'sim' },
          ]
        },
        {
          code: 'GDM_ID',
          tips: '实名认证有效期仅60分钟，请尽快完成业务办理',
          err: null,
          extData: [
            { label: '实名认证', data: {}, value: '', key: 'name' },
            { label: '身份证号', data: {}, value: '', key: 'desens' },
          ]
        },
        {
          code: 'BLOCK',
          tips: '请确认境外来话来信拦截服务',
          err: null,
          extData: null,
        },
        {
          code: 'GDM_TJYW',
          tips: '业务鉴权及订单生成',
          err: null,
          extData: null,
        },
        {
          code: 'GDM_SIGN',
          tips: '业务受理单签署确认',
          err: null,
          extData: null,
        },
        {
          code: 'GDM_PAY',
          tips: '请用户扫码或现金支付完成订单',
          err: null,
          extData: null,
        }
      ]
    };
  },
  components: { CLayout },

  computed: {
    ...mapGetters({
      custInfo: "getCustInfo",
      salesInfo: 'getSalesInfo',
    }),
    stepInfo() {
      const process = []
      const getStepInfo = {...this.$store.getters.getStepInfo}
      if(getStepInfo.process) {
        getStepInfo.process.forEach((n,i) => {
          const [bean] = this.textProcess.filter(m => m.code == n.code)
          if(bean.extData) {
            bean.extData.map(m => {
              m.value = n.data[m.key]
            })
          }
          process[i] = {...n, ...bean}
        })
        getStepInfo.process = process
      }

      return getStepInfo
    },
    productTip() {
      let productTip = "您已选择了移动套餐"
      if (this.salesInfo && this.salesInfo.offertypename) {
        productTip = "您已选择了" + this.salesInfo.offertypename + "套餐"
      }
      return productTip
    },
    isComplete() {
      let stepProcess = this.stepInfo.process
      return stepProcess[stepProcess.length - 1].done
    },
    isPay() {
      let stepProcess = this.stepInfo.process
      if (!stepProcess) {
        return
      }
      for (let processBean of stepProcess) {
        if (processBean.code == 'PAYCONF') {
          return processBean.done
        }
      }
      return false;
    }
  },

  mounted() {
    localStorage.removeItem('GServe')
    this.initData()

  },

  methods: {
    ...mapActions({
      queProcess: 'queProcess',
      needAppAgreement: 'needAppAgreement',
      saveProcess: 'saveProcess',
    }),

    ...mapMutations({
      resetStep: "resetStepInfo",
      resetSubmitData: "resetSubmitData"
    }),

    async initData() {
      await this.queProcess({poid: this.$route.query.poid})
      await this.checkOrder()
      if(this.isComplete) {
        uni.showModal({
          title: '温馨提示',
          content: '业务办理已完成，返回工具箱',
          showCancel: false,
          cancelColor: '#576B95',
          success: (res) => {
            console.log(res)
            if(res.confirm) {
              this.$rauter.reLaunch({
                url: "FifthG"
              });
            }
          }
        });
      }
    },
    toVerifiedPage(item) {
      this.$rauter.navigateTo({
        url: item.url,
        data: {
          ...this.verifiedData,
          type: item.value,
        },
      })
    },
    qryAuthIdetifyType() {
      const vm = this
      const names = {
        '0': {
          name: "读卡器认证",
          url: 'verifiedID'
        },
        '1': {
          name: "国网H5实名认证",
          url: 'verifiedID'
        },
        '2': {
          name: "读卡器实名认证",
          url: 'verifiedID'
        }
      }
      vm.$http.post(vm.$apis.qryAuthIdetifyType, {}).then(resp => {
        console.log(resp)
        if (resp && resp.responseBody.type) {
          const type = resp.responseBody.type
          type.split('~').forEach(item => {
            vm.tabs.unshift({
              ...names[item],
              value: item
            })
          })
        }
      });
    },
    isStepActivity(stepBean) {
      return stepBean.done ? {
        color: '#52C41A'
      } : {
        color: '#F5222D'
      };
    },
    getIcon(stepBean) {
      return stepBean.done ? '&#xe730;' : '&#xe655';
    },
    async onClickStepBean(stepBean, index, allProcess) {
      const expireIdx = allProcess.findIndex(n => !n.done)
      console.log('stepBean,', stepBean, index, allProcess, expireIdx, expireIdx < index);
      if(process.env.RUN_ENV != 'local') {
        if(expireIdx >= 0 && index != 0 && expireIdx < index) {
          uni.showToast({
            title: '请按照流程完成步骤',
            icon: 'none'
          })
          return
        }
      }

      this.$rauter.navigateTo({
        url: stepBean.page,
        data: {
          poid: stepBean.poid,
          code: stepBean.code,
          cpcode: stepBean.code,
          currentStep: stepBean.code,
          // serialno: this.stepInfo.data.GDM_PAY.bossserialno,
          serialno: this.stepInfo.data.GDM_TJYW.serialno,// 流程节点顺序改变,取GDM_TJYW中的流水号
          custOrderId: this.stepInfo.data.GDM_TJYW.custOrderid
        }
      })

    },

    rePickProd() {
      if (this.isComplete) {
        //已完成，不能继续办理业务
        return;
      }
      this.$rauter.navigateTo({
        url: "productsSelect",
        data: {
          delta: 2
        }
      });
    },
    checkOrder() {
      // 判断当前流程到支付确认环节，且未完成
      var payStep = null
      if (this.stepInfo.process) {
        for (let i = 0; i < this.stepInfo.process.length; i++) {
          var step = this.stepInfo.process[i];
          if (step.code === 'GDM_PAY') {
            if (step.pstatus * 1 !== 2) {
              if (i > 0) {
                var preStep = this.stepInfo.process[i - 1]
                if (preStep.done === true) {
                  payStep = step
                }
              } else {
                payStep = step
              }
            }
          }
        }
      }
      if (payStep && this.stepInfo?.data?.GDM_TJYW?.custOrderid) {
        let custOrderId = this.stepInfo?.data?.GDM_TJYW?.custOrderid || '';
        let payway = this.stepInfo?.data?.GDM_TJYW?.payway || '';
        // if(payway!='H' && payway!='G'){
        // 	return;
        // }

        this.$http.request({
          method: 'POST',
          api: this.$apis.queQdUnifyPayResultInfo,
          url: this.$baseUrl,
          isAutoLoading: false,
          data: { custorderid: custOrderId }
        }).then(resp => {
          let isPay = resp?.responseBody?.data?.cmms_status == '7'
          let isConfirm = resp?.responseBody?.data?.cmms_status == '200'
          if (isPay) {
            this.stepInfo.data.GDM_TJYW.cmms_status = '7';
            this.payOrder(payway)
          } else if (isConfirm) {
            this.stepInfo.data.GDM_TJYW.cmms_status = '200';
            let step = this.stepInfo.process.filter(o => {
              return o.code === 'GDM_PAY'
            })
            if (step && step.pstatus * 1 !== 2) {
              this.saveData()
            }
          }
        });
      }
    },
    payOrder(payway) {
      let paycode = "000000";
      let payreqid = uuidv4();
      let params = {
        paycode: paycode,
        payreqid: payreqid,
        payway: payway,
      }
      if (this.stepInfo?.data?.GDM_TJYW?.custOrderid) {
        //实时开户的支付
        this.commitOpenAccount(params)
        return;
      }
    },
    //实时开户的确认
    commitOpenAccount(params) {
      params.multipaywayflag = "N"
      params.custOrderid = this.stepInfo?.data?.GDM_TJYW?.custOrderid
      params.poid = this.stepInfo?.poid
      this.$http.post(this.$apis.commitOpenAccount, params).then(resp => {
        this.saveData();
      });
    },
    saveData() {
      let saveData = {
        "desc": "自动补单"
      };
      this.saveProcess({
        data: {
          "poid": this.stepInfo.poid,
          "cpcode": "GDM_PAY",
          "data": JSON.stringify(saveData),
          "temp": "1"
        },
        refreshCustInfo: true,
        custid: this.custInfo.custid,
        unNeedNext: true
      })
    },
  },
}
</script>

<style lang='scss' scoped>
.layout {
  padding: 10rpx 0;
}

.select-prod-group {
  background-color: #FFFFFF;
  padding: 30rpx 30rpx;

  .select-prod-msg {
    position: relative;
    margin-top: 20rpx;

    .product-name {
      font-weight: bold;
      height: 120rpx;
      line-height: 1.5;
      cursor: pointer;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3; // 数字代表文字需要显示几行
    }

    .product-price {
      position: absolute;
      line-height: 1;
      color: #F5222D;
      margin-top: 15rpx;
      bottom: 0;
    }

    .product-img {
      width: 160rpx;
      height: 160rpx;
      /* position: absolute;
				right: 0;
				top: 0;
				bottom: 0; */
    }
  }

}

.cust-content {
  display: flex;
  align-items: flex-start;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 20rpx;
  background-color: #FFFFFF;
  box-sizing: border-box;

  .cust-left {
    width: 64rpx;
    height: 64rpx;
    background: url('../../../../static/fifthg/imgs/icon_avatar.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 80rpx 80rpx;
  }

  .cust-right {
    padding-left: 8rpx;

    .cust-top {
      display: flex;
      align-items: flex-end;
      color: #212121;

      .custname {
        margin-right: 20rpx;
        font-size: 32rpx;
        font-weight: bold;
        line-height: 64rpx;
      }

      .custid {
        font-size: 28rpx;
        line-height: 64rpx;
      }
    }

    .custaddr {
      color: #999;
      font-size: 28rpx;
      line-height: 40rpx;
    }
  }
}

.prompt {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20rpx;

  .prompt-text {
    margin: 0 20rpx;
    font-size: 28rpx;
    color: #999;
  }

  .line {
    flex: 1;
    min-width: 30rpx;
    height: 4rpx;
    &.left {
      background: linear-gradient(-90deg, rgb(31, 143, 247), rgba(31, 143, 247, 0));
    }
    &.right {
      background: linear-gradient(90deg, rgb(31, 143, 247), rgba(31, 143, 247, 0));
    }
  }
}

.step-group {
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  background-color: #FFFFFF;
  box-sizing: border-box;

  .step-item {
    position: relative;
    font-size: 28rpx;
    box-sizing: border-box;

    .summary {
      position: relative;
      display: flex;
      align-items: center;
      padding: 10rpx 0;
      z-index: 10;

      .step-icon {
        width: 40rpx;
        height: 40rpx;
        background: url('../../../../static/fifthg/imgs/icon_idel.png');
        background-position: center;
        background-repeat: no-repeat;
        background-size: 40rpx 40rpx;
      }

      .step-name {
        flex: 1;
        padding: 0 20rpx;
        font-size: 32rpx;
        line-height: 28 * 2rpx;
        font-weight: bold;
        color: rgba(16, 16, 16, 1);
      }

      .step-right {
        width: 40rpx;
        height: 40rpx;
        background: url('../../../../static/fifthg/imgs/ic_in.png');
        background-position: center;
        background-repeat: no-repeat;
        background-size: 12rpx 20rpx;
      }
    }

    .body {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10rpx;

      .step-line {
        position: absolute;
        left: 20rpx;
        height: 100%;
        border-left: dashed 1px #b1b1b1;
      }

      .ctn {
        flex: 1;
        margin-left: 60rpx;

        .step-content {
          border-radius: 8rpx;
          padding: 10rpx 20rpx;
          background-color: #f5f5f5;
          font-size: 28rpx;
          line-height: 40rpx;
          box-sizing: border-box;

          .selected-package {
            display: flex;
            align-items: flex-start;
            padding: 10rpx 0;

            .text1 {
              flex: 1;
              color: #212121;
            }

            .text2 {
              flex: 3;
              color: rgba(64, 149, 229, 1);
              white-space: break-spaces;
            }
          }
        }
      }
    }

    &.finish {
      .summary .step-icon {
        background-image: url('../../../../static/fifthg/imgs/icon_finish.png');
      }

      .body .step-line {
        border-left-color: #1F8FF7;
      }
    }

    &.expire {
      .summary .step-icon {
        background-image: url('../../../../static/fifthg/imgs/icon_expire.png');
      }

      .body .step-line {
        border-left-color: #FF2E2E;
      }
    }
  }

  // .step-item:last-child{
  // 	margin-bottom: 0;
  // }

  .realname-prompt {
    color: rgba(145, 145, 145, 1);
    line-height: 34rpx;
    font-size: 24rpx;
  }


  .crad-view {


    .index-txt {
      padding-right: 20rpx;
    }

    .name-txt {
      flex: 10;

    }

    .state-txt {
      font-size: large;
    }
  }

  .sub-tip {
    position: relative;
    margin-left: 32rpx;
    padding-left: 40rpx;
    font-size: 24rpx;
    line-height: 40rpx;
    color: #b1b1b1;

    &::after {
      content: '';
      display: block;
      position: absolute;
      top: 50%;
      left: 0;
      width: 30rpx;
      height: 30rpx;
      background-image: url(../../../../static/fifthg/imgs/icon_expire.png);
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      transform: translate(0, -50%);
    }
  }

  .out-card-view {
    border: 1px solid #BBBBBB;
    border-radius: 18rpx;
    padding: 20rpx;
  }

  .in-card-view {
    margin-top: 20rpx;
  }

  .step-info {
    margin-top: 30rpx !important;
  }

}

.giveup-btn {
  text-align: center;
  margin: 40rpx 120rpx;
  padding: 20rpx 30rpx;
  background-color: #FF7B3A;
  color: #FFFFFF;
  border-radius: 10rpx;
}
</style>
