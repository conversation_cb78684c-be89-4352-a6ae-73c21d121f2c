<template>
  <view class="vOpenAccount">
    <!-- <action-bar title="开户" leftIcon="back"></action-bar> -->
    <action-scroll-view>
      <uni-section title="基础信息" type="line"></uni-section>
      <!-- 基础信息 -->
      <BaseMsg></BaseMsg>
      <!-- 固网商品用户 -->
      <!-- <OAGDMProducts class="marginT" @showGDMProductsDialog="showGDMProductsDialog"></OAGDMProducts> -->
      <!-- 选择退订用户 -->
      <!-- <OAWithdrawProduct class="marginT"></OAWithdrawProduct>-->

      <!-- 设备信息 -->
      <!-- 设备来源 -->
      <OADevInfoInput class="marginT"/>
      <!-- 数字业务 -->
      <OADigit class="marginT"/>
      <!-- 互动业务 -->
      <OAVod class="marginT"/>
      <!-- 宽带业务 -->
      <OACm class="marginT"/>
      <!-- 5g业务信息 -->
      <OAFifthG class="marginT" ref="OAFifthG"/>
      <!-- 产品/套餐配置 -->
      <OAProduct class="marginT"/>
      <!-- 统一账户 -->
      <OAUnifyAccount v-if="noUnifyAccount" class="marginT" @getType="getTypeData" @getPhoneList="getPhoneData"></OAUnifyAccount>
      <!-- 校园信息-->
      <SchoolView class="marginT"/>
      <!-- 发票信息 -->
      <OAUserInvoice ref="OAUserInvoice" class="marginT"/>
      <!-- 安装预约 -->
      <OAInstallMsg class="marginT"></OAInstallMsg>

      <uni-section class="marginT" title="其它" type="line"></uni-section>
      <!-- 其它 -->
      <OAOtherMsg/>
      <!-- 发展人信息 -->
      <DeveloperView class="marginT"></DeveloperView>

      <view v-if="needCommit">
        <view v-if="!btnDis" class="vOpenAccount-btn">
          <view class="btn" style="background-color: #92bdff;">提交 ({{ seconds }} 秒)</view>
        </view>
        <view v-else class="vOpenAccount-btn">
          <view class="btn" @click="submit()">提交</view>
        </view>
      </view>
      <view v-else class="vOpenAccount-btn">
        <view class="btn" @click="goNext()">业务已提交，进行下一步</view>
      </view>
    </action-scroll-view>

    <!-- 以固促移的商品列表 -->
    <GDMProductsDialog ref="GDMProducts"></GDMProductsDialog>
  </view>
</template>

<script>
import UniSection from '@/uni_modules/uni-section/uni-section.vue';
import BaseMsg from '@/pages/FifthG/components/openAccount/OABaseMsg.vue';
import OADevInfoInput from '@/pages/FifthG/components/openAccount/OADevInfosInput.vue';
import OADigit from '@/pages/FifthG/components/openAccount/OADigit.vue';
import OAVod from '@/pages/FifthG/components/openAccount/OAVod.vue';
import OACm from '@/pages/FifthG/components/openAccount/OACm.vue';
import DeveloperView from '@/components/developer/DeveloperView.vue';
import OAInstallMsg from '@/pages/FifthG/components/openAccount/OAInstallMsg.vue';
import OAOtherMsg from '@/pages/FifthG/components/openAccount/OAOtherMsg.vue';
import OAFifthG from '@/pages/FifthG/components/openAccount/OA5GMsg.vue';
import OAUserInvoice from '@/pages/FifthG/components/openAccount/OAUserInvoice.vue';
import OAProduct from '@/pages/FifthG/components/openAccount/OAProduct.vue';
import OAWithdrawProduct from '@/pages/FifthG/components/openAccount/OAWithdrawProduct.vue';
import OAGDMProducts from '@/pages/FifthG/components/openAccount/OAGDMProducts.vue';
import GDMProductsDialog from '@/pages/FifthG/components/openAccount/GDMProductsDialog.vue';
import SchoolView from '@/pages/FifthG/components/openAccount/SchoolView.vue';
import {mapGetters, mapActions, mapMutations} from 'vuex';
import {psubClassTag} from '@/store/modules/sysparam.js';
import {PSUBCLASS, PERMARK} from '@/store/modules/openAccount.js';
import PickMsgItem from '@/pages/FifthG/components/PickMsgItem.vue';
import OAUnifyAccount from "../../components/openAccount/OAUnifyAccount.vue";

export default {
  name: 'business',
  data() {
    return {
      pageCode: '',
      btnDis: false,
      seconds: 10,
      isSend: false, // 是否已发送
      selectPkgList: [],
      signType: null,
      phone: null,
      noUnifyAccount: true,
      isDistrict: false, // 是否区级代理商
      options: {}
    };
  },

  created() {
  },

  components: {
    UniSection,
    BaseMsg,
    OADevInfoInput,
    OADigit,
    OAVod,
    OACm,
    DeveloperView,
    OAInstallMsg,
    OAOtherMsg,
    OAFifthG,
    OAUserInvoice,
    OAProduct,
    OAWithdrawProduct,
    OAGDMProducts,
    GDMProductsDialog,
    SchoolView,
    OAUnifyAccount
  },

  computed: {
    ...mapGetters({
      percomb: 'getPercomb',
      submitData: 'getSubmitData',
      custInfo: 'getCustInfo',
      currentAddress: 'getCurrentAddress',
      stepInfo: 'getStepInfo',
      salesInfo: 'getSalesInfo',
      userInfo: "getUserInfo"
    }),
    //检查是否需要提交
    needCommit() {
      console.log('needCommit')
      let process = this.stepInfo.process
      let pageBean = process.filter(proc => {
        return proc.code == this.pageCode
      })[0]
      console.log('pageBean---------------------')
      console.log(pageBean)
      if (pageBean && pageBean.pstatus == '2') {
        return false
      } else {
        return true;
      }

    }
  },

  watch: {},

  async onLoad(data) {
    this.options = data
    this.initSaveProcess()
    this.pageCode = data.code;
    await this.initData([psubClassTag]);
    await this.queryData({
      permark: 'B',
      pcode: this.salesInfo.pcode //商品的pcode
    });
    // console.log('this.userInfo====' + JSON.stringify(this.userInfo));
    // console.log('this.salesInfo====' + JSON.stringify(this.salesInfo));
    // console.log('this.custInfo====' + JSON.stringify(this.custInfo));
    // console.log('设置这个开户流程的默认值this.stepInfo.data[this.pageCode]' + JSON.stringify(this.stepInfo.data[this.pageCode]));
    console.log('this.stepInfo====' + JSON.stringify(this.stepInfo));
    console.log('this.stepInfo.pkginfo====>', this.stepInfo.pkginfo);
    console.log('stepInfo', this.stepInfo, this.pageCode);
    //设置这个开户流程的默认值
    this.setStepDefault(this.stepInfo.data[this.pageCode]);
    this.$refs.OAFifthG.checkFgbServTypeList();
  },

  onShow() {
    console.log(11)
    // 10s倒计时
    this.btnDis = false
    this.onLoadSalesPackagePrd()
    this.queryUnifyAccounts()
    this.queAgentInfo()
    this.timer = setInterval(() => {
      this.countdown();
    }, 1000)
  },

  onUnload() {
    this.leave();
  },

  methods: {
    ...mapActions({
      queryData: 'queryData',
      initData: 'init',
      saveProcess: 'saveProcess',
      goNextPage: 'goNextPage',
      queProcess: 'queProcess'
    }),

    ...mapMutations({
      collect: 'submit', //开始收集数据的方法
      setStepDefault: 'setStepDefault',
      leave: 'leaveOpenAccount'
    }),

    showGDMProductsDialog() {
      this.$refs.GDMProducts.show(this.salesInfo.salesRels);
    },

    submit() {
      const that = this
      uni.showModal({
        title: '温馨提示',
        content: '当前业务流程提交后，无法再次返回修改提交，请检查各项信息是否已填写完整',
        showCancel: true, //没有取消按钮的弹框
        buttonText: '确定',
        success(res) {
          if (res.confirm) {
            // 10s倒计时
            that.btnDis = false
            that.timer = setInterval(() => {
              that.countdown();
            }, 1000)
            that.countdown()
            that.collect();
            that.$nextTick(() => {
              console.log(that.submitData);
              if (!that.checkData()) {
                return;
              }

              that._submit();
            });
          }
        }
      });
    },

    saveData(custOrderid, serialno) {
      // this.collect()
      // this.$nextTick(() => {
      // 	let saveData = Object.assign({
      // 		percomb: this.percomb.percomb,
      // 		combname: this.percomb.combname,
      // 		permarks: this.percomb.permarks
      // 	}, this.submitData)
      // 	saveData.address = this.currentAddress;
      // 	this.saveProcess({
      // 		data: {
      // 			"poid": this.stepInfo.poid,
      // 			"cpcode": this.pageCode,
      // 			"data": JSON.stringify(saveData),
      // 			"temp": "1"
      // 		}
      // 	})
      // })

      let saveData = Object.assign(
        {
          percomb: this.percomb.percomb,
          combname: this.percomb.combname,
          permarks: this.percomb.permarks,
          custOrderid: custOrderid,
          serialno: serialno
        },
        this.submitData
      );
      saveData.address = this.currentAddress;
      this.saveProcess({
        data: {
          poid: this.stepInfo.poid,
          cpcode: this.pageCode,
          data: JSON.stringify(saveData),
          temp: '1',
        },
        refreshCustInfo: true,
        custid: this.custInfo.custid,
        unNeedNext: true,
        afterProcess: () => {
          console.log('afterProcess')
          this.$rauter.navigateTo({
            url: "FusionGDM",
            data: {
              poid: this.stepInfo.poid
            }
          });
        }
      });
    },

    //最终提交接口
    _submit() {
      let params = {
        installparams: []
      };
      params.custid = this.custInfo.custid; //填充客户id
      params.custname = this.custInfo.custname; //填充客户名字
      params.iscrtorder = 'Y';
      params.opcode = 'BIZ_USER_NEW'; //填充当前业务的opcode
      params.optime = this.$util.dataProcess.formatDate(new Date()); //填充当前办理的时间
      params.describe = '';
      params.memo = this.submitData.memo; //备注

      params.systemid = 'GRID';
      // params.systemid = "BOSS"
      params.houseid = this.currentAddress.houseid || ''; //当前地址的houseid
      params.patchid = this.currentAddress.patchid || ''; //当前地址的patchid
      params.whladdr = this.currentAddress.addr || ''; //当前地址的addr
      params.knowids = this.stepInfo.salesid + '';
      params.counts = '0';
      params.units = this.salesInfo.unitname;
      params.ispostpones = this.salesInfo.ispostpone;
      // params.stimes=stimes
      params.mindates = 'null'; //5g业务下，先传null
      // params.verifyphone= "" //手机验证码
      let ejectparams = this.submitData.ejectparams;
      if (this.notEmptyObj(ejectparams)) {
        params.servids = ejectparams.servids;
      } else {
        params.servids = 'null'; //5g业务下，先传null
      }

      if (this.salesInfo.pcode == 'MGD') {
        // 以移带固
        let fixorder = this.submitData.fixorderArr;
        let permark = this.salesInfo.permark;
        let mixservids = '';
        console.log('===fixorder===');
        console.log(fixorder);
        if (fixorder && fixorder.length) {
          permark.split(',').forEach(val => {
            let find = fixorder.find(item => item.permark == val);
            const _servid = find.servid || '';
            if (mixservids) {
              mixservids = mixservids + '~' + _servid;
            } else {
              mixservids = _servid;
            }
          });
          params.mixservids = mixservids
          console.log("mixservids====" + mixservids)
        }
      } else {
        // 有线专属和以固促移
        let fixorder = this.stepInfo.data.GDM_GWDX;
        if (fixorder) {
          const _servid = fixorder.servid || '';
          const _pid = fixorder.pid || '';
          // const _relswtype = fixorder.relswtype || '';
          // const _relswcode = fixorder.relswcode || '';
          // const _offercode = fixorder.offercode || '';
          const digituser = _servid + '~' + _pid;
          params.digituser = digituser
          console.log(digituser)
        }
      }

      // 促销多选
      let discountProd = this.stepInfo.discountpkgs ? JSON.parse(this.stepInfo.discountpkgs) : []
      if (discountProd && discountProd.length > 0) {
        params.relSales = discountProd.map(item => {
          return {
            knowid: this.salesInfo.knowid,
            relsalesid: item.objid
          }
        })
      }

      // let discountProd = this.submitData.discountProd;
      // if (discountProd && discountProd.objid) {
      //   params.relSales = [
      //     {
      //       knowid: this.salesInfo.knowid,
      //       relsalesid: discountProd.objid
      //     }
      //   ];
      // }

      //添加安装参数
      let installParam = this.addInstallParam();
      params.installparams.push(installParam);

      // 充返
      const contract = this.submitData.contract
      console.log(contract)
      if (contract && contract.offercode) {
        params.contractList = [{
          contract_code: contract.offercode,
        }]
      }

      //添加地址参数
      let addrs = this.addAddrParam();
      if (addrs) {
        params.addrs = [addrs];
      }

      //安装费用参数
      let onceFeeParam = this.addOnceFeeParam();
      if (onceFeeParam) {
        params.oncefeeparams = [onceFeeParam];
      }

      //添加使用参数
      let userParam = this.addUserParam();
      params.userparams = userParam;

      //添加发展人参数
      let developerParam = this.addDeveloperParam();
      if (developerParam) {
        params.developer = developerParam;
      }

      //获取遥控器模块
      let tel = this.submitData.devInfosInput[PSUBCLASS.tel];
      if (tel) {
        //仓库部门
        params.storeplace = tel.storeplace;
      }

      //添加退订参数
      let ejectParams = this.addEjectparams();
      if (ejectParams) {
        params.ejectparams = [ejectParams];
        params.bizfeein = 'Y';
      }

      //添加校园信息参数
      let schoolParam = this.addSchoolParam();
      if (schoolParam) {
        params.collcode = schoolParam.collCode;
        params.cellscode = schoolParam.cellsCode;
      }

      //添加可选包参数
      let pkgparams = this.addPkgParams();
      console.log('pkgparams====>', pkgparams)
      if (pkgparams) {
        params.packs = pkgparams;
      }

      // 添加流程编号
      this.$set(params, 'poid', this.stepInfo.poid)

      console.log(params);


      if (this.isSend) {
        return
      }
      uni.showLoading({
        title: '请稍后...',
        mask: true
      })
      this.isSend = true
      // 3秒后可以再次点击
      setTimeout(() => {
        this.isSend = false
        uni.hideLoading()
      }, 3000)

      this.$http.post(this.$apis.openAccount, params).then(response => {
        //开户成功后，保存数据，并且跳转
        let custOrderid = response.responseBody.custOrderid;
        let serialno = response.responseBody.serialno;
        this.saveData(custOrderid, serialno);
      });
    },

    addInstallParam() {
      let installParam = {};
      installParam.houseid = this.currentAddress.houseid || ''; //当前地址的houseid
      installParam.addr = this.currentAddress.patchid || ''; //当前地址的patchid
      installParam.patchid = this.currentAddress.addr || ''; //当前地址的addr
      installParam.name = this.custInfo.custname; //填充客户名字
      installParam.cardtype = this.custInfo.cardtype;
      installParam.cardno = this.custInfo.cardno;
      installParam.linkman = this.custInfo.linkman;
      installParam.linkphone = this.custInfo.mobile;
      installParam.mobile = this.custInfo.mobile;

      installParam.permark = this.percomb.permarks;
      installParam.omode = '0'; //开户类型
      installParam.feekind = this.submitData.feekind; //收费类型
      // 判断是否安装类型是否”自安装“=0
      // installParam.predate = this.submitData.installType == '0' ? null : this.$util.dataProcess.formatDate(new Date(this.submitData.installDate || '')) || ''; //预约安装时间，格式"yyyy-MM-dd HH:mm:ss"

      installParam.orderId = this.stepInfo.data.GDM_ID.orderId
      installParam.basisTime = this.stepInfo.data.GDM_ID.basisTime

      //获取数字业务的选择信息
      let digit = this.submitData.digit;
      if (this.notEmptyObj(digit)) {
        //填充数字业务的数据
        installParam.servtype = digit.servtype;
        installParam.pservid = digit.pservid;
      }

      let devInfosInput = this.submitData.devInfosInput;

      //获取智能卡的输入信息
      let smInput = devInfosInput[PSUBCLASS.sm];
      if (smInput) {
        //填充智能卡的数据
        //设置智能卡设备号
        installParam.logicdevno = smInput.input;
        installParam.smno = smInput.input;
        //设置智能卡设备来源
        installParam.smnouseprop = smInput.mcode || '';
      }

      //获取机顶盒的输入信息
      let stbInput = devInfosInput[PSUBCLASS.stb];
      if (stbInput) {
        //填充机顶盒的数据
        //设置机顶盒设备号
        installParam.stbno = stbInput.input;
        //设置机顶盒设备来源
        installParam.stbuseprop = stbInput.mcode || '';
      }

      //获取cm的输入信息
      let cmInput = devInfosInput[PSUBCLASS.cm];
      if (cmInput) {
        //填充cm的数据
        if (!installParam.logicdevno) {
          //如果没有logicdevno,就填充cm的input
          installParam.logicdevno = cmInput.input;
        }
        //设置cm的设备号
        installParam.cmno = cmInput.input;
        //设置cm的来源
        installParam.cmuseprop = cmInput.mcode;
      }

      //获取eoc的输入信息,EOC设备参数与CM参数是一样的
      let eocInput = devInfosInput[PSUBCLASS.eoc];
      if (eocInput) {
        //设置eoc的设备号
        installParam.cmno = eocInput.input;
        //设置eoc的来源
        installParam.cmuseprop = eocInput.mcode;
      }

      //获取光机的输入信息
      let gjInput = devInfosInput[PSUBCLASS.gj];
      if (gjInput) {
        //设置光机的设备号
        installParam.qtypeno = gjInput.input;
        //设置光机的来源
        installParam.qtypeprop = gjInput.mcode;
      }

      //获取OTT的输入信息
      let ottInput = devInfosInput[PSUBCLASS.ott];
      if (ottInput) {
        let ottParamsArr = [];
        let ottParams = ottParamsArr[0];
        //设置OTT的设备号
        ottParams.devno = ottInput.input;
        //设置OTT的来源
        ottParams.useprop = ottInput.mcode;
        //设置OTT的产品
        ottParams.asstno = ottInput.pid;
        //设置OTT的kind
        ottParams.kind = 'R';
        //设置OTT的exdevList
        installParam.exdevList = ottParamsArr;
      }

      //获取U安的输入信息
      let uaInput = devInfosInput[PSUBCLASS.ua];
      if (uaInput) {
        //设置U安的设备号
        installParam.uano = uaInput.input;
        //设置U安的来源
        installParam.uaprop = uaInput.mcode;
      }

      //获取遥控器模块
      let tel = devInfosInput[PSUBCLASS.tel];
      if (tel) {
        //遥控器模块
        //遥控器来源
        installParam.fituseprop = tel.sourceMcode;
        //遥控器类型
        installParam.fitkind = tel.typeMcode;
        //遥控器产品
        installParam.fitpid = tel.prodMcode;
        //遥控器属性
        installParam.fitattr = tel.natureMcode;
      }

      //5g业务
      let fg = this.submitData.fg;
      if (this.notEmptyObj(fg)) {
        let fgParams = {
          prestorefee: fg.prestorefee,
          invoicemode: fg.invoicemode,
          fgbmobileno: fg.fgbmobileno,
          fgbiccid: fg.fgbiccid,
          fgbservtype: fg.fgbservtype,
          fgbpservid: fg.fgbpservid || ''
        };
        installParam = Object.assign(installParam, fgParams);
      }

      installParam.percomb = this.percomb.percomb;
      //备注参考app，传空
      installParam.memo = '';

      //支付方式
      installParam.payway = this.submitData.payment;

      //统一账户参数
      if (fg?.fgbservtype == 0 && this.noUnifyAccount && this.signType && this.phone) {
        installParam.unityFlag = 'Y'
        installParam.businessType = this.signType
      }

      // 号码归属地(改成BOSS控制)
      // installParam.belongCity = this.isDistrict ? this.custInfo.city : this.userInfo.city

      // 添加境外拦截确认书图片ID
      if (this.stepInfo.data && this.stepInfo.data.BLOCK) {
        const blockData = this.stepInfo.data.BLOCK;
        console.log('境外拦截确认书数据:', blockData);
        if (blockData.imgId) {
          installParam.abroadinterceptid = blockData.imgId;
          console.log('添加境外拦截确认书图片ID:', blockData.imgId);
        }
      }

      return installParam;
    },

    addAddrParam() {
      let addrs = null;
      if (this.currentAddress.isEdited) {
        //当前地址编辑过就传数据
        addrs = {
          houseid: this.currentAddress.houseid,
          addr7: this.currentAddress.addr7,
          addr8: this.currentAddress.addr8,
          addr9: this.currentAddress.addr9,
          addr10: this.currentAddress.addr10,
          addr11: this.currentAddress.addr11
        };
      }
      return addrs;
    },

    addOnceFeeParam() {
      let onceFeeParam = null;
      if (this.submitData.installFees) {
        // 存在安装费就填充数据
        onceFeeParam = {
          pcode: this.submitData.feePcode,
          sum: this.submitData.fee
        };
      }
      return onceFeeParam;
    },

    addUserParam() {
      let userParam = [];
      //获取数字业务的选择信息
      let digit = this.submitData.digit;
      if (this.notEmptyObj(digit)) {
        let digitPermark = this.addPermarkParams(PERMARK.digit);
        digitPermark.iscabl = digit.cableMcode;
        digitPermark.uecode = digit.platformMcode;
        //填充数字业务的数据
        userParam.push(digitPermark);
      }

      //获取互动业务信息
      let vod = this.submitData.vod;
      if (this.notEmptyObj(vod)) {
        let vodPermarkParams = this.addPermarkParams(PERMARK.vod);
        // 接入方式
        vodPermarkParams.inputway = vod.inputWay;
        //信用额度
        vodPermarkParams.vodacctno = vod.vodacctno;
        //外置的mac编号
        vodPermarkParams.outdevno = vod.outMacNum;
        //使用使用内置mac号
        vodPermarkParams.isineer = vod.useInnerMac;
        //填充互动业务的数据
        userParam.push(vodPermarkParams);
      }

      //获取宽带业务信息
      let cm = this.submitData.cm;
      if (this.notEmptyObj(cm)) {
        let cmPermark = this.addPermarkParams(PERMARK.cm);
        cmPermark.inputway = cm.inputWayCode;
        cmPermark.authmode = cm.authModeCode;
        cmPermark.ipmode = cm.ipMode;
        cmPermark.ipnum = cm.ipCount;
        cmPermark.cmtype = cm.cmTypeCode || '';
        cmPermark.cmacctno = cm.cmAccount; //宽带账号
        cmPermark.cmpwd = cm.cmPassword;
        cmPermark.password = cm.cmPassword; //宽带密码
        userParam.push(cmPermark);
      }

      if (this.percomb.permarks.indexOf('7') >= 0) {
        //ua类型
        let uaPermark = this.addPermarkParams(PERMARK.ua);
        userParam.push(uaPermark);
      }

      //5g业务
      let fg = this.submitData.fg;
      if (this.notEmptyObj(fg)) {
        //5g类型
        let fgPermark = this.addPermarkParams(PERMARK.fg);
        userParam.push(fgPermark);
      }

      return userParam;
    },

    addPermarkParams(permark) {
      let permarkParams = {};
      permarkParams.permark = permark;
      permarkParams.feekind = this.submitData.feekind; //收费类型
      permarkParams.servkind = '0'; //固定传0

      let devInfosInput = this.submitData.devInfosInput;

      //获取智能卡的输入信息
      let smInput = devInfosInput[PSUBCLASS.sm];
      if (smInput) {
        //填充智能卡的数据
        //设置智能卡设备来源
        permarkParams.userprop = smInput.mcode || '';
        //设置智能卡设备产品
        permarkParams.smnopid = smInput.pid || '';
      }

      //获取机顶盒的输入信息
      let stbInput = devInfosInput[PSUBCLASS.stb];
      if (stbInput) {
        //填充机顶盒的数据
        //设置机顶盒设备产品
        permarkParams.stbpid = stbInput.pid || '';
      }

      //获取cm的输入信息
      let cmInput = devInfosInput[PSUBCLASS.cm];
      if (cmInput) {
        //填充cm的数据
        if (cmInput.pid && !permarkParams.smnopid) {
          //如果存在cm的产品id，并且智能卡的产品id没有，就置空
          permarkParams.userprop = null;
        }
        permarkParams.cmpid = cmInput.pid || '';
        permarkParams.macpid = cmInput.pid || '';
      }

      //获取光机的输入信息
      let gjInput = devInfosInput[PSUBCLASS.gj];
      if (gjInput) {
        //设置光机的来源
        permarkParams.qtypepid = gjInput.pid;
      }

      //获取U安的输入信息
      let uaInput = devInfosInput[PSUBCLASS.ua];
      if (uaInput) {
        installParam.uapid = uaInput.pid;
      }
      //安装类型
      permarkParams.installtype = this.submitData.installType
      //缴费方式
      permarkParams.payway = this.submitData.payment
      return permarkParams
    },
    addDeveloperParam() {
      var developerParam = null
      var developer = this.submitData.developer
      if (developer) {
        //如果存在发展人信息添加参数
        developerParam = {}
        developerParam.type = developer.type
        developerParam.place = developer.place
        developerParam.devname = developer.devname
        developerParam.memo = developer.memo
        developerParam.operid = developer.operid
        developerParam.newdevid = developer.newdevid
        developerParam.devid = developer.devid
      }
      return developerParam
    },

    addEjectparams() {
      let tempEjectparams = null
      let ejectparams = this.submitData.ejectparams
      if (this.notEmptyObj(ejectparams) &&
        ejectparams.ejectPrds &&
        ejectparams.ejectPrds.length > 0) {
        tempEjectparams = {}
        let ejectProds = ejectparams.ejectPrds.map(ejectProdBean => {
          return {
            servid: ejectProdBean.servid,
            pids: ejectProdBean.pid
          }
        })
        tempEjectparams.ejecttype = "0" //退订商品
        tempEjectparams.ejectPrds = ejectProds //退订商品列表
      }
      return tempEjectparams
    },
    addSchoolParam() {
      var schoolParam = null
      var schoolInfo = this.submitData.schoolInfo
      if (schoolInfo) {
        //如果存在校园信息添加参数
        schoolParam = {}
        schoolParam.collCode = schoolInfo.collCode
        schoolParam.cellsCode = schoolInfo.cellsCode
      }
      return schoolParam
    },
    addPkgParams() {
      let packs = []
      if (this.stepInfo.pkginfo) {
        this.selectPkgList.forEach(pkg => {
          let param = {
            optype: '0',
            salesid: pkg.objid,
            salescode: pkg.salescode,
          }
          packs.push(param)
        })
      }
      return packs
    },
    notEmptyObj(obj) {
      return obj && JSON.stringify(obj) != '{}';
    },

    checkData() {
      console.log(this.currentAddress);
      if (this.percomb.permarks != PERMARK.fg && !this.currentAddress.houseid) {
        //纯5g业务不需要开户地址
        this.$util.feeback.showToast({
          title: '请选择开户地址'
        });
        return false;
      }

      if (this.submitData.fg.fgbservtype && this.submitData.fg.fgbservtype == '1' && !this.submitData.fg.fgbpservid) {
        //5g业务选择副卡的时候，判断有没有选主卡
        this.$util.feeback.showToast({
          title: '5g业务选择副卡类型，可是该客户下没有可供选择的主卡信息',
          duration: 3000
        });
        return false;
      }

      if (this.submitData.fg.fgbservtype === '1' && this.submitData.fg.prestorefee * 1 > 0) {
        this.$util.feeback.showToast({
          title: '副卡价格不为0，副卡价格为：' + this.submitData.fg.prestorefee + '，请检查'
        });
        return false
      }

      if (this.submitData.installType == '1' && this.submitData.installDate == '--/--') {
        //当类型是上门安装的时候
        this.$util.feeback.showToast({
          title: '请选择上门安装时间'
        });
        return false;
      }

      for (let configBean of this.percomb.devinfoList) {
        let inputBean = this.submitData.devInfosInput[configBean.psubClass];
        if (configBean.isMust == 'Y' && !inputBean) {
          this.$util.feeback.showToast({
            title: '请输入' + configBean.devLable + '的设备号或点击对应的获取设备产品按钮'
          });
          return false;
        }
      }

      // if (this.salesInfo.offertype === 'SL' && !this.submitData.schoolInfo.collCode) {
      //   this.$util.feeback.showToast({
      //     title: '请选择校园信息'
      //   });
      //   return false;
      // }

      if (this.submitData.fixorder && Object.keys(this.submitData.fixorder).length === 0) {
        this.$util.feeback.showToast({
          title: '请选择固网用户'
        });
        return false;
      }

      return true;
    },
    goNext() {
      const that = this
      uni.showModal({
        title: '温馨提示',
        content: '当前业务已提交，当前操作不会再进行业务提交，会直接进行到下一个环节！',
        showCancel: true, //没有取消按钮的弹框
        buttonText: '确定',
        success: (res) => {
          if (res.confirm) {
            this.$rauter.navigateTo({
              url: "FusionGDM",
              data: {
                poid: this.stepInfo.poid
              }
            });
          }
        }
      });
    },
    countdown: function () {
      if (this.seconds > 0) {
        this.seconds--;
      } else {
        this.btnDis = true
        this.seconds = 10
        clearInterval(this.timer)
      }
    },
    getPkgList(data) {
      this.selectPkgList = []
      console.log('getPkgList====>', data)
      if (data) {
        this.selectPkgList = data
      }

    },
    onLoadSalesPackagePrd() {
      const vm = this
      if (vm.salesInfo.objid) {

        vm.$http.post(vm.$apis.getSalesPackagePrd, {
          objid: this.salesInfo.objid
        }).then((resp) => {
          console.log('resp.responseBody', resp.responseBody)
          if (resp && resp.responseBody && resp.responseBody.salesList.length > 0) {
            vm.selectPkgList = []
            vm.packageList = resp.responseBody.salesList

            console.log('packageList', vm.packageList)

            if(vm.stepInfo.pkginfo) {
              let pkgList = vm.stepInfo.pkginfo.split(',')
              console.log('pkgList', pkgList)
              vm.packageList.forEach((pkg) => {
                let objid = pkg.objid + ''
                if (pkgList.includes(objid)) {
                  vm.selectPkgList.push(pkg)
                }
              });
            }


            console.log('selectPkgList', vm.selectPkgList)
          }
        });
      }
    },
    getTypeData(data) {
      console.log('getTypeData====>', data)
      this.signType = data ? data.objid : null
    },
    getPhoneData(data) {
      console.log('getPhoneData====>', data)
      this.phone = data ? data.objid : null
    },
    queryUnifyAccounts() {
      console.log('this.noUnifyAccount', this.noUnifyAccount)
      let params = {
        custId: this.custInfo.custid,
        status: '0'
      }

      this.$http.post(this.$apis.listUnifiedAccount, params)
        .then(resp => {
          console.log('resp', resp)
          let copy = resp.responseBody.unifiedAccountSearchVOS
          if (copy && copy.length > 0 || this.salesInfo.knowname.indexOf('副卡') !== -1) {
            this.noUnifyAccount = false
            console.log('this.noUnifyAccount', this.noUnifyAccount)
          }
        })
    },
    async queAgentInfo() {
      if (this.userInfo.isagent == 'Y' && this.userInfo.agentid) {
        let resp = await this.$http.post(this.$apis.queAgentInfo)
        console.log('queAgentInfo=====>', resp.responseBody.retData)
        if (resp.responseBody && resp.responseBody.retData) {
          const agentInfo = resp.responseBody.retData
          if (agentInfo.agentlevel && agentInfo.agentlevel == '1') {
            this.isDistrict = true
            console.log('this.params=====>', this.params)
          }
        }
      }
    },
    initSaveProcess() {
      console.log('onLoad========>saveProcess');
      let stepInfo = JSON.parse(JSON.stringify(this.stepInfo)),
        optionsCode = this.options?.code;
      let data = {
        poid: stepInfo.poid || this.$route.query.poid,
        cpcode: optionsCode ? optionsCode : '',
        temp: 0
      };
      let keepData = {
        data,
        unNeedNext: true
      };
      this.saveProcess(keepData);
    }
  }
}
;
</script>

<style lang="scss" scoped>
.vOpenAccount {
  box-sizing: border-box;

  view {
    box-sizing: border-box;
  }

  &-btn {
    width: 100%;
    overflow: hidden;
    padding: 20rpx 40rpx;

    .btn {
      width: 100%;
      overflow: hidden;
      color: #fff;
      font-size: 32rpx;
      background-color: #1f77ff;
      line-height: 100rpx;
      height: 100rpx;
      border-radius: 50rpx;
      text-align: center;
    }
  }
}

.marginT {
  margin-top: 20 rpx;
}</style>
