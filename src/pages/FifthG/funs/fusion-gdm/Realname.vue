<template>
  <c-layout title="实名认证">
    <div class="main">
      <div class="item">
        <div class="info-lf">证件类型</div>
        <selector class="right-info" placeholder="选择证件类型" :options="cardTypeList" v-model="cardType" :value-map="['key','values']" @on-change="selectChangeCardType">
        </selector>
      </div>

      <div class="item">
        <div class="info-lf">证件号码</div>
        <div class="info-rt"><input type="text" class="input-type" placeholder="证件号码" v-model="cardNo"></div>
      </div>
      <div class="pic-box">
        <upload @change="uploadResult"></upload>
      </div>
      <div class="errorinfo">{{ errorInfo }}</div>
      <button class="btn-submit can-btn" @click="doSubmit()">提 交</button>
    </div>
  </c-layout>
</template>

<script>
import selector from '@/components/select/cmmsSelector.vue';
import upload from "../../../../components/upload";
import CForm from "../../components/form/CForm";
import CQueryCard from "../../components/list/CQueryCard";
import CQueryInfoItem from "../../components/list/CQueryInfoItem";
import CNoData from "../../components/list/CNoData";
import { mapGetters } from "vuex";
import { toRange } from "../../enums/enums";
import CLayout from "../../components/layout/CLayout";
import { getMonth } from "../../../../common/js/DateUtils";
import { fenToYuan } from "../../../../common/js/AmountUtils";

const checkIDCard = (card) => {
  // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
  var reg = /(^\d{15}$)|(^\d{17}(\d|X|x)$)/
  if (reg.test(card) === false) {
    return false
  }
  return true
}
export default {
  components: { CLayout,upload,selector },
  data() {
    return {
      citylist: [],
      userinfo: null,
      operinfo: null,
      cityCode: null,
      cardType: null,
      cardTypeList: [],
      canSubmit: false,
      custName: null,
      cardNo: null,
      phone: null,
      errorInfo: '',
      picArr: null
    }
  },
  computed: {
    ...mapGetters({
      custInfo: 'getCustInfo',
      userInfo: "getUserInfo",
      checkIdentity: 'getCheckIdentity',
    })
  },
  mounted() {
    this.loadData()
  },
  methods: {
    uploadResult(e){
      console.log('uploadResult', e)
      this.picArr = [e]
    },
    async loadData() {
      this.list = null
      this.operinfo = this.userInfo
      console.log(this.operinfo)
      const {responseBody: {paramdata}} = await this.$http.post(this.$apis.getdata, {
          gcode: 'PRV_AREA_BY_CITY',
          mcode: this.operinfo.city
        })

      console.log(paramdata)
      var citys = []
      for (var i in paramdata) {
        console.log(i)
        var item = {}
        item.key = paramdata[i].mcode
        item.values = paramdata[i].mname
        citys.push(item)
      }
      this.citylist = citys
      console.log('cityList')
      console.log(this.citylist)

      const {responseBody: cardList} = await this.$http.post(this.$apis.getInstallBasedata, {
        city: this.operinfo.city
      })
      console.log(cardList)
      if (cardList && cardList.cardTypes) {
        for (var j in cardList.cardTypes) {
          var it = {}
          it.key = cardList.cardTypes[j].mcode
          it.values = cardList.cardTypes[j].mname
          this.cardTypeList.push(it)
        }
      }
    },
    async doSubmit() {
      if (this.cardType == 1) {
        if (!checkIDCard(this.cardNo)) {
          this.errorInfo = '* 证件号码输入有误'
          return
        } else {
          this.errorInfo = ''
        }
      }
      if (!this.picArr || this.picArr.length <= 0) {
        uni.showToast({
          title: '请先上传图片证件',
          icon: 'none'
        })
        return
      }
      var paths = []
      paths = this.picArr
      const {responseBody: result} = await this.$http.post(this.$apis.saveCardPhoto, {
        cardNo: this.cardNo,
        cardType: this.cardType,
        paths: paths
      })
      console.log(result)
      var custorderCardId = result.custorderCardId
      localStorage.setItem('custorderCardId', custorderCardId)
      this.$router.go(-1)
    },
    selectChangeCardType(val) {
      console.log(this.cardType)
      this.cardType = val
    }
  }
}
</script>


<style lang="scss" scoped>
/deep/.weui-cell:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 0px solid #D9D9D9 !important;
  color: #D9D9D9;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  left: 15px;
}

.main-content {
  background-color: #fff;
  margin-top: 0.2 * 100rpx;
  padding-top: 0.2 * 100rpx;
  padding-bottom: 0.2 * 100rpx;
}

.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20rpx 0;
  padding: 18rpx 30rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.info-lf {
  line-height: 0.6 * 100rpx;
  text-align: justify;
  text-align-last: justify;
}

.info-rt {
  line-height: 0.6 * 100rpx;
  text-align: right;
}

.input-type {
  margin-left: 0.2 * 100rpx;
  border: none;
  border-radius: 0.1 * 100rpx;
  padding: 10rpx;
  background: #F2F5F8;
  outline: none;
}

.btn-submit {
  margin: 30rpx;
  border-radius: 0.08 * 100rpx;
  padding: 20rpx 0;
  background: #C3D2E0;
  font-size: 0.32 * 100rpx;
  font-weight: 400;
  color: #FFFFFF;
}

.can-btn {
  background-color: #3875D0;
}

.errorinfo {
  width: 6.6 * 100rpx;
  height: 0.3 * 100rpx;
  font-size: .28 * 100rpx;

  font-weight: 400;
  color: #F23C3C;
  line-height: 36px;
}

/deep/ .weui-select {
  font-size: 0.26rem !important;
}

/deep/ .weui-cell {
  padding: .2rem 0rem !important;
  padding-left: 0.3rem !important;
}

/deep/ .weui-label {
  float: left;
  text-align: left;
  min-width: 1.5 * 100rpx;
  text-align: justify;
  text-align-last: justify;
  margin-right: 0.2 * 100rpx;
  font-size: 0.28 * 100rpx;
  color: #000;
}

.pic-box {
  background-color: #fff;
}
</style>