<template>
  <c-layout title="选择产品">

    <div class="main-body">
      <div class="tool-box">
        <!-- <span class="t-sp t-sp-select" v-if="prdType == 1" @click="switchType(1)">营销推荐</span>
            <span class="t-sp" v-else @click="switchType(1)">营销推荐</span> -->
        <span class="t-sp t-sp-select" v-if="prdType == 2" @click="switchType(2)">产品</span>
        <span class="t-sp" v-else @click="switchType(2)">产品</span>
        <span v-if="flag == 'prdsTransform'">
          <span class="t-sp t-sp-select" v-if="prdType == 3" @click="switchType(3)">收藏</span>
          <span class="t-sp" v-else @click="switchType(3)">收藏</span>
          <span class="img-sp img-sp-shaix" @click="showFilter"></span>
        </span>
        <span v-else>
          <span class="t-sp t-sp-select" v-if="prdType == 3" @click="switchType(3)">收藏</span>
          <span class="t-sp" v-else @click="switchType(3)">收藏</span>
          <!-- <span class="img-sp img-sp-gwc" @click="toShopList"></span> -->
          <span class="img-sp img-sp-shaix" @click="showFilter"></span>
        </span>
      </div>
      <div v-if="prdType == 2">
        <!-- <search class="search-input-sy" v-model="searchName" position="absolute" auto-scroll-to-top
          @on-submit="onSearchSubmit" @on-cancel="onSearchCancel" ref="search">
        </search> -->

        <div class="search-box-keyword">
          <input id="searchInput" type="text" placeholder-class="font-24" placeholder="搜索产品"
            v-model="searchName" confirm-type="search" @confirm="onSearchSubmit" />
          <i class="iconfont font-48 color-C3C3C3" v-html="'&#xe651;'" @click="onSearchSubmit"></i>
        </div>
        <div v-if="prdList && prdList.length > 0">
          <div class="item-inv" v-for="(item, index) in prdList" :key="index">
            <div class="card">
              <div class="item">
                <span class="prd-info" v-if="item.objtype == '0'">产品</span>
                <span class="prd-info pkg-info" v-else-if="item.objtype == '1'">套餐</span>
                <span class="prd-info-permark digit" v-if="item.permarks && item.permarks.indexOf('1') > -1">数字</span>
                <span class="prd-info-permark cm" v-if="item.permarks && item.permarks.indexOf('2') > -1">宽带</span>
                <span class="prd-info-permark vod" v-if="item.permarks && item.permarks.indexOf('3') > -1">互动</span>
                <span class="prd-info-permark ott" v-if="item.permarks && item.permarks.indexOf('4') > -1">OTT</span>
                <span class="prd-info-permark st" v-if="item.permarks && item.permarks.indexOf('S') > -1">软终端</span>
                <span class="sp-collect un-collect" @click="doUnCollect(item)" v-if="item.favorites">取消收藏</span>
                <span class="sp-collect" @click="doCollect(item)" v-else>收藏</span>
              </div>
              <div @click="gotoOrder(item)">
                <div class="item">
                  <span class="right-info">{{ item.knowname }}</span>
                </div>
                <div class="item">
                  <span class="right-info">价格：{{ item.price }}/{{ item.unitName }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <div class="empty-box">
            <img src="../../../../static/fifthg/imgs/sucai_nodata.png" alt="" class="img-empty">
            <span class="sp-empty">未查询到产品</span>
          </div>
        </div>
      </div>
      <div v-else-if="prdType == 3">
        <div v-if="collectList && collectList.length > 0">
          <div class="item-inv" v-for="(item, index) in collectList" :key="index">
            <div class="card">
              <div class="item">
                <span class="prd-info">产品</span>
                <span class="prd-info-permark digit" v-if="item.permarks && item.permarks.indexOf('1') > -1">数字</span>
                <span class="prd-info-permark cm" v-if="item.permarks && item.permarks.indexOf('2') > -1">宽带</span>
                <span class="prd-info-permark vod" v-if="item.permarks && item.permarks.indexOf('3') > -1">互动</span>
                <span class="prd-info-permark ott" v-if="item.permarks && item.permarks.indexOf('4') > -1">OTT</span>
                <span class="prd-info-permark st" v-if="item.permarks && item.permarks.indexOf('S') > -1">软终端</span>
                <span class="sp-collect un-collect" @click="doUnCollect(item)" v-if="item.favorites">取消收藏</span>
                <span class="sp-collect" @click="doCollect(item)" v-else>收藏</span>
              </div>
              <div @click="gotoOrder(item)">
                <div class="item">
                  <span class="right-info">{{ item.knowname }}</span>
                </div>
                <div class="item">
                  <span class="right-info">价格：{{ item.price }}/{{ item.unitName }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <div class="empty-box">
            <img src="../../../../static/fifthg/imgs/sucai_shoucang_kong.png" alt="" class="img-empty">
            <span class="sp-empty">未查询到收藏记录</span>
          </div>
        </div>
      </div>
      <div v-else-if="prdType == 1">
        <div v-if="cmpRecomList && cmpRecomList.length > 0">
          <div class="item-inv" v-for="(item, index) in cmpRecomList" :key="index">
            <div class="card">
              <div class="item">
                <span class="prd-info">产品</span>
                <span class="prd-info-permark digit" v-if="item.permarks && item.permarks.indexOf('1') > -1">数字</span>
                <span class="prd-info-permark cm" v-if="item.permarks && item.permarks.indexOf('2') > -1">宽带</span>
                <span class="prd-info-permark vod" v-if="item.permarks && item.permarks.indexOf('3') > -1">互动</span>
                <span class="prd-info-permark ott" v-if="item.permarks && item.permarks.indexOf('4') > -1">OTT</span>
                <span class="prd-info-permark st" v-if="item.permarks && item.permarks.indexOf('S') > -1">软终端</span>
                <span class="sp-collect un-collect" @click="doUnCollect(item)" v-if="item.favorites">取消收藏</span>
                <span class="sp-collect" @click="doCollect(item)" v-else>收藏</span>
              </div>
              <div @click="gotoOrder(item)">
                <div class="item">
                  <span class="right-info">{{ item.knowname }}</span>
                </div>
                <div class="item">
                  <span class="right-info">价格：{{ item.price }}/{{ item.unitName }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <div class="empty-box">
            <img src="../../../../static/fifthg/imgs/sucai_shoucang_kong.png" alt="" class="img-empty">
            <span class="sp-empty">未查询到收藏记录</span>
          </div>
        </div>
      </div>

      <van-popup v-model="showFilterDialog">
        <div class="dialog">
          <div class="dg-opt-box">
            <span class="dg-opt-lft-sp" @click="doReset">重置</span>
            <span class="dg-opt-right-sp" @click="doSelectedConfirm">确认</span>
          </div>
          <!-- <div class="dg-title" style="margin-top: 1 * 100rpx;">产品目录</div>
          <div class="catalog-box">
            <div class="dg-ct-box" v-for="(it, idx) in catalogList" :key="idx">
              <span class="dg-sp-sty dg-sp-select" v-if="it.isChecked" @click="clickCataTag(it, idx)">{{ it.catalogName
              }}</span>
              <span class="dg-sp-sty" v-else @click="clickCataTag(it, idx)">{{ it.catalogName }}</span>
            </div>
          </div> -->

          <div class="dg-title">业务类型</div>
          <div class="catalog-box">
            <div class="dg-ct-box" v-for="(it, idx) in permarkList" :key="idx">
              <span class="dg-sp-sty dg-sp-select" v-if="it.isChecked" @click="clickPermarkTag(it, idx)">{{ it.name
              }}</span>
              <span class="dg-sp-sty" v-else @click="clickPermarkTag(it, idx)">{{ it.name }}</span>
            </div>
          </div>

          <div class="dg-title">产品类型</div>
          <div class="catalog-box">
            <div class="dg-ct-box" v-for="(it, idx) in objtypeList" :key="idx">
              <span class="dg-sp-sty dg-sp-select" v-if="it.isChecked" @click="clickObjTag(it, idx)">{{ it.mname }}</span>
              <span class="dg-sp-sty" v-else @click="clickObjTag(it, idx)">{{ it.mname }}</span>
            </div>
          </div>
        </div>
      </van-popup>


    </div>
  </c-layout>
</template>

<script>
import Vue from 'vue';
import {
  mapGetters,
  mapActions,
  mapMutations
} from 'vuex';
import { Popup } from 'vant'
import CLayout from "../../components/layout/CLayout";

Vue.use(Popup)

const postWgApi = () => { }
export default {
  components: { CLayout },
  data() {
    return {
      collectList: null,
      prdList: null,
      userinfo: null,
      prdType: 2, // 2产品 1 推荐
      searchName: null,
      showFilterDialog: false,
      permarkList: null,
      catalogList: null,
      objtypeList: null,
      selectedCatalogs: [],
      selectedPermarks: [],
      selectedObjs: [],
      cmpRecomList: [],
      flag: null,
      serialno: null,
      sum: null
    }
  },
  computed: {
    ...mapGetters({
      custInfo: "getCustInfo",
    }),
  },
  mounted() {
    this.flag = this.$route.query.flag || ''
    this.serialno = this.$route.query.serialno || ''
    this.sum = this.$route.query.sum || ''
    this.loadData()
    // this.loadCatalog()
    this.loadPermark()
    this.loadObjTypy()
  },
  methods: {
    async loadData() {
      if (this.prdType == 2) {
        this.loadPrds()
      } else if (this.prdType == 1) {
        this.loadCmp()
      } else if (this.prdType == 3) {
        this.loadCollects()
      }
    },
    async loadPrds() {
      this.prdList = null
      var param = {}
      param.custid = this.custInfo.custid
      param.opcode = 'BIZ_PRD_ORDER'
      param.catalogIds = this.selectedCatalogs.join(',')
      param.permarks = this.selectedPermarks.join(',')
      param.objtype = this.selectedObjs.join(',')

      const { responseBody: result } = await this.$http.post(this.$apis.queProdList, param)
      console.log(result)
      this.prdList = result
    },
    async loadCollects() {
      this.collectList = null
      var param = {}
      param.custid = this.custInfo.custid
      param.catalogIds = ''
      param.opcode = 'BIZ_PRD_ORDER'
      param.permarks = ''

      const { responseBody: result } = await this.$http.post(this.$apis.queFavoritesProdList, param)
      console.log(result)
      this.collectList = result
    },
    async loadCmp() {
      this.cmpRecomList = null
      var param = {}
      param.custid = this.custInfo.custid
      param.opcode = 'BIZ_PRD_ORDER'

      const { responseBody: result } = await this.$http.post(this.$apis.queCmpProdList, param)
      console.log(result)
      this.cmpRecomList = result
    },
    async loadCatalog() {
      this.catalogList = null
      var param = {}
      // param.custid = this.custInfo.custid
      // param.areaid = this.custInfo.areaid
      // param.opcode = 'BIZ_PRD_ORDER'
      param.objType = '0'
      const { responseBody: result } = await this.$http.post(this.$apis.queryPrdCatalog, param)
      console.log(result)
      this.catalogList = result
    },
    async loadPermark() {
      this.permarkList = null
      var param = {}
      const { responseBody: result } = await this.$http.post(this.$apis.queryType, param)
      console.log(result)
      this.permarkList = result
    },
    async loadObjTypy() {
      this.objtypeList = null
      var param = {}
      param.gcode = 'OBJTYPE'
      const { responseBody: result } = await this.$http.post(this.$apis.getdata, param)
      console.log(result)
      this.objtypeList = result.paramdata
    },
    clickCataTag(it, idx) {
      var copy = [...this.catalogList]
      copy[idx].isChecked = !copy[idx].isChecked
      this.catalogList = copy
    },
    clickPermarkTag(it, idx) {
      var copy = [...this.permarkList]
      copy[idx].isChecked = !copy[idx].isChecked
      this.permarkList = copy
    },
    clickObjTag(it, idx) {
      var copy = [...this.objtypeList]
      copy[idx].isChecked = !copy[idx].isChecked
      this.objtypeList = copy
    },
    async doSelectedConfirm() {
      this.selectedCatalogs = []
      this.selectedPermarks = []
      this.selectedObjs = []
      this.objtypeList.forEach(it => {
        if (it.isChecked) {
          this.selectedObjs.push(it.mcode)
        }
      })
      if (this.catalogList) {
        this.catalogList.forEach(it => {
          if (it.isChecked) {
            this.selectedCatalogs.push(it.catalogId)
          }
        })
      }
      this.permarkList.forEach(it => {
        if (it.isChecked) {
          this.selectedPermarks.push(it.type)
        }
      })
      console.log(this.selectedCatalogs.join(','))
      console.log(this.selectedPermarks.join(','))
      console.log(this.selectedObjs.join(','))

      this.prdType = 2
      this.loadPrds()
      this.showFilterDialog = false
    },
    doReset() {
      this.selectedCatalogs = []
      this.selectedPermarks = []
      this.selectedObjs = []

      var copy = [...this.objtypeList]
      copy.forEach(it => {
        it.isChecked = false
      })
      this.objtypeList = copy

      var copy1 = [...this.permarkList]
      copy1.forEach(it => {
        it.isChecked = false
      })
      this.permarkList = copy1

      var copy2 = [...this.catalogList]
      copy2.forEach(it => {
        it.isChecked = false
      })
      this.catalogList = copy2
    },
    async doCollect(it) {
      var param = {}
      param.knowid = it.knowid
      await this.$http.post(this.$apis.createFavoritesProd, param)
      this.loadData()
    },
    async doUnCollect(it) {
      var param = {}
      param.knowid = it.knowid
      await this.$http.post(this.$apis.cancelFavoritesProd, param)
      this.loadData()
    },
    async onSearchSubmit() {
      this.prdList = null
      var param = {}
      param.custid = this.custInfo.custid
      param.opcode = 'BIZ_PRD_ORDER'
      param.knowName = this.searchName

      param.catalogIds = this.selectedCatalogs.join(',')
      param.permarks = this.selectedPermarks.join(',')
      param.objtype = this.selectedObjs.join(',')
      const { responseBody: result } = await this.$http.post(this.$apis.queProdList, param)
      this.prdList = result
    },
    async onSearchCancel() {
      this.prdList = null
      var param = {}
      param.custid = this.custInfo.custid
      param.opcode = 'BIZ_PRD_ORDER'

      param.catalogIds = this.selectedCatalogs.join(',')
      param.permarks = this.selectedPermarks.join(',')
      param.objtype = this.selectedObjs.join(',')
      const { responseBody: result } = await this.$http.post(this.$apis.queProdList, param)
      this.prdList = result
    },
    showFilter() {
      this.showFilterDialog = true
      console.log('111')
    },
    gotoOrder(param) {
      let query = { ...param }
      if (this.flag && ['createUser', 'inversion'].includes(this.flag)) {
        query = { ...param, flag: this.flag }
      } else if (this.flag && this.flag == 'prdsTransform') {
        query = { ...param, flag: this.flag, serialno: this.serialno, sum: this.sum }
      }

      this.$router.push({ path: 'GProductDetail', query })
    },
    toShopList() {
      this.$router.push({ path: '/prds/shopList' })
    },
    switchType(type) {
      if (this.prdType == type) {
        return
      }
      this.prdType = type
      if (this.prdType == 1) {
        this.loadCmp()
      } else if (this.prdType == 2) {
        this.loadPrds()
      } else if (this.prdType == 3) {
        this.loadCollects()
      }
    }
  }
}
</script>


<style lang="scss" scoped>
* {
  text-align: center;
}

.card {
  background-color: #fff;
  width: 7.1 * 100rpx;
  margin: 0 auto;
  border-radius: 0.08 * 100rpx;
  margin-top: 0.2 * 100rpx;
  padding-top: 0.2 * 100rpx;
  padding-bottom: 0.2 * 100rpx;
}

.item {
  height: 0.5 * 100rpx;
  width: 7.1 * 100rpx;
  display: flex;
  padding-left: 0.3 * 100rpx;
  box-sizing: border-box;

}

.left-info {
  display: inline-block;
  text-align: left;
  height: 0.5 * 100rpx;
  line-height: 0.5 * 100rpx;
  font-size: .28 * 100rpx;

  font-weight: 400;
  color: #000000;
}

.right-info {
  display: inline-block;
  text-align: left;
  height: 0.5 * 100rpx;
  line-height: 0.5 * 100rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 5.5 * 100rpx;
}

.empty-box {
  margin-top: 2 * 100rpx;
}

.img-empty {
  width: 1.2 * 100rpx;
  height: 1.2 * 100rpx;
  background-size: 1.2 * 100rpx 1.2 * 100rpx;
  background-repeat: no-repeat;
}

.sp-empty {
  display: block;
  font-size: .28 * 100rpx;

  font-weight: 400;
  color: #333333;
}

.next-btn {
  width: 7.1 * 100rpx;
  height: 0.6 * 100rpx;
  outline: none;
  border: none;
  color: #fff;
  background-color: #2B84D7;
  display: block;
  position: fixed;
  bottom: 0;
  left: 0.2 * 100rpx;
}

.tool-box {
  width: 7.5 * 100rpx;
  height: 1 * 100rpx;
  background-color: #2B84D7;
}

.t-sp {
  display: inline-block;
  width: 1.8 * 100rpx;
  font-size: 0.32 * 100rpx;
  border-radius: 0.08 * 100rpx;
  color: #fff;
  opacity: 0.6;
  float: left;
  line-height: 1 * 100rpx;
}

.t-sp-select {
  opacity: 1;
}

.img-sp {
  width: 0.7 * 100rpx;
  height: 1 * 100rpx;
  background-size: 0.34 * 100rpx 0.34 * 100rpx;
  display: inline-block;
  float: left;
  background-repeat: no-repeat;
  background-position: center;
}

.img-sp-gwc {
  background-image: url("../../../../static/fifthg/imgs/ic_gouwuche.png");
}

.img-sp-shaix {
  background-image: url("../../../../static/fifthg/imgs/ic_shaixuan.png");
  margin-left: 0.2 * 100rpx;
}

.prd-info {
  display: inline-block;
  background-color: #9BB9E6;
  color: #fff;
  font-size: 0.24 * 100rpx;
  padding-left: 0.2 * 100rpx;
  padding-right: 0.2 * 100rpx;
  height: 0.4 * 100rpx;
  line-height: 0.4 * 100rpx;
  border-radius: 0.06 * 100rpx;
  box-sizing: content-box;
  text-align: center;

}

.pkg-info {
  background-color: #EE904C;
  color: #fff;
}

.prd-info-permark {
  display: inline-block;
  color: #fff;
  font-size: 0.24 * 100rpx;
  padding-left: 0.2 * 100rpx;
  padding-right: 0.2 * 100rpx;
  height: 0.4 * 100rpx;
  line-height: 0.4 * 100rpx;
  border-radius: 0.06 * 100rpx;
  margin-left: 0.1 * 100rpx;
}

.sp-collect {
  display: inline-block;
  color: #fff;
  font-size: 0.24 * 100rpx;
  padding-left: 0.1 * 100rpx;
  padding-right: 0.1 * 100rpx;
  height: 0.4 * 100rpx;
  line-height: 0.4 * 100rpx;
  border-radius: 0.06 * 100rpx;
  color: #F53535;
  position: absolute;
  right: 0.5 * 100rpx;
  border: 1px solid #F53535;
  box-sizing: content-box;
}

.un-collect {
  color: #80A6DE;
  border: 1px solid #80A6DE;
}

.digit {
  background-color: #9BB9E6;
}

.cm {
  background-color: #9BB9E6;
}

.vod {
  background-color: #FB9999;
}

.ott {
  background-color: #F4C082;
}

.st {
  background-color: #00bcd4;
}

.dialog {
  height: 9 * 100rpx;
  background-color: #F2F5F8;
  border-radius: 0.08 * 100rpx;
  overflow: scroll;
}

.main-body /deep/ .weui-dialog {
  width: 6.9 * 100rpx !important;
  max-width: 6.9 * 100rpx !important;
}

/deep/ .weui-search-bar:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 0px solid #D7D6DC !important;
  color: #D7D6DC;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}

.dg-sp-sty {
  background-color: #fff;
  width: 2 * 100rpx;
  height: 0.6 * 100rpx;
  display: inline-block;
  overflow: hidden;
  margin-left: 0.2 * 100rpx;
  float: left;
  margin-top: 0.2 * 100rpx;
  border-radius: 0.08 * 100rpx;
  font-size: 0.26 * 100rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 0.6 * 100rpx;
}

.dg-sp-select {
  background-color: #3875D0;
  color: #fff;
}

.dg-title {
  margin-top: 40rpx;
  width: 6.9 * 100rpx;
  height: 0.6 * 100rpx;
  color: #999999;
  text-align: left;
  padding-left: 0.3 * 100rpx;
  box-sizing: border-box;
  line-height: 0.6 * 100rpx;
  font-size: .28 * 100rpx;
  font-weight: 400;
}

.dg-ct-box {
  display: inline-block;
  float: left;
}

.catalog-box {
  width: 6.9 * 100rpx;
  overflow-y: auto;
  box-sizing: border-box;
  margin-bottom: 0.3 * 100rpx;
}

.dg-opt-box {
  width: 6.9 * 100rpx;
  height: 0.8 * 100rpx;
  box-sizing: border-box;
  background-color: #fff;
}

.dg-opt-lft-sp {
  display: inline-block;
  width: 2 * 100rpx;
  height: 0.8 * 100rpx;
  line-height: 0.8 * 100rpx;
  float: left;
  font-size: 0.32 * 100rpx;

  font-weight: 400;
  color: #999999;
}

.dg-opt-right-sp {
  display: inline-block;
  width: 2 * 100rpx;
  height: 0.8 * 100rpx;
  line-height: 0.8 * 100rpx;
  float: right;
  font-size: 0.32 * 100rpx;

  font-weight: 400;
  color: #3875D0;
}

.search-input-sy {}

/deep/.weui-search-bar__label {
  position: absolute;
  top: 1px;
  right: 1px;
  bottom: 1px;
  left: 1px;
  z-index: 2;
  border-radius: 3px;
  text-align: center;
  color: #9B9B9B;
  background: #FFFFFF;
  height: 0.6 * 100rpx;
  line-height: 0.6 * 100rpx;
}

/deep/ .weui-search-bar__form {
  height: 0.6 * 100rpx;
  line-height: 0.6 * 100rpx;
}

/deep/ .weui-search-bar__label {
  height: 0.6 * 100rpx;
  line-height: 0.6 * 100rpx;
}

.weui-search-bar__box /deep/ .weui-icon-search {
  line-height: 0.6 * 100rpx;
}

/deep/ .weui-icon-search {
  height: 0.2 * 100rpx;
}

.search-box-keyword {
		display: flex;
		flex-direction: row;
		background-color: #fff;
		align-items: center;
		justify-content: space-between;
		border-radius: 28rpx;
		padding: 5rpx 25rpx;
		margin: 20upx;

		input {
			flex: 1;
		}
	}
</style>