<!-- 流程界面 -->
<template>
	<view class="content">
	  <!-- <action-bar class="action-bar" title="业务受理" leftIcon="back"></action-bar> -->
	  <view class="body">
		<view class="form">
		  <view class="card sim">
			<view class="field">
			  <view class="main">
				<view class="perfix">业务单号</view>
				<view class="body">{{stepInfo.data.GDM_TJYW.serialno}}</view>
			  </view>
			</view>
			<view class="field">
			  <view class="main">
				<view class="perfix">办理号码</view>
				<view class="body">{{stepInfo.data.GDM_M5G.resNum}}</view>
			  </view>
			</view>
			<view class="field">
			  <view class="main">
				<view class="perfix">SIM卡号</view>
				<view class="body">{{stepInfo.data.GDM_M5G.sim}}</view>
			  </view>
			</view>
			<view class="field">
			  <view class="main">
				<view class="perfix">客户姓名</view>
				<view class="body">{{stepInfo.data.GDM_YCCF.custname}}</view>
			  </view>
			</view>
			<view class="field">
			  <view class="main">
				<view class="perfix">移网套餐</view>
				<view class="body">{{stepInfo.data.GDM_XZTC.salesname}}</view>
			  </view>
			</view>
			<view class="field">
			  <view class="main">
				<view class="perfix">预存话费</view>
				<view class="body">{{stepInfo.data.GDM_YCCF.minFees}}</view>
			  </view>
			</view>
			<view class="field">
			  <view class="main">
				<view class="perfix">充返活动</view>
				<view class="body">{{stepInfo.data.GDM_YCCF.contract}}</view>
			  </view>
			</view>
		  </view>
		  <view class="card sim">
			<view class="field">
			  <view class="main">
				<view class="perfix">支付方式</view>
				<view class="body">{{stepInfo.data.GDM_XZTC.salesname}}</view>
			  </view>
			</view>
		  </view>
		</view>
		<view class="btns">
		  <view :class="['btn', {'active' : !btnVisible}]" @click="openAccount()">立即支付</view>
		</view>
	  </view>
	  
	</view>
  </template>
  <script>
  import Vue from 'vue'
  import {
	mapGetters,
	mapActions,
	mapMutations
  } from 'vuex';
  import CLayout from "../../components/layout/CLayout";
  import { Popup, Step, Steps } from 'vant'
  Vue.use(Popup)
  Vue.use(Step)
  Vue.use(Steps)
  
  import {
	v4 as uuidv4
  } from 'uuid';
  export default {
	data() {
	  return {
		btnVisible: false,
		isSend: false,
	  };
	},
	components: { CLayout },
  
	computed: {
	  ...mapGetters({
		custInfo: "getCustInfo",
		stepInfo: "getStepInfo",
		salesInfo: 'getSalesInfo',
	  }),
	},
  
	mounted(){
	  console.log(this.stepInfo)
	},
  
	methods: {
	  ...mapActions({
		queProcess: 'queProcess',
		needAppAgreement: 'needAppAgreement',
		saveProcess: 'saveProcess',
	  }),
  
	  ...mapMutations({
		resetStep: "resetStepInfo",
		resetSubmitData: "resetSubmitData"
	  }),
  
	},
  }
  </script>
  
  <style lang='scss' scoped>
  .body {
	padding: 20rpx;
  }
  .card {
	margin-bottom: 20rpx;
	border-radius: 10rpx;
	padding: 26rpx 20rpx;
	background-color: #fff;
  }
  
  .card.address {
	display: flex;
	align-items: flex-start;
	
	.icon-personal {
	  width: 40rpx;
	  height: 40rpx;
	  margin-top: 10rpx;
	  background-image: url(../../../../static/fifthg/2023/icon_personal.png);
	  background-repeat: no-repeat;
	  background-size: 40rpx 40rpx;
	}
  
	.body {
	  flex: 1;
	  margin-left: 20rpx;
  
	  .h1 {
		font-size: 28rpx;
		font-weight: bold;
		color: #212121;
	  }
  
	  .p {
		font-size: 28rpx;
		color: #999;
	  }
	}
  }
  
  .card.sim {
	display: flex;
	flex-direction: column;
  
	.field {
	  margin-top: 20rpx;
	  border-top: solid 1px #f1f1f1;
	  padding-top: 20rpx;
  
	  &:nth-child(1) {
		margin-top: 0;
		border-top: none;
		padding-top: 0;
	  }
  
	  .main {
		flex: 1;
		display: flex;
		align-items: flex-start;
		font-size: 28rpx;
  
		.perfix {
		  position: relative;
		  padding-right: 22rpx;
  
		  .red {
			position: absolute;
			top: 4rpx;
			right: 0;
			font-size: 42rpx;
			color: #f00;
		  }
  
		  .sub {
			padding-left: 6rpx;
			font-weight: normal;
			color: #F9A63F;
		  }
		}
  
		.body {
		  flex: 1;
		  padding: 0 20rpx;
		  font-weight: 500;
  
		  .text {
			line-height: 2em;
			color: #1F8FF7;
  
			&.placeholder {
			  font-weight: normal;
			  color: #999;
			}
  
			&.expire {
			  color: #f00;
			}
		  }
  
		  .input {
			display: block;
			width: 100%;
			border: none;
			padding: 10rpx 0;
			line-height: 2em;
  
			.input-placeholder {
			  font-weight: normal;
			  color: #999;
			}
  
			.uni-input-input {
			  line-height: 2em;
			}
		  }
  
		  .radio-group {
			display: flex;
			align-items: center;
  
			.item {
			  flex: 1;
			  display: flex;
			  align-items: center;
  
			  .status {
				position: relative;
				width: 26rpx;
				height: 26rpx;
				margin-right: 10rpx;
				border: solid 1px #212121;
				border-radius: 50%;
  
				&.checked {
				  border-color: #1F8FF7;
				  background-color: #1F8FF7;
				  background-image: url(../../../../static/fifthg/2023/icon-check.png);
				  background-position: center center;
				  background-repeat: no-repeat;
				  background-size: 20rpx 20rpx;
				}
			  }
			}
		  }
		}
  
		.suffix {
		  align-self: stretch;
  
		  .btn {
			border: solid 1px #0E67B9;
			border-radius: 40rpx;
			padding: 6rpx 20rpx;
			font-weight: normal;
			color: #0E67B9;
		  }
  
		  .icon-arw {
			width: 20rpx;
			height: 100%;
			background-image: url(../../../../static/fifthg/2023/icon-arw.png);
			background-position: center center;
			background-repeat: no-repeat;
			background-size: 20rpx 20rpx;
		  }
		}
	  }
  
	  .extra {
		padding-top: 10rpx;
		font-size: 28rpx;
		color: #999;
	  }
	}
  
	.check-group {
  
	  .item {
		display: flex;
		align-items: center;
		margin: 10rpx 0;
		border-radius: 10rpx;
		padding: 20rpx;
		background-color: #F0F8FF;
  
		.checkbox {
		  display: flex;
		  align-items: center;
		}
  
		.status {
		  position: relative;
		  width: 26rpx;
		  height: 26rpx;
		  margin-right: 10rpx;
		  border: solid 1px #0E67B9;
		  border-radius: 50%;
  
		  &.checked {
			border-color: #1F8FF7;
			background-color: #1F8FF7;
			background-image: url(../../../../static/fifthg/2023/icon-check.png);
			background-position: center center;
			background-repeat: no-repeat;
			background-size: 20rpx 20rpx;
		  }
		}
  
		.label {
		  color: #0E67B9;
		}
	  }
	}
  
	.contracts {
	  margin: 10rpx 0;
	  border-radius: 10rpx;
	  border: none;
	  padding: 20rpx;
	  background-color: #FFF1E1;
  
	  .text {
		text-align: right;
		color: #F9A63F!important;
	  }
  
	  .icon-arw {
		background-image: url(../../../../static/fifthg/2023/icon-arw-yellow.png)!important;
	  }
	}
  
  }
  
  .btns {
	margin-top: 60rpx;
  
	.btn {
	  border-radius: 60rpx;
	  padding: 20rpx;
	  background-color: #1F8FF7;
	  color: #fff;
	  font-size: 32rpx;
	  text-align: center;
	}
  }
  
  .dialog {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 10;
  
	.mask {
	  position: absolute;
	  top: 0;
	  left: 0;
	  width: 100%;
	  height: 100%;
	  background-color: rgba(0, 0, 0, .4);
	  z-index: 1;
	}
  
	.sheet {
	  position: absolute;
	  bottom: 0;
	  left: 0;
	  width: 100%;
	  background-color: #fff;
	  z-index: 10;
  
	  .head {
		position: relative;
		padding: 20rpx;
		border-bottom: solid 1px #EAEAEA;
  
		.title {
		  padding: 10rpx;
		  font-size: 32rpx;
		  font-weight: bold;
		  color: #212121;
		  text-align: center;
		}
  
		.close {
		  position: absolute;
		  top: 50%;
		  right: 20rpx;
		  width: 40rpx;
		  height: 40rpx;
		  background-image: url(../../../../static/fifthg/2023/icon-close.png);
		  background-repeat: no-repeat;
		  background-size: 40rpx 40rpx;
		  transform: translate(0, -50%);
		}
	  }
  
	  .body {
		padding: 20rpx;
  
		.list {
		  max-height: 600rpx;
		  overflow: auto;
  
		  .item {
			display: flex;
			align-items: center;
			margin: 20rpx 0;
			border: solid 1px transparent;
			border-radius: 10rpx;
			padding: 20rpx;
			background-color: #F3F3F3;
			font-size: 28rpx;
			color: #212121;
  
			&.active {
			  border-color: #1F8FF7;
			  background-color: #F8FCFF;
			  color: #1F8FF7;
			}
  
			.addr {
			  flex: 1;
			}
  
			.radiobox {
			  width: 40rpx;
			  height: 40rpx;
			  margin-left: 10rpx;
			  border: solid 1px #666;
			  border-radius: 50%;
  
			  &.checked {
				border-color: #1F8FF7;
				background-color: #1F8FF7;
				background-image: url(../../../../static/fifthg/2023/icon-check.png);
				background-position: center center;
				background-repeat: no-repeat;
				background-size: 20rpx 20rpx;
			  }
			}
		  }
		}
		
		.input {
		  border-radius: 40rpx;
		  padding: 20rpx 40rpx;
		  background-color: #F3F3F3;
		  line-height: 2em;
		}
	  }
  
	  .foot {
		display: flex;
		align-items: center;
		padding: 20rpx;
		margin: 40rpx 0;
  
		.btn {
		  flex: 2;
		  border-radius: 60rpx;
		  padding: 20rpx;
		  margin: 0 10rpx;
		  background-color: #1F8FF7;
		  color: #fff;
		  font-size: 32rpx;
		  text-align: center;
  
		  &.sub {
			flex: 1;
			background-color: #DEEBFF;
			color: #1F8FF7;
		  }
		}
  
  
	  }
	}
  }
  </style>
  