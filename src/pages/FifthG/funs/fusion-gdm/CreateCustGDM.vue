<template>
  <view class="customersAndBusiness">
    <!-- <action-bar style="width: 100%;" title="新增客户" leftIcon="back"></action-bar> -->
    <add-customers ref="addCustomers" v-if="!loading" @createDone="createDone"></add-customers>

<!--    <view class="customersAndBusiness-btn">-->
<!--      <view :class="['btn', {'active' : !btnVisible}]" @click="customersBtn()">-->
<!--        提&nbsp;交-->
<!--      </view>-->
<!--    </view>-->
  </view>
</template>

<script>
  import { mapState, mapGetters, mapActions, mapMutations } from 'vuex'
  import { areaByCity, customerType, cardType, scopeType } from '@/store/modules/sysparam.js'
  import AddCustomers from '../../components/addCustomers.vue'
	export default {
    name: 'customersAndBusiness',
		data() {
			return {
        btnVisible: false,
        loading: false,
      }
		},
		components: {
      AddCustomers,
    },
		computed: {
      ...mapGetters({
        userInfo: "getUserInfo",
        sysparam: 'getSysparam',
        getDatas: 'getDatas',
      }),
    },
    onLoad() {
      this.InitData()
    },
		methods: {
      ...mapActions({
      	InitDigital: 'init',
        querySysData: 'querySysData',
			  queProcess: 'queProcess',
      }),
      async InitData () {
        this.loading = true
        let queryParams = {
          key: areaByCity.gcode,
          params: {
            ...areaByCity,
            mcode: this.userInfo && this.userInfo.city ? this.userInfo.city : '',
          }
        }
        await this.querySysData(queryParams)
        await this.InitDigital([customerType, cardType, scopeType])
        this.loading = false
      },
      customersBtn () {
        this.$refs.addCustomers.submit()
      },
      async createDone(e) {
        const {responseBody: data} = await this.queProcess({
          "pcode":"NGDM","custid": e.custid,"custname": e.custname
        });

        this.$rauter.navigateTo({
          url: 'FusionGDM',
          data: {
            poid: data.poid
          }
        });
      }
    }
	}
</script>

<style lang='scss' scoped>
.customersAndBusiness {
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  view {
    box-sizing: border-box;
  }
  &-btn {
    width: 100%;
    overflow: hidden;
    padding: 30rpx 40rpx;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 9;
    .btn {
      width: 100%;
      text-align: center;
      overflow: hidden;
      color: #fff;
      border-radius: 60rpx;
      line-height: 100rpx;
      height: 100rpx;
      font-size: 28rpx;
      background-color: #bfbfbf;
      &.active {
        background-color: #FA8C16;
      }
    }
  }
}
</style>
