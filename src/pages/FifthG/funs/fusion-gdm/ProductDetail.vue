<template>
	<view class="display-flex-column" style="height: 100vh;">
		<!-- <action-bar title="商品详情" leftIcon="back"></action-bar> -->

		<view v-if="singleProduct" class="select-phone">
			<uni-section title="选择订购的手机号" type="line"></uni-section>
			<PickPhone style="" ref="phonePic">
			</PickPhone>
		</view>

		<scroll-view :style="{ height: scrollHeight + 'px' }" :scroll-y="true">
			<view class="display-flex-column product-msg-group">
				<image class="head-img" v-for="(imageMBean, index) in productDetail.imageM" :key="index" :src="imageMBean">
				</image>
				<text class="knowname-txt">{{ productDetail.knowname }}</text>
				<text class="priceDisplay-txt">{{ productDetail.tips || productDetail.priceDisplay }}</text>
				<text class="desc-txt text-main">{{ productDetail.knowdesc }}</text>
				<image mode="widthFix" class="bottom-img" v-for="(imageDBean, index) in productDetail.imageD" :key="index"
					:src="imageDBean"></image>
			</view>
<!--			<view v-for="(item, index) in products" :key="item.objid" class="products-box">-->
<!--				<view class="products-title-box">-->
<!--					<text @click="onSelectProduct(item)"-->
<!--						:class="`product-checkbox iconfont ${selectObjid === item.objid ? 'icon-a-31Exuanzhong' : 'icon-fuxuanweicheckbox'}`"-->
<!--						:style="{ color: `${selectObjid === item.objid ? '#1890FF' : '#333333'}` }"></text>-->
<!--					<text @click="onSelectProduct(item)" class="product-name"-->
<!--						:style="{ color: `${selectObjid === item.objid ? '#1890FF' : '#333333'}` }">{{ item.knowname }}</text>-->
<!--					<text class="show-desc-btn iconfont icon-zhankai" :ref="`showDescBtn${index}`"-->
<!--						@click="onShowDesc(item, `showDescBtn${index}`)"></text>-->
<!--				</view>-->
<!--				<view class="products-desc-box hide" :ref="`showDescBtn${index}Desc`">-->
<!--					{{ item.offerdesc }}-->
<!--				</view>-->
<!--			</view>-->

<!--      <uni-section v-if="packageList && packageList.length > 0" title="可选包订购" type="line">-->
<!--        <view v-for="(item, index) in packageList" :key="item.objid" class="products-box">-->
<!--          <view class="products-title-box">-->
<!--            <text @click="onSelectPackage(item)"-->
<!--                  :class="`product-checkbox iconfont ${isSelected(item)  ? 'icon-a-31Exuanzhong' : 'icon-fuxuanweicheckbox'}`"-->
<!--                  :style="{ color: `${isSelected(item) ? '#1890FF' : '#333333'}` }"></text>-->
<!--            <text @click="onSelectPackage(item)" class="product-name"-->
<!--                  :style="{ color: `${isSelected(item)  ? '#1890FF' : '#333333'}` }">{{ item.knowname }}</text>-->
<!--            <text class="show-desc-btn iconfont icon-zhankai" :ref="`showPkgDescBtn${index}`"-->
<!--                  @click="onShowByPkgDesc(item, `showPkgDescBtn${index}`)"></text>-->
<!--          </view>-->
<!--          <view class="products-desc-box hide" :ref="`showPkgDescBtn${index}Desc`">-->
<!--            {{ item.knowdesc }}-->
<!--          </view>-->
<!--        </view>-->
<!--      </uni-section>-->

      <view class="card sim" v-if="products.length">
        <view class="field">
          <view class="main tit">
            <view class="perfix">促销套餐</view>
            <view class="body">
              <view class="text placeholder"></view>
            </view>
            <view class="suffix" @click="show = true">
              <view class="icon-arw">选促销</view>
            </view>
          </view>
        </view>

        <view class="check-group">
          <view v-if="selectProducts.size > 0">
            <view class="item" v-for="(i,k) in selectProducts" :key="k">
              <view class="checkbox">
                <view class="label">
                  <view class="t">{{ i.knowname }}</view>
                  <view class="d">{{ i.offerdesc }}</view>
                </view>
              </view>
            </view>
          </view>
<!--          <view class="item" v-if="selectProductObj">-->
<!--            <view class="checkbox">-->
<!--              <view class="label">-->
<!--                <view class="t">{{ selectProductObj.knowname }}</view>-->
<!--                <view class="d">{{ selectProductObj.offerdesc }}</view>-->
<!--              </view>-->
<!--            </view>-->
<!--          </view>-->
        </view>
      </view>

      <view class="card sim" v-if="packageList.length">
        <view class="field">
          <view class="main tit">
            <view class="perfix">专属可选包</view>
            <view class="body">
              <view class="text placeholder"></view>
            </view>
            <view class="suffix" @click="openDialog">
              <view class="icon-arw">选可选包</view>
            </view>
          </view>
        </view>

        <view class="check-group">
          <view class="item" v-for="n, i in selectPkgList" :key="i">
            <view class="checkbox">
              <view class="label">
                <view class="t">{{ n.knowname }}</view>
                <view class="d">{{ n.knowdesc }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view style="height: 1em;line-height: 1em;"></view>

		</scroll-view>


    <view class="dialog" v-show="show">
      <view class="mask"></view>
      <view class="sheet">
        <view class="head">
          <view class="perfix" @click="show = false">取消</view>
          <view class="title">选择促销</view>
          <view class="suffix" @click="confirmProduct">确定</view>
        </view>
        <view class="body">
          <view class="list">
            <view class="item" :class="{ active: n.isChecked }" v-for="(n, i) in products" :key="i" @click="onSelectProduct(n, i)">
              <view class="ctn">
                <view class="title">{{ n.knowname }}</view>
                <view class="memo ellipsis-text">{{ n.offerdesc }}</view>
              </view>
              <view class="radiobox" :class="{ checked: n.isChecked }"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="dialog" v-show="dialog">
      <view class="mask"></view>
      <view class="sheet">
        <view class="head">
          <view class="perfix" @click="dialog = false">取消</view>
          <view class="title">选择可选包</view>
          <view class="suffix" @click="confirmDialog">确定</view>
        </view>
        <view class="body">
          <uni-search-bar placeholder="请输入可选包关键词搜索" @confirm="search"></uni-search-bar>
          <view class="list">
            <view class="item" :class="{ active: n.isChecked }" v-for="n, i in packageList" :key="i" @click="changeSales(n, i)">
              <view class="ctn">
                <view class="title">{{ n.knowname }}</view>
                <view class="memo">{{ n.knowdesc }}</view>
              </view>
              <view class="radiobox" :class="{ checked: n.isChecked }"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="display-flex-row btn-bottom-group ">
      <view class="collect-btm display-flex-column border-top" @click="collect">
        <text class="iconfont" :style="{ color: productDetail.collect ? '#FF7B3A' : '#EBEBEB' }">&#xe60c;</text>
        <text style="line-height: 1.5;">收藏</text>
      </view>
      <text class="order-btm" @click="order">{{ orderTxt }}</text>
    </view>

<!--		<view class="btns-bottom-box">-->
<!--			<view class="btn-box" @click="collect">-->
<!--				<view style="line-height: 1.8em">-->
<!--					<text class="iconfont" :style="{ color: productDetail.collect ? '#FF7B3A' : '#EBEBEB' }">&#xe60c;</text>-->
<!--				</view>-->
<!--				<view style="line-height: 1.8em">收藏</view>-->
<!--			</view>-->
<!--			<view class="btn-box" @click="order" style="background-color: #1890FF;color: #FFFFFF">{{ orderTxt }}</view>-->
<!--		</view>-->


	</view>
</template>

<script>
import {
	mapGetters,
	mapActions,
	mapMutations
} from 'vuex';
import {
	GDM
} from "@/store/modules/step.js"
import PickPhone from "../../components/PickPhone.vue"
export default {
	data() {
		return {
			productDetail: {},
			scrollHeight: 0,
			delta: 0,
			pageParams: {}, //页面参数
			type: "", //订购类型，目前有转购
			products: [], // 优惠套餐集合
			selectObjid: "", // 优惠套餐id
			selectProductObj: null,
      packageList: [], //可选包列表
      selectPkgList: [],
      dialog: false,
      dialogSelect: [],
      storePkgList: [],
      show: false,
      selectProduct: null,
      selectProducts: new Set(),
		};
	},
	created() { },

	onLoad(data) {
		console.log(data)
		this.pageParams = data;
		this.productDetail = JSON.parse(data.product)
		// 加载优惠套餐
		this.onLoadDiscountPackage(this.productDetail.objid)
    this.onLoadSalesPackagePrd(this.productDetail.objid)
		this.delta = data.delta
		// this.scrollHeight = uni.getSystemInfoSync().windowHeight - 94
    this.scrollHeight = uni.getSystemInfoSync().windowHeight - 50
		this.$nextTick(() => {
			if (this.singleProduct) {
				let query = uni.createSelectorQuery().in(this);
				query.select(".select-phone").boundingClientRect(data => {
					this.scrollHeight = this.scrollHeight - data.height - 20;
				}).exec();
			}
		})
	},
	components: {
		PickPhone
	},

	computed: {
		...mapGetters({
			custInfo: "getCustInfo",
			stepInfo: "getStepInfo",
			pageConfig: "getPageConfig",
			discountProd: "getDiscountProd"
		}),

		//单独的商品订购
		singleProduct() {
			return this.pageParams.type == "singleProduct"
		},

		//套餐转购
		zhuangou() {
			return this.pageParams.type == "zhuangou"
		},

		//优惠商品订购
		discount() {
			return this.pageParams.type == "discount"
		},

		// 副卡绑定套餐
		cardUniBind() {
			return this.pageParams.type == "cardUniBind"
		},

		orderTxt() {
			let orderTxt = "办理套餐"
			if (this.singleProduct) {
				orderTxt = "订购商品"
			} else if (this.zhuangou) {
				orderTxt = "转购商品"
			} else if (this.cardUniBind) {
				orderTxt = "副卡解绑"
			}
			return orderTxt;
		}
	},

	methods: {
		...mapActions({
			queProcess: 'queProcess',
			saveProcess: 'saveProcess',
		}),
		...mapMutations({
			setRepurchaseProds: "setRepurchaseProds",
			addDiscountProds: "addDiscountProds",
			setCardUniBindProds: "setCardUniBindProds",
			discountProdSubmit: "discountProdSubmit"
		}),
		onShowDesc(item, refName) {
			const vm = this
			if (item.showDesc === undefined) {
				item.showDesc = false
			}
			item.showDesc = !item.showDesc
			console.log(item.showDesc)

			const icons = vm.$refs[refName]
			if (icons && icons.length > 0) {
				const icon = icons[0]
				icon.$el.className = 'show-desc-btn iconfont ' + (item.showDesc ? 'icon-shouqi' : 'icon-zhankai')
			}

			const boxs = vm.$refs[refName + 'Desc']
			if (boxs && boxs.length > 0) {
				const box = boxs[0]
				box.$el.className = item.showDesc ? 'show' : 'hide'
			}

		},
		collect() {
			this.$http.post(this.$apis.collectSales, {
				"knowid": this.productDetail.knowid,
				"type": this.productDetail.collect ? 0 : 1
			}).then(resp => {
				uni.$emit("collect", JSON.parse(JSON.stringify(this.productDetail)))
				this.productDetail.collect = !this.productDetail.collect;
			});

		},

		order() {
			this.doOrder()
		},

		doOrder() {
			// const hasMainNums = this.getMainNums()
			// if (!hasMainNums) {
			// 	return
			// }
			if (this.singleProduct) {
				//订购
				this.orderProduct()
			} else if (this.zhuangou) {
				//转购
				this.repurchaseProds()
			} else if (this.discount) {
				//优惠套餐
				this.orderDiscount()
			} else if (this.cardUniBind) {
				//副卡绑定
				this.additionalCardUnibind()
			} else {
				this.stepOrder()
			}
		},

		//优惠套餐
		orderDiscount() {
			this.addDiscountProds(this.productDetail)
		},

		// 副卡绑定
		additionalCardUnibind() {
			const products = {
				product: this.productDetail,
				disProduct: this.selectProductObj
			}
			this.setCardUniBindProds(products)
			console.log("副卡绑定成功")

			// 触发onShow和onLoad
			// navigateTo，redirectTo   navigateBack，reLaunch
			// this.$rauter.redirectTo({
			// 	url: 'additionalCardUnibind',
			// 	data: this.pageParams
			// })
			this.$rauter.navigateBack({
				delta: 2
			});
		},

		//转购
		repurchaseProds() {
			const products = {
				product: this.productDetail,
				disProduct: this.selectProductObj
			}
			this.setRepurchaseProds(products)
			this.$rauter.navigateBack({
				delta: 2
			});
			// this.$rauter.redirectTo({
			// 	url: 'prodRepurchase',
			// 	data: {
			// 		phone: this.pageParams.phone
			// 	}
			// })
		},

		//普通的订购
		orderProduct() {
			console.log(this.custInfo)
			let params = {
				"addr": this.custInfo.linkaddr,
				"custid": this.custInfo.custid,
				"iscrtorder": "Y",
				"custname": this.custInfo.custname,
				"packs": [{
					"servid": this.$refs.phonePic.getPickBean().servid,
					"salesid": this.productDetail.objid,
					"optype": "A"
				}],
				// "patchid": this.custInfo.patchid,
				// "verifyphone": "13200000000",
				// "whladdr": this.custInfo.patchid
			}
			this.$http.post(this.$apis.m5gBuyGoods, params).then(resp => {
				let res = response.responseBody
				if (res && res.custOrderid) {
					if (!res.custorderid) res.custorderid = res.custOrderid
					this.setOrderInfo(res)
					this.$rauter.navigateTo({
						url: 'orderDetail',
					})
				}
			});
		},

		// 查询副卡开户是否有主卡
		// getMainNums() {
		// 	if (this.productDetail.knowname.indexOf('副卡') === -1) {
		// 		return true
		// 	}
		// 	if (!this.custInfo) {
		// 		this.$util.feeback.showToast({
		// 			title: '客户信息失效，请重新定位客户'
		// 		})
		// 		return false
		// 	}
		// 	let servs = this.custInfo.servs
		// 	let mainNums = [];
		// 	for (let serv of servs) {
		// 		for (let devBean of serv.servDetails) {
		// 			if (devBean.servtype == "0" //设备为主机
		// 				&&
		// 				devBean.permark == "B" //5G业务
		// 				&&
		// 				devBean.keyno //存在手机号码
		// 			) {
		// 				//当找到数字业务的设备的时候就停止当前遍历，进入下一个遍历
		// 				mainNums.push({
		// 					mname: devBean.keyno,
		// 					mcode:devBean.servid
		// 				})
		// 				break;
		// 			}
		// 		}
		// 	}
		// 	if (!mainNums || mainNums.length < 1) {
		// 		this.$util.feeback.showToast({
		// 			title: '当前客户无主卡信息，不允许办理副卡'
		// 		})
		// 		return false
		// 	}
		// 	return true
		// },

		//开户的订购
		async stepOrder() {
			console.log(this.stepInfo)
			let delta = this.delta ? parseInt(this.delta) : 0
			if (delta > 0 && this.stepInfo && this.stepInfo.data["GDM_M5G"]) {
				//重新选择商品，需要判断当前商品是否适合订购
				let price = this.productDetail.price
				//步骤中的号码价格
				let depositAmountYuan = this.stepInfo.data["GDM_M5G"].depositAmountYuan
				//对比套餐价格是否能够订购吉祥号
				if (depositAmountYuan > price) {
					uni.showToast({
						title: '所选套餐不能订购吉祥号',
						icon: 'none'
					})
					return;
				}
			}
			let data = {
				"pcode": 'NGDM',
				"custid": this.custInfo ? this.custInfo.custid : "",
				"custname": this.custInfo ? this.custInfo.custname : "",
				"salesid": this.productDetail.knowid,
				"salesname": this.productDetail.knowname,
				objid: this.selectProductObj?.objid,
				knowname: this.selectProductObj?.knowname,
			}

      if (this.selectPkgList && this.selectPkgList.length > 0) {
        let pkgList = []
        this.selectPkgList.forEach(item => {
          pkgList.push(item.objid)
        })

        data.pkginfo = pkgList.join(',')
        console.log('data.pkginfo', data.pkginfo)
      }

      if (this.selectProducts.size > 0) {
        let list = Array.from(this.selectProducts).map(item => ({
          objid: item.objid,
          knowname: item.knowname
        }));
        data.discountpkgs = JSON.stringify(list)
        data.knowname = list.map(item => item.knowname).join('\n')
      } else {
        data.discountpkgs = '0'
      }

			await this.queProcess({
				...data,
				poid: this.stepInfo.poid,
			})
			this.saveProcess({
				data: {
					poid: this.stepInfo.poid,
					cpcode: 'GDM_XZTC',
					data: JSON.stringify(data),
          			"temp": "1"
				},
				unNeedNext: true
			})

			if (delta <= 0) {
				//如果不存在step.vue，直接跳转
				this.$rauter.navigateTo({
					url: "FusionGDM",
					data: {
						poid: this.stepInfo.poid
					}
				});
			} else {
				//如果存在step.vue，返回
				this.$rauter.navigateBack({
					delta: delta,
				});
			}
		},

		// 加载优惠套餐
		onLoadDiscountPackage(objid) {
			if (objid) {
				const vm = this;
				vm.$http.post(vm.$apis.queCompaignoffer, {
					objid: objid
				}).then((resp) => {
          resp.responseBody.data.forEach(n => {
            n.isChecked = false
          })
					vm.products = resp.responseBody.data;
				});
			}
		},
		// 选择优惠套餐
		onSelectProduct(item, index) {
      console.log('item', item, index)
      item.isChecked = !item.isChecked
      if (this.selectProducts.has(item)) {
        this.selectProducts.delete(item)
      } else {
        this.selectProducts.add(item)
      }
      console.log('this.selectProducts', this.selectProducts,this.selectProducts.size)
      // const vm = this
      // console.log('item', item, index)
      // if (item.isChecked) {
      //   // 如果已经选中，则取消选中状态
      //   item.isChecked = false;
      //   vm.selectProduct = null;
      // } else {
      //   // 如果未选中，则选中当前项目
      //   // 取消其他项目的选中状态
      //   vm.products.forEach((product) => {
      //     product.isChecked = false;
      //   });
      //
      //   item.isChecked = true;
      //   vm.selectProduct = item;
      // }
		},
    onLoadSalesPackagePrd(objid) {
      if (objid) {
        const vm = this;
        vm.$http.post(vm.$apis.getSalesPackagePrd, {
          objid: objid
        }).then((resp) => {
          console.log('resp.responseBody', resp.responseBody)
          if (resp && resp.responseBody.salesList.length > 0) {
            resp.responseBody.salesList.forEach(n => {
              n.isChecked = false
            })
            vm.storePkgList = resp.responseBody.salesList
            vm.packageList = resp.responseBody.salesList
            console.log('packageList', vm.packageList)
          }

        });
      }
    },
    onSelectPackage(item) {
      if (this.isSelected(item)) {
        // 如果已经选中，则取消选中
        this.selectPkgList = this.selectPkgList.filter(pkg => pkg.objid !== item.objid);
      } else {
        // 否则添加到选中列表中
        this.selectPkgList.push(item);
      }
      console.log('selectPkgList', this.selectPkgList)
    },
    onShowByPkgDesc(item, refName) {
      const vm = this
      if (item.showDesc === undefined) {
        item.showDesc = false
      }
      item.showDesc = !item.showDesc
      console.log(item.showDesc)

      const icons = vm.$refs[refName]
      if (icons && icons.length > 0) {
        const icon = icons[0]
        icon.$el.className = 'show-desc-btn iconfont ' + (item.showDesc ? 'icon-shouqi' : 'icon-zhankai')
      }

      const boxs = vm.$refs[refName + 'Desc']
      if (boxs && boxs.length > 0) {
        const box = boxs[0]
        box.$el.className = item.showDesc ? 'show' : 'hide'
      }

    },
    isSelected(item) {
      // 检查一个包是否已经被选中
      return this.selectPkgList.some(pkg => pkg.objid === item.objid);
    },
    openDialog() {
      this.packageList.forEach(n => {
        let has = this.selectPkgList.filter(m => m.knowname == n.knowname)

        n.isChecked = has.length > 0
      })
      this.dialog = true
    },

    confirmDialog() {
      this.selectPkgList = [...this.dialogSelect]
      this.dialog = false
    },

    changeSales(item, index) {
      console.log('item', item, index)
      this.packageList.map((n, i) => {
        if(i == index) {
          n.isChecked = !n.isChecked
        }
      })
      console.log(this.packageList)
      this.dialogSelect = this.packageList.filter(n => n.isChecked)
    },

    search(e) {
      this.packageList = this.storePkgList.filter(n => n.knowname.includes(e.value))
      this.dialogSelect = []
    },
    confirmProduct() {
      // if (this.selectProduct) {
      //   this.selectProductObj = this.selectProduct
      // }else {
      //   this.selectProductObj = null
      // }
      this.show = false
    },
	}
}
</script>

<style lang='scss' scoped>
.products-box {
  //border: 1px solid #EEEEEE;
  border: 1px solid #91D5FF;
  //border-radius: 1em;
  border-radius: 8rpx;
  margin: 20rpx;
  padding: 20rpx;
  //background-color: #E6F7F3;
  background-color: #E6F7FF;

  .products-title-box {
    font-weight: bold;
    font-size: 1em;
    display: flex;
    justify-content: space-between;

    .product-checkbox {
      font-size: 1.2em;
    }

    .product-name {
      flex-grow: 1;
      margin-left: 0.8em;
    }

    .show-desc-btn {
      font-size: 1em;
    }
  }
}



.btns-bottom-box {
  display: flex;
  height: 3.6em;
  background-color: #fff;
  border-top: 1px solid #eeeeee;
  width: 100%;
  margin-top: 20rpx;
  position: absolute;
  bottom: 0;
  z-index: 1;

  .btn-box {
    flex: 1;
    line-height: 3.6em;
    text-align: center;
  }
}

.select-phone {
  border: 1rpx solid #0084FF;
  padding: 20rpx 40rpx 0 40rpx;
  margin: 20rpx 20rpx;
  border-radius: 10rpx;

  /deep/ .uni-section-header {
    padding: 0;
  }
}

.product-msg-group {
  margin: 11px;
  padding: 11px;
  border-radius: 5px;
  background-color: #fff;

  .head-img {
    width: 100%;
    height: 500rpx;
  }

  .knowname-txt {
    margin-left: 30rpx;
    color: #101010;
    font-weight: bold;
  }

  .priceDisplay-txt {
    margin-left: 30rpx;
    color: #FF4D4F;
  }

  .desc-txt {
    margin-left: 30rpx;
  }

  .bottom-img {
    width: 100%;
    /* min-height: 600rpx; */
  }
}


.btn-bottom-group {
  height: 100rpx;

  .order-btm {
    flex: 2;
    text-align: center;
    line-height: 100rpx;
    color: #FFFFFF;
    background: #1890FF;
  }

  .collect-btm {
    flex: 1;
    text-align: center;
  }
}

/deep/ .uni-section-header {
  padding: 0 22rpx;
  margin-top: 20rpx;
}

.card {
  margin: 20rpx;
  border-radius: 10rpx;
  padding: 26rpx 20rpx;
  background-color: #fff;
}

.card.sim {
  display: flex;
  flex-direction: column;

  .field {
    margin-top: 20rpx;
    border-top: solid 1px #D8D8D8;
    padding-top: 20rpx;

    &:nth-child(1) {
      margin-top: 0;
      border-top: none;
      padding-top: 0;
    }

    .main {
      flex: 1;
      display: flex;
      align-items: flex-start;
      font-size: 28rpx;

      .perfix {
        position: relative;
        padding-right: 22rpx;

        .red {
          position: absolute;
          top: 4rpx;
          right: 0;
          font-size: 42rpx;
          color: #f00;
        }

        .sub {
          padding-left: 6rpx;
          font-weight: normal;
          color: #F9A63F;
        }
      }

      .body {
        flex: 1;
        padding: 0 20rpx;

        .text {
          line-height: 2em;
          color: #1F8FF7;

          &.placeholder {
            font-weight: normal;
            color: #999;
          }
        }

        .input {
          display: block;
          width: 100%;
          border: none;
          padding: 10rpx 0;
          line-height: 2em;

          .input-placeholder {
            font-weight: normal;
            color: #999;
          }

          .uni-input-input {
            line-height: 2em;
          }
        }

        .radio-group {
          display: flex;
          align-items: center;

          .item {
            flex: 1;
            display: flex;
            align-items: center;

            .status {
              position: relative;
              width: 26rpx;
              height: 26rpx;
              margin-right: 10rpx;
              border: solid 1px #212121;
              border-radius: 50%;

              &.checked {
                border-color: #1F8FF7;
                background-color: #1F8FF7;
                background-image: url(../../../../static/fifthg/2023/icon-check.png);
                background-position: center center;
                background-repeat: no-repeat;
                background-size: 20rpx 20rpx;
              }
            }
          }
        }
      }

      .suffix {
        align-self: stretch;

        .btn {
          border: solid 1px #0E67B9;
          border-radius: 40rpx;
          padding: 6rpx 20rpx;
          font-weight: normal;
          color: #0E67B9;
        }

        .icon-arw {
          position: relative;
          padding-right: 30rpx;

          &::after {
            content: "";
            display: block;
            position: absolute;
            right: 0;
            top: 0;
            width: 20rpx;
            height: 100%;
            background-image: url(../../../../static/fifthg/2023/icon-arw.png);
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 20rpx 20rpx;
          }
        }
      }

      &.tit {
        .perfix {
          position: relative;
          padding-left: 20rpx;
          font-weight: bold;

          &::after {
            content: "";
            display: block;
            position: absolute;
            top: 50%;
            left: 0;
            width: 6rpx;
            height: 30rpx;
            border-radius: 4rpx;
            background-color: #0084FF;
            transform: translate(0, -50%);
          }
        }

        .suffix .icon-arw {
          color: #1F8FF7;
        }
      }
    }

    .extra {
      padding-top: 10rpx;
      font-size: 28rpx;
      color: #999;
    }
  }

  .check-group {

    .item {
      display: flex;
      align-items: center;
      margin: 10rpx 0;
      border-radius: 10rpx;
      padding: 20rpx;
      background-color: #F0F8FF;

      .checkbox {
        display: flex;
        align-items: center;
      }

      .status {
        position: relative;
        width: 26rpx;
        height: 26rpx;
        margin-right: 10rpx;
        border: solid 1px #0E67B9;
        border-radius: 50%;

        &.checked {
          border-color: #1F8FF7;
          background-color: #1F8FF7;
          background-image: url(../../../../static/fifthg/2023/icon-check.png);
          background-position: center center;
          background-repeat: no-repeat;
          background-size: 20rpx 20rpx;
        }
      }

      .label {
        color: #0E67B9;
      }
    }
  }

  .contracts {
    margin: 10rpx 0;
    border-radius: 10rpx;
    border: none;
    padding: 20rpx;
    background-color: #FFF1E1;

    .text {
      text-align: right;
      color: #F9A63F !important;
    }

    .icon-arw {
      background-image: url(../../../../static/fifthg/2023/icon-arw-yellow.png)!important;
    }
  }

}


.dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;

  .mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .4);
    z-index: 1;
  }

  .sheet {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    z-index: 999;
    height: 450px;

    .head {
      display: flex;
      align-items: center;
      position: relative;
      padding: 20rpx;
      border-bottom: solid 1px #EAEAEA;

      .perfix, .suffix {
        padding: 10rpx;
      }

      .suffix {
        color: #1F8FF7;
      }

      .title {
        flex: 1;
        padding: 10rpx;
        font-size: 32rpx;
        font-weight: bold;
        color: #212121;
        text-align: center;
      }

      .close {
        position: absolute;
        top: 50%;
        right: 20rpx;
        width: 40rpx;
        height: 40rpx;
        background-image: url(../../../../static/fifthg/2023/icon-close.png);
        background-repeat: no-repeat;
        background-size: 40rpx 40rpx;
        transform: translate(0, -50%);
      }
    }

    .body {
      padding: 20rpx;

      .list {
        max-height: 600rpx;
        overflow: auto;

        .item {
          display: flex;
          align-items: center;
          margin: 20rpx 0;
          border: solid 1px transparent;
          border-radius: 10rpx;
          padding: 20rpx;
          background-color: #F3F3F3;
          font-size: 28rpx;
          color: #212121;

          &.active {
            border-color: #1F8FF7;
            background-color: #F8FCFF;
            color: #1F8FF7;
          }

          .addr {
            flex: 1;
          }

          .radiobox {
            width: 40rpx;
            height: 40rpx;
            margin-left: 10rpx;
            border: solid 1px #666;
            border-radius: 50%;

            &.checked {
              border-color: #1F8FF7;
              background-color: #1F8FF7;
              background-image: url(../../../../static/fifthg/2023/icon-check.png);
              background-position: center center;
              background-repeat: no-repeat;
              background-size: 20rpx 20rpx;
            }
          }
        }
      }

      .input {
        border-radius: 40rpx;
        padding: 20rpx 40rpx;
        background-color: #F3F3F3;
        line-height: 2em;
      }
    }

    .foot {
      display: flex;
      align-items: center;
      padding: 20rpx;
      margin: 40rpx 0;

      .btn {
        flex: 2;
        border-radius: 60rpx;
        padding: 20rpx;
        margin: 0 10rpx;
        background-color: #1F8FF7;
        color: #fff;
        font-size: 32rpx;
        text-align: center;

        &.sub {
          flex: 1;
          background-color: #DEEBFF;
          color: #1F8FF7;
        }
      }


    }
  }
}

.dialog .sheet .body .list .item {
  padding: 0;
  box-sizing: border-box;

  .perfix {
    width: 130rpx;
    margin-top: 20rpx;
    padding: 0 10rpx;
    text-align: center;

    .discount {
      font-size: 60rpx;
      line-height: 40rpx;
      color: #357AF1;

      .symbol {
        font-size: 28rpx;
        color: #357AF1;
      }
    }

    .rule {
      font-size: 24rpx;
      color: #666;
    }
  }

  .ctn {
    flex: 1;
    padding: 20rpx;

    .title {
      font-size: 28rpx;
      color: #212121;
    }

    .memo {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      font-size: 24rpx;
      line-height: 30rpx;
      color: #666;
      overflow: hidden;
    }
  }

  .radiobox {
    margin-right: 10rpx;
    border-color: #666;
    background-color: #fff;

    &.checked {
      border-color: #357AF1;
      background-color: #357AF1;
    }
  }
}

.uni-searchbar {
  padding: 0;
  overflow: hidden;
}

.ellipsis-text {
  text-overflow: ellipsis;
  -webkit-line-clamp: 1 !important;
}
</style>
