<template>
  <div class="body">
    <div class="body-title">硬件、杂项自选权益池</div>
    <div class="group">
      <div class="title">{{ poolSelList.poolname }}</div>
      <p class="text1">可选商品价值：{{poolSelList.price}}元</p>
      <p class="text2">已选商品价值：{{totalSelectedPrice}}元</p>
      <div class="info">
        <div class="warning-icon"></div>  
        <div>{{ poolSelList.pooltype == '1' ? '选择的商品价值总额必须等于可选的商品价值' : '选择的商品价值大于等于可选的商品价值' }}</div>
      </div>
    </div>
    <div v-for="(key,index) in Object.keys(filterSelList)" :key="index">
      <div class="body-title">第【{{key}}】组</div>
      <div class="group">
        <div class="title">{{ filterSelList[key].groupalias }}</div>
        <div class="check-list">
          <van-checkbox-group v-model="selectProductId">
            <van-cell-group>
              <div v-for="(item,index) in filterSelList[key].items" clickable :key="index" class="group-body">
                <van-cell @click="toggle(index,item)">
                  <template #title>
                    <div class="left-content">
                      <div class="salesname">{{ item.salesname }} {{ item.pricevalue }}元</div>
                    </div>
                  </template>
                  <template #right-icon>
                    <van-checkbox :name="item.id" shape="square" />
                  </template>
                </van-cell>
                <div class="psiprodflag" v-if="item.psiprodflag == 'Y'">
                  <div class="item-info">
                    <span class="prefix">出库方式</span>
                    <div class="item-body">
                      <selector :options="scopeList" v-model="scopeType[item.id]" :value-map="scopevalMap"></selector>
                    </div>
                  </div>
                  <div class="item-info">
                    <span class="prefix">商品码</span>
                    <div class="item-body">
                      <input type="text" class="input" v-model="produceCodes[item.id]" placeholder="请输入商品码">
                      <div class="scan-class" @click="callScan(item.id)"></div>
                    </div>
                  </div>
                </div>
              </div>
            </van-cell-group>
          </van-checkbox-group>
        </div>
      </div>
    </div>
    <div class="btn" @click="submitFn">提交</div>
  </div>
</template>

<script>

import Vue from 'vue'
import {
  mapGetters,
  mapActions,
  mapMutations,
  mapState,
} from 'vuex';

var initialGrid = null
export default {
  components:{ },
  data () {
    return {
      selList:[],    // 权益数据
      filterSelList:[],   // 分组后的权益数据
      produceCodes:{},    // 存储商品码
      selectProduct:[],
      selectProductId:[],
      totalSelectedPrice:'',   // 已选商品总价值
      scopeList: [],
      scopeType: {},
      scopevalMap: ['data', 'mname'],
      showPopup:false,    // 提示
      popupTitle:'提示',
      popupContent:'默认内容',
    }
  },
  // beforeMount(){
  //   this.selectProduct = localStorage.getItem('poolSels') ? JSON.parse(localStorage.getItem('poolSels')) : []
  //   if(this.selectProduct.length>0){
  //     this.selectProductId = this.selectProduct.map((item)=>item.id)
  //   }
  // },
  mounted () {
    console.log(this.poolSelList)
    this.selList = this.poolSelList.selList
    if(this.selList){
      this.filterSelListFn()
      this.getscopeListFn()
    }
    console.log(this.selectProduct, 'this.selectProduct')
    if(this.selectProduct.length > 0){
      this.selectProductId = this.selectProduct.map((item)=>item.id)
    }
  },
  watch:{
    selectProduct:{
      handler:function(newVal){
        console.log('selectProduct222',newVal);
        this.totalSelectedPrice = 0
        for(const item of newVal){
          const pricevalue = parseFloat(item.pricevalue)
          console.log('pricevalue',pricevalue);
          if (!isNaN(pricevalue)) {
            this.totalSelectedPrice += pricevalue;
          }
          this.totalSelectedPrice = parseFloat(this.totalSelectedPrice.toFixed(1));
        }
      },
      deep:true
    }
  },
  computed: {
    ...mapGetters({
      poolSelList: "getPoolSelList",
    }),
  },
  methods: {
    ...mapMutations({
      setProduceCodes: "setProduceCodes",
      setSelectProduct: "setSelectProduct",
    }),
    async getscopeListFn(){
      const {responseBody: result} = await this.$http.post(this.$apis.getdata, {
        gcode:'STOCK_OUT_METHOD'
      })
      console.log('STOCK_OUT_METHOD',result);
      this.scopeList = result.paramdata
    },
    filterSelListFn(){
      this.filterSelList = this.selList.reduce((groups,item)=>{
        const groupkey = item.groupflag;
        if(!groups[groupkey]){
          console.log(`Initializing group for key ${groupkey}`);
          groups[groupkey] = {groupalias:item.groupalias,items:[]}
        }
        console.log(`Pushing item to group for key ${groupkey}`);
        groups[groupkey].items.push(item)
        return groups
      },{})
      const keyOrder = ['A','B','C','D','E','F','G','H','I']
      const orderedFilterSelList = []
      keyOrder.forEach(key=>{
        if(this.filterSelList[key]){
          orderedFilterSelList[key] = this.filterSelList[key]
        }
      })
      this.filterSelList = orderedFilterSelList
      console.log('filterSelList',this.filterSelList);
    },
    callScan(id){
      if (window.android) {
        //注册监听事件
        window['onScanResult'] = (scanTxt) => {
          console.log('onScanResult', scanTxt)
          this.productCode[id] = scanTxt || ""
        }
        window.android.qrScan()
      } else {
        this.$util.feeback.showToast({
          title: "该应用还没支持扫描功能"
        })
      }
    },
    toggle(index,item){
      console.log('toggle',index);
      console.log('groupflag',item);
      // 检查该数据是否已经存在于selectProduct中
      const selectedIndex = this.selectProductId.indexOf(item.id);
      if (selectedIndex !== -1) {
        // Item is already selected, remove it
        this.selectProductId.splice(selectedIndex, 1);
      } else {
        // Item is not selected, add it
        this.selectProductId.push(item.id);
      }
      this.selectProduct = this.selList.filter((item)=>this.selectProductId.includes(item.id))
    },
    submitFn(){
      console.log('selectProduct',this.selectProduct);
      console.log('scopeType',this.scopeType);
      console.log('produceCodes',this.produceCodes);
      if(this.poolSelList.pooltype == '1' && this.totalSelectedPrice !== parseFloat(this.poolSelList.price)){
        console.log('22222');
        uni.showModal({
          title: '温馨提示',
          content: '自选权益池可选的商品价值与已选的商品价值金额不一致。',
          showCancel: false, //没有取消按钮的弹框
          buttonText: '确定',
          success: function (res) {
            if (res.confirm) {
            }
          }
        })
        return
      }
      if(this.poolSelList.pooltype !== '1' && this.totalSelectedPrice < parseFloat(this.poolSelList.price)){
        console.log('333333');
        uni.showModal({
          title: '温馨提示',
          content: '自选权益池可选的商品价值与已选的商品价值金额不一致。',
          showCancel: false, //没有取消按钮的弹框
          buttonText: '确定',
          success: function (res) {
            if (res.confirm) {
            }
          }
        })
        return
      }
      console.log('1234');
      for(const item of this.selectProduct){
        item.artcode = this.produceCodes[item.id]
        item.retrieval = this.scopeType[item.id]
        if(item.retrieval == 'LOCAL_SALE' && !item.artcode){
          uni.showModal({
            title: '温馨提示',
            content: '出库方式选择店销模式时必须输入商品码。',
            showCancel: false, //没有取消按钮的弹框
            buttonText: '确定',
            success: function (res) {
              if (res.confirm) {
              }
            }
          })
          return
        }
      }
      console.log('selectProduct',this.selectProduct);
      this.setSelectProduct(this.selectProduct)
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
 
.body{
  padding: 0 .26 * 100rpx 0 .34 * 100rpx;
  box-sizing: border-box;
}
.body-title{
  font-size: .28 * 100rpx;
  line-height: .8 * 100rpx;
  color: rgba(16, 16, 16, 1);
  text-align: left;
}
.group{
  border-radius: .1 * 100rpx;
  background-color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(255, 255, 255, 1);
  padding: .1 * 100rpx .2 * 100rpx .2 * 100rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .title{
    font-size: .28 * 100rpx;
    font-weight: bold;
    text-align: left;
  }
  .text1{
    font-size: .28 * 100rpx;
    margin-top: .2 * 100rpx;
    color: rgba(16, 16, 16, 1);
  }
  .text2{
    font-size: .28 * 100rpx;
    margin-top: .2 * 100rpx;
    color: rgba(189, 49, 36, 1);
  }
  .info{
    display: flex;
    align-items: center;
    margin-top: .06 * 100rpx;
    font-size: .24 * 100rpx;
    color: rgba(108, 108, 108, 1);
    .warning-icon{
      width: .28 * 100rpx;
      height: .28 * 100rpx;
      background: url('../../../../static/fifthg/2023/warning-icon.png');
      background-position: center;
      background-repeat: no-repeat;
      background-size: .28 * 100rpx .28 * 100rpx;
      margin-right: .08 * 100rpx;
    }
  }
  .check-list{
    width: 100%;
  }
  // .group-body{
  //   margin-bottom: .2 * 100rpx;
  // }
  .left-content{
    margin-right: .2 * 100rpx;
    word-wrap: break-word;
    overflow-wrap: break-word;

    .salesname{
      margin-right: .2 * 100rpx;
      text-align: left;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }
  }
  .psiprodflag{
    .item-info{
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: .2 * 100rpx;
      .item-body{
        display: flex;
        align-items: center;
        .weui-cell {
          padding: 0!important;

          .weui-select {
            height: auto;
            line-height: .3 * 100rpx;
          }
        }
        .input{
          width: 100%;
          padding: .2 * 100rpx;
          border: none;
          box-sizing: border-box;
          text-align: right;
          color: rgba(154, 154, 154, 1);
        }
        .scan-class{
          width: .5 * 100rpx;
          height: .5 * 100rpx;
          margin-left: .2 * 100rpx;
          background: url('../../../../static/fifthg/2023/icon_scan.png') no-repeat center center;
          background-size: contain;
        }
      }
    }
  }

  
  .psiprodflag{
    .item-info{
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: .2 * 100rpx 0;
      font-size: 28rpx;
      .item-body{
        display: flex;
        align-items: center;
        .weui-cell {
          padding: 0!important;

          .weui-select {
            height: auto;
            line-height: .3 * 100rpx;
          }
        }
        .input{
          width: 100%;
          padding: .2 * 100rpx;
          border: none;
          box-sizing: border-box;
          text-align: right;
          color: rgba(154, 154, 154, 1);
        }
        .scan-class{
          width: .5 * 100rpx;
          height: .5 * 100rpx;
          margin-left: .2 * 100rpx;
          background: url('../../../../static/fifthg/2023/icon_scan.png') no-repeat center center;
          background-size: contain;
        }
      }
    }
  }
}
/deep/ .van-cell{
  padding: .2 * 100rpx 0;
}
.btn{
  width: 100%;
  height: .82 * 100rpx;
  line-height: .82 * 100rpx;
  border-radius: .6 * 100rpx;
  background-color: rgba(64, 149, 229, 1);
  color: rgba(255, 255, 255, 1);
  text-align: center;
  margin-top: 1.3 * 100rpx;
}

</style>
