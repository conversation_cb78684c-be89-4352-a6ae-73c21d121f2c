<template>
	<view class="app-agreement">
		<!-- <action-bar ref="bar" class="app-agreement__bar" title="电子签名" leftIcon="back"></action-bar> -->
		<web-view v-if="url" :src="url"></web-view>
	</view>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';

export default {
	name: 'commonSign',
	data() {
		return {
			options: {},
			serialno: '',
			poid: '',
			cpcode: '',
			transId: undefined,
			url: undefined
		};
	},
	computed: {
		...mapGetters({
			custInfo: 'getCustInfo',
      stepInfo: 'getStepInfo',
		})
	},
	onLoad(options) {
		this.options = options;
    this.initSaveProcess()
		this.serialno = options.serialno
		if (this.serialno) {
			this.poid = options.poid
			this.cpcode = options.cpcode
		} else {
			const funsProcessInfo = JSON.parse(uni.getStorageSync('funsProcessInfo'))
			this.poid = funsProcessInfo.poid
			this.cpcode = funsProcessInfo.currentStep
			funsProcessInfo.process.forEach(item => {
				if (item.paySerialno) {
					this.serialno = item.paySerialno
				}
			})

		}
		this.InitData();
	},
	methods: {
		...mapActions({
			saveProcess: 'saveProcess',
		}),
		InitData() {
			window.addEventListener('message', this.onWinEvent, false);
			this.getPdf();
		},
		async onWinEvent(e) {
			console.log('进来了嘛')
			console.log(e)
			console.log(e.data)
			if (e.data === 'success') {
			// if (e.isTrusted === true) {
				// 这个要保存环节，然后再判断是否有下一步

				await this.saveProcess({
					data: {
						poid: this.poid,
						cpcode: this.cpcode,
						temp: '1'
					},
					refreshCustInfo: true,
					custid: this.custInfo.custid,
					unNeedNext: true,
					afterProcess: () => {
						uni.showModal({
							title: '温馨提示',
							content: '签名成功',
							showCancel: false,
							cancelColor: '#576B95',
							success: (res) => {
								this.$rauter.navigateTo({
									url: 'FusionGDM',
									data: { poid: this.poid }
								});
							}
						});
					}
				})
			}
		},
		getPdf() {
			if (!this.serialno) {
				this.$util.feeback.showToast({ title: '签名环节序列号为空。' });
				return;
			}

			this.$http
				.post(this.$apis.m5gSyncBusiInfo, {
					keywords: {
						archivesType: '5G',
						archivesTypeCode: '1'
					},
					custid: this.custInfo.custid,
					serialno: this.serialno
				})
				.then(response => {
					let res = response.responseBody;
					if (res && res.data) {
						this.transId = res.data.transId;
						this.url = res.data.h5PageUrl;
					}
				});
		},
		closeClick() {
			this.$rauter.redirectTo({
				url: 'BusinessList'
			});
		},
    initSaveProcess() {
      console.log('onLoad========>saveProcess');
      let stepInfo = JSON.parse(JSON.stringify(this.stepInfo)),
        optionsCode = this.options?.code;
      let data = {
        poid: stepInfo.poid || this.$route.query.poid,
        cpcode: optionsCode ? optionsCode : '',
        temp: 0
      };
      let keepData = {
        data,
        unNeedNext: true
      };
      this.saveProcess(keepData);
    }
	}
};
</script>

<style lang="scss" scoped>
.app-agreement {
	&__bar {
		display: none;
		position: absolute;
		top: 0;
		z-index: 99;
		width: 100%;
	}

	&__bottom {
		position: absolute;
		bottom: 0;
		z-index: 99;
		width: 100%;
	}
}
</style>
