<template>
    <div>
        <div class="main">
            <div class="item">
                <span class="left">地址</span>
                <span class="right-sp">{{addrInfo.whladdr}}</span>
            </div>
            <div class="item">
                <span class="left">片区</span>
                <span class="right-sp-grid">{{addrInfo.gridname}}</span>
            </div>
            <div class="item" v-if="canNewAddr">
                <span class="copy-sp is-copy" v-if="isCopy == true" @click="copyAddr(false)">复制新建地址</span>
                <span class="copy-sp" v-else @click="copyAddr(true)">复制新建地址</span>
            </div>

            <div v-if="isCopy == true">
                <div class="item">
                    <span class="left">首地址</span>
                    <span class="right-sp">{{addrInfo.addr}}</span>
                </div>
                <div class="item">
                    <span class="left">尾地址</span>
                    <input type="text" class="addr-input" v-model="newEndAddr">
                </div>
            </div>

            <button class="btn-query" v-if="isCopy == true" @click="editandSaveAddr">修改并选择</button>
            <button class="btn-query" v-else @click="selectAddr">选择</button>
        </div>
    </div>
</template>
<script>

import {
  mapGetters,
  mapActions,
  mapMutations
} from 'vuex';
export default {
  data () {
    return {
      citylist: [],
      operinfo: null,
      cityCode: null,
      addrInfo: {},
      isCopy: false,
      newEndAddr: '',
      canNewAddr: true
    }
  },
  computed: {
    ...mapGetters({
      custInfo: "getCustInfo",
    }),
  },
  mounted () {
    this.addrInfo = {}
    this.addrInfo.houseid = this.$route.query.houseid
    this.addrInfo.gridname = this.$route.query.gridname
    this.addrInfo.whladdr = this.$route.query.whladdr
    this.addrInfo.addr = this.$route.query.addr
    this.addrInfo.endaddr = this.$route.query.endaddr
    this.newEndAddr = this.$route.query.endaddr
    this.addrInfo.patchid = this.$route.query.patchid
    this.canNewAddr = this.$route.query.canNewAddr
  },
  methods: {
    async editandSaveAddr () {
      await this.$http.post(this.$apis.addAddress, {
        customerId: this.custInfo.custid,
        houserId: this.addrInfo.houseid,
        level11: this.newEndAddr
      })
        
      this.addrInfo.endaddr = this.newEndAddr
      localStorage.setItem('selectAddrInfo', JSON.stringify(this.addrInfo))
      this.$router.go(-2)
    },
    async selectAddr () {
      await this.$http.post(this.$apis.addAddress, {
        customerId: this.custInfo.custid,
        houserId: this.addrInfo.houseid,
        level11: ''
      })
      this.addrInfo.endaddr = null
      localStorage.setItem('selectAddrInfo', JSON.stringify(this.addrInfo))
      this.$router.go(-2)
    },
    copyAddr (flag) {
      this.isCopy = flag
      console.log(this.isCopy)
    },
    selectChange () {
      console.log(this.cityCode)
    }
  }
}
</script>

<style lang="scss" scoped>
    *{
        
        font-size: 0.28 * 100rpx;
    }
    .item{
        display: flex;
        background-color: #fff;
        padding-left: 0.3 * 100rpx;
        box-sizing: border-box;
        padding-right: 0.3 * 100rpx;
    }
    .left{
        height: 0.8 * 100rpx;
        line-height: 0.8 * 100rpx;
        font-size: 0.28 * 100rpx;
        
        font-weight: 400;
        color: #000000;
        width: 1.5 * 100rpx;
    }
    .right-selector{
        
        font-weight: 400;
        color: #999999;
        margin-left: 2 * 100rpx;
    }
    /deep/.weui-cell:before {
        content: " ";
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        height: 1px;
        border-top: 0px solid #D9D9D9 !important;
        color: #D9D9D9;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        left: 15px;
    }
    .can-btn{
        background-color: #3875D0;
    }
    .errorinfo{
        width: 6.6 * 100rpx;
        height: 0.3 * 100rpx;
        font-size: .28 * 100rpx;
        
        font-weight: 400;
        color: #F23C3C;
        line-height: 36px;
    }
    .addr-input{
        border: none;
        outline: none;
        height: 0.6 * 100rpx;
        width: 4.8 * 100rpx;
        background-color: #eee;
        margin-left: 0.3 * 100rpx;
        border-radius: 0.08 * 100rpx;
        padding-left: 0.2 * 100rpx;
    }
    .btn-query{
        outline: none;
        margin: 40rpx 30rpx;
        padding: 20rpx;
        border: none;
        border-radius: 0.08 * 100rpx;
        color: #fff;
        background-color: #3875D0;
    }
    .right-sp{
        font-size: 0.28 * 100rpx;
        font-weight: 400;
        line-height: 80rpx;
        color: #000000;
        text-align: left;
    }
    .right-sp-grid{
        display: inline-block;
        height: 0.8 * 100rpx;
        font-size: 0.28 * 100rpx;
        
        font-weight: 400;
        color: #000000;
        width: 5.5 * 100rpx;
        text-align: left;
        line-height: 0.8 * 100rpx;
    }
    .copy-sp{
        display: inline-block;
        margin-bottom: 20rpx;
        border: 1px solid #eee;
        border-radius: 0.08 * 100rpx;
        padding-left: 0.2 * 100rpx;
        padding-right: 0.2 * 100rpx;
        line-height: 0.8 * 100rpx;
    }
    .is-copy{
        background-color: #3875D0;
        border: 1px solid #3875D0;
        color: #fff;
    }
</style>
