<template>
	<view>
		<!-- <action-bar title="定位客户" leftIcon="back"></action-bar> -->
		<view class="group-class crm-border display-flex-column">
			<text class="font-32 text-main font-w600 title-class">广电用户</text>
			<CustMsgHeader :afterLocation="afterLocation"></CustMsgHeader>
			<text v-if="custInfo" class="font-32 banli-btn" style="margin-right: 20rpx;margin-bottom: 20rpx;" @click="banli">立即办理</text>
		</view>
		<view class="new-cust-group group-class crm-border">
			<text class="font-32 text-main font-w600 title-class">新用户</text>
			<image src="../../../../static/fifthg/imgs/newCust.png" class="new-cust-icon" />
			<!-- <text class="font-32 banli-btn" @click="banli">立即办理</text> -->
			<text class="font-32 banli-btn" @click="addAndHandle">新增客户并立即办理</text>
		</view>

		<view class="records-btn" @click="goStepRecords">查看办理记录</view>
	</view>
</template>

<script>
import CustMsgHeader from '../../components/CustMsgHeader.vue';

import { mapGetters, mapActions } from 'vuex';
export default {
	data() {
		return {
			afterLocation: {
				type: 'back'
			}, //跳到定位界面后的操作
			pageData: null //原始的界面参数
		};
	},
	created() {},

	onLoad(data) {
		if (data.afterLocation) this.pageData = JSON.parse(data.afterLocation);
	},

	components: {
		CustMsgHeader
	},

	computed: {
		...mapGetters({
			custInfo: 'getCustInfo'
		})
	},

	methods: {
		...mapActions({
			queProcess: 'queProcess',
		}),
		async banli() {
			const {responseBody: data} = await this.queProcess({
				"pcode":"NGDM","custid": this.custInfo.custid,"custname": this.custInfo.custname
			});
			console.log('data', data)
			this.$rauter.navigateTo({
				url: 'FusionGDM',
				data: {
					poid: data.poid
				}
			});

		},
		goStepRecords() {
			this.$rauter.navigateTo({
				url: 'BusinessList'
			});
		},
		addAndHandle() {
			this.$rauter.navigateTo({
				url: 'FNAddCustBusiness',
        data: {
          back: 'LocationCustGDM'
        }
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.group-class {
	background-color: #ffffff;
	border-radius: 20rpx;
	margin: 40rpx 20rpx 0 20rpx;
	overflow: hidden;
}

.title-class {
	margin-top: 20rpx;
	margin-left: 20rpx;
	display: block;
}

.new-cust-group {
	height: 400rpx;
	position: relative;

	.new-cust-icon {
		width: 160rpx;
		height: 160rpx;
		color: #0084ff;
		position: absolute;
		top: calc(200rpx - 80rpx);
		left: calc(100vw / 2 - 80rpx - 20rpx);
	}

	.banli-btn {
		position: absolute;
		bottom: 20rpx;
		right: 20rpx;
	}
}

.banli-btn {
	color: #0084ff;
	border: 1px solid #0084ff;
	padding: 8rpx 10rpx;
	border-radius: 20rpx;
	align-self: flex-end;
}

.records-btn {
	background-color: #1f77ff;
	color: #ffffff;
	padding: 20rpx 80rpx;
	font-size: 36rpx;
	text-align: center;
	margin: 40rpx 120rpx;
	border-radius: 60rpx;
}
</style>
