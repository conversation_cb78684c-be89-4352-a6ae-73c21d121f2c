<template>
	<view class="vOpenAccount">
		<!-- <action-bar title="补充信息" leftIcon="back"></action-bar> -->
		<action-scroll-view>
			<uni-section title="基础信息" type="line"></uni-section>
			<!-- 基础信息 -->
			<BaseMsg></BaseMsg>
			<!-- 产品/套餐配置 -->
			<OAProduct class="marginT" @submitData="productData" />
			<!-- 发展人信息 -->
			<DeveloperView class="marginT" @result="developerData"></DeveloperView>

			<view class="vOpenAccount-btn">
				<view class="btn" @click="submit()">提交</view>
			</view>
		</action-scroll-view>
	</view>
</template>

<script>
import UniSection from '@/uni_modules/uni-section/uni-section.vue';
import BaseMsg from '@/pages/FifthG/components/gdm/OABaseMsg.vue';
import OAProduct from '@/pages/FifthG/components/gdm/OAProduct.vue';
import DeveloperView from '@/components/developer/DeveloperView.vue';
import { mapGetters, mapActions, mapMutations } from 'vuex';
import { psubClassTag } from '@/store/modules/sysparam.js';
import { PSUBCLASS, PERMARK } from '@/store/modules/openAccount.js';
import PickMsgItem from '@/pages/FifthG/components/PickMsgItem.vue';
export default {
	name: 'business',
	data() {
		return {
			pageCode: '',
			btnDis: false,
			seconds: 10,
			isSend: false, // 是否已发送
			product: null,
			developerParam: null,
		};
	},

	created() { },

	components: {
		UniSection,
		DeveloperView,
		OAProduct,
		BaseMsg,
	},

	computed: {
		...mapGetters({
			percomb: 'getPercomb',
			submitData: 'getSubmitData',
			custInfo: 'getCustInfo',
			currentAddress: 'getCurrentAddress',
			stepInfo: 'getStepInfo',
			salesInfo: 'getSalesInfo'
		}),
		//检查是否需要提交
		needCommit() {
			console.log('needCommit')
			let process = this.stepInfo.process
			let pageBean = process.filter(proc => {
				return proc.code == this.pageCode
			})[0]
			console.log('pageBean---------------------')
			console.log(pageBean)
			if (pageBean && pageBean.pstatus == '2') {
				return false
			} else {
				return true;
			}

		}
	},

	watch: {},

	async onLoad(data) {
		this.pageCode = data.code;
		await this.initData([psubClassTag]);
		await this.queryData({
			permark: 'B',
			pcode: this.salesInfo.pcode //商品的pcode
		});
	},

	onShow() {
		// 10s倒计时
		this.btnDis = false
		this.timer = setInterval(() => {
			this.countdown();
		}, 1000)
	},

	onUnload() {
		this.leave();
	},

	methods: {
		...mapActions({
			queryData: 'queryData',
			initData: 'init',
			saveProcess: 'saveProcess',
			goNextPage: 'goNextPage',
			queProcess: 'queProcess'
		}),

		...mapMutations({
			collect: 'submit', //开始收集数据的方法
			setStepDefault: 'setStepDefault',
			leave: 'leaveOpenAccount'
		}),

		productData(e) {
			console.log(e)
			this.product = e
		},

		developerData(e) {
			this.developerParam = e
		},

		submit() {
			this._submit();
		},

		//最终提交接口
		_submit() {
			let params = {
				installparams: []
			};
			params.poid = this.stepInfo.poid;
			params.custid = this.custInfo.custid; //填充客户id
			params.custname = this.custInfo.custname; //填充客户名字
			params.iscrtorder = 'Y';
			params.opcode = 'BIZ_USER_NEW'; //填充当前业务的opcode
			params.optime = this.$util.dataProcess.formatDate(new Date()); //填充当前办理的时间
			params.describe = '';
			params.memo = this.submitData.memo; //备注

			params.systemid = 'GRID';
			// params.systemid = "BOSS"
			params.houseid = this.currentAddress.houseid || ''; //当前地址的houseid
			params.patchid = this.currentAddress.patchid || ''; //当前地址的patchid
			params.whladdr = this.currentAddress.addr || ''; //当前地址的addr
			params.knowids = this.stepInfo.salesid + '';
			params.counts = '0';
			params.units = this.salesInfo.unitname;
			params.ispostpones = this.salesInfo.ispostpone;
			// params.stimes=stimes
			params.mindates = 'null'; //5g业务下，先传null

			let fixorder = this.stepInfo.data.GDM_GWDX;
			if (fixorder) {
				const _servid = fixorder.servid || '';
				const _pid = fixorder.pid || '';
				// const _relswtype = fixorder.relswtype || '';
				// const _relswcode = fixorder.relswcode || '';
				// const _offercode = fixorder.offercode || '';
				const digituser = _servid + '~' + _pid;
				params.digituser = digituser
				console.log(digituser)
			}

			let yccf = this.product;
			if (this.stepInfo.objid) {
				params.relSales = [
					{
						knowid: this.salesInfo.knowid,
						relsalesid: this.stepInfo.objid
					}
				];
			}
			// 充返
			if (yccf && yccf.offercode) {
				params.contractList = [{
					contract_code: yccf.offercode,
				}]
			}

			//添加发展人参数
			if (this.developerParam) {
				params.developer = this.developerParam;
			}
			//添加安装参数
			let installParam = this.addInstallParam();
			params.installparams.push(installParam);

			//添加使用参数
			let userParam = [];


			let permarkParams = {};
			permarkParams.permark = this.stepInfo.data.GDM_XZTC.permark || 'B';
			permarkParams.servkind = '0'; //固定传0
			permarkParams.feekind = '01'; //收费类型
			permarkParams.installtype = '0' //安装类型
			permarkParams.payway = '0' //缴费方式
			userParam.push(permarkParams);
			params.userparams = userParam;

			const defFee = this.salesInfo.price
			const minFees = this.product.minFees
			const prestoremoney = this.product.prestoremoney

			if (isNaN(Number(minFees))) {
				this.$util.feeback.showToast({
					title: `最低预存金额不合法`
				})
				return
			}

			if (defFee * 1 > minFees * 1 || prestoremoney * 1 > minFees * 1) {
				this.$util.feeback.showToast({
					title: `最低预存金额不可低于${Math.max(Number(defFee) ? defFee : 0, Number(prestoremoney) ? prestoremoney : 0)}`
				})
				return
			}

			this.saveProcess({
				data: {
					poid: this.stepInfo.poid,
					cpcode: 'GDM_YCCF',
					data: JSON.stringify({
						params,
						contract: yccf.offername,
						minFees: this.product.minFees,
						custname: params.custname,
						custid: params.custid,
						whladdr: params.whladdr,
						devname: params.developer ? params.developer.devname : '',
					}),
					"temp": "1"
				},
				unNeedNext: true,
				afterProcess: () => {
					this.$rauter.navigateTo({
						url: "FusionGDM",
						data: {
							poid: this.stepInfo.poid
						}
					});
				}
			});
		},

		addInstallParam() {
			let installParam = {};
			installParam.houseid = this.currentAddress.houseid || ''; //当前地址的houseid
			installParam.addr = this.currentAddress.patchid || ''; //当前地址的patchid
			installParam.patchid = this.currentAddress.addr || ''; //当前地址的addr
			installParam.name = this.custInfo.custname; //填充客户名字
			installParam.cardtype = this.custInfo.cardtype;
			installParam.cardno = this.custInfo.cardno;
			installParam.linkman = this.custInfo.linkman;
			installParam.linkphone = this.custInfo.mobile;
			installParam.mobile = this.custInfo.mobile;

			installParam.permark = this.percomb.permarks || 'B';
			installParam.omode = '0'; //开户类型
			installParam.feekind = '01'; //收费类型
			// 判断是否安装类型是否”自安装“=0
			installParam.predate = null; //预约安装时间，格式"yyyy-MM-dd HH:mm:ss"


			//5g业务
			let fg = this.stepInfo.data.GDM_M5G;
			if (this.notEmptyObj(fg)) {
				let fgParams = {
					fgbmobileno: fg.resNum,
					fgbiccid: fg.sim,
					prestorefee: this.product.minFees,
					invoicemode: '1',
					fgbservtype: '0',
					fgbpservid: ''
				};
				installParam = Object.assign(installParam, fgParams);
			}

			installParam.percomb = '80';
			//备注参考app，传空
			installParam.memo = '';

			//支付方式
			installParam.payway = '0';

			return installParam;
		},

		addAddrParam() {
			let addrs = null;
			if (this.currentAddress.isEdited) {
				//当前地址编辑过就传数据
				addrs = {
					houseid: this.currentAddress.houseid,
					addr7: this.currentAddress.addr7,
					addr8: this.currentAddress.addr8,
					addr9: this.currentAddress.addr9,
					addr10: this.currentAddress.addr10,
					addr11: this.currentAddress.addr11
				};
			}
			return addrs;
		},

		notEmptyObj(obj) {
			return obj && JSON.stringify(obj) != '{}';
		},

		goNext() {
			const that = this
			uni.showModal({
				title: '温馨提示',
				content: '当前业务已提交，当前操作不会再进行业务提交，会直接进行到下一个环节！',
				showCancel: true, //没有取消按钮的弹框
				buttonText: '确定',
				success(res) {
					if (res.confirm) {
						that.goNextPage()
					}
				}
			});
		},
		countdown: function () {
			if (this.seconds > 0) {
				this.seconds--;
			} else {
				this.btnDis = true
				this.seconds = 10
				clearInterval(this.timer)
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.vOpenAccount {
	box-sizing: border-box;

	view {
		box-sizing: border-box;
	}

	&-btn {
		width: 100%;
		overflow: hidden;
		padding: 20rpx 40rpx;

		.btn {
			width: 100%;
			overflow: hidden;
			color: #fff;
			font-size: 32rpx;
			background-color: #1f77ff;
			line-height: 100rpx;
			height: 100rpx;
			border-radius: 50rpx;
			text-align: center;
		}
	}
}

.marginT {
	margin-top: 20rpx;
}</style>
