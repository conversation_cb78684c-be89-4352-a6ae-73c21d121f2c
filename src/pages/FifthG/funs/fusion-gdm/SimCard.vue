<!-- 绑卡界面 -->
<template>

  <view>
    <!-- <action-bar title="号卡绑定" leftIcon="back"></action-bar> -->
    <ActionScrollView>
      <view class="msg-group">
        <view class="display-flex-row border-bottom msg-item" v-for="(msgItem,index) in cardMsgs" :key="index">
          <text class="title">{{ msgItem.title }}</text>
          <text class="content">{{ msgItem.content }}</text>
        </view>

        <view class="display-flex-row border-bottom msg-item">
          <text class="title">SIM卡号</text>
          <input class="content" v-model="params.sim" placeholder="请输入SIM卡号" />
					<i class="iconfont icon-saoma" style="font-size: 1.2em;" @click="onGotoBarcode"></i>
        </view>

        <view class="bind-btn">
          <view class="btn" @click="readSIM">
            读取SIM卡号
          </view>
          <view class="btn" @click="readSIMData">
            识别SIM卡号
          </view>
          <view class="btn" @click="onBangClick">
            绑定
          </view>
        </view>
      </view>
    </ActionScrollView>
  </view>

</template>

<script>
import launchMiniprogram from "@/sdk/eWechat/launchMiniprogram";
import {
  mapGetters,
  mapActions
} from 'vuex'

export default {
  name: "bindSimCard",
  mixins: [launchMiniprogram],
  data() {
    return {
      pageIndex: -1,
			minFees: 0,
			salesInfoPrice: 0,
      params: {
        sim: '',
      },
      cardMsgs: [{
        title: "套餐名称",
        content: ""
      },
        // {
        // 	title: "手机号归属",
        // 	content: "18元套餐卡"
        // },
        {
          title: "手机号",
          content: ""
        },
				{
				  title: "靓号金额",
				  content: ""
				},
				{
				  title: "套餐金额",
				  content: ""
				},
        {
          title: "预存金额",
          content: ""
        },
        // {
        // 	title: "最低消费金额",
        // 	content: "18元套餐卡"
        // }
      ],
      options: {},
      readVisible: false,
    };
  },
  created() {
  },
  components: {},

  onLoad(options) {
    this.$nextTick(async () => {
      // 唤起小程序基础配置
      this.options = options

      this.InitData()
      this.refreshOrderId()
      await this.agentConfig()
    })
  },
  onShow() {
    // this.echoInfo()
    const vm = this
    // 监听获取扫码的sim号
    // uni.$on('scanCodeParamsSim', function (params) {
    //   if (params) {
    //     vm.params.sim = params
    //   } else {
    //     this.$util.feeback.showToast({
    //       title: "未识别出条形码或二维码！"
    //     })
    //   }
    // })
  },
  onUnload() {
    this.removeMessage()
  },
  computed: {
    ...mapGetters({
      custInfo: 'getCustInfo',
      stepInfo: 'getStepInfo',
			salesInfo: 'getSalesInfo'
    })
  },
  watch: {
    stepInfo: {
      handler(newVal, oldVal) {
        console.log('stepInfoNewVal')
        console.log(newVal)
        // this.echoInfo()
      },
      immediate: false,
      deep: true,
    }
  },
  methods: {
    ...mapActions({
      saveProcess: 'saveProcess',
      goNextPage: 'goNextPage',
      queProcess: 'queProcess',
    }),
    // onGotoBarcode() {
    //   const vm = this
    //   uni.chooseImage({
    //     count: 1, // 只能选取一张
    //     sizeType: ['original'], // original【原图】、compressed【压缩图】
    //     sourceType: ['camera'],   // album【相册选图】、camera【相机拍照】
    //     success: async function (res) {
    //       if (res.tempFilePaths.length > 0) {
    //         console.log(res)
    //         let fileData = {
    //           loaded: true,
    //           type: res.tempFiles[0].type,
    //           name: res.tempFiles[0].name,
    //           url: res.tempFilePaths[0]
    //         }
    //         uni.setStorageSync('editPicData',fileData)
    //         uni.navigateTo({
    //           url: '/pages/FifthG/funs/open-account/components/SimPicEdit'
    //         })
    //       }
    //     },
    //     fail: function (err) {
    //       uni.showToast({
    //         title: '图片获取失败！',
    //         icon: 'none'
    //       })
    //     }
    //   });
    // },
    onGotoBarcode() {
      const vm = this
      wx.scanQRCode({
        desc: 'scanQRCode desc',
        needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
        scanType: ["barCode"], // 可以指定扫二维码还是条形码（一维码），默认二者都有
        success: function(res) {
          console.log(res)
          vm.params.sim = res.resultStr
        },
        error: function(res) {
          if (res.errMsg.indexOf('function_not_exist') > 0) {

          }
        }
      });
    },
    InitData() {
      console.log('stepInfo')
      console.log(this.custInfo)
      console.log(this.stepInfo)
      // this.getProcess()
			this.getMinFees()
    },
    getProcess() {
      let that = this,
          stepInfo = JSON.parse(JSON.stringify(this.stepInfo))
      if (stepInfo && stepInfo.poid) {
        this.queProcess({
          poid: stepInfo.poid,
        })
      }
    },
    echoInfo() {
      let that = this,
          stepInfo = JSON.parse(JSON.stringify(this.stepInfo)),
          code = this.options?.code
      // if (stepInfo && stepInfo.custid && stepInfo.process && stepInfo.process.length) {
      if (stepInfo && stepInfo.custid) {
        let stepObj = {}
        // if (stepInfo.process && stepInfo.process.length && stepInfo.process[1].data) {
        //   stepObj = stepInfo.process[1].data
        // }
				if (stepInfo.process && stepInfo.process.length) {
				  stepObj = stepInfo.process.filter(p => {
						return p.code === 'GDM_M5G'
					})[0].data
				}
        this.cardMsgs[0].content = stepInfo?.salesInfo?.knowname
        if (stepObj && stepObj.resNum) {
          this.cardMsgs[1].content = stepObj?.resNum
					// 最低预存金额 = 靓号费用 + 套餐费用。
          // this.cardMsgs[2].content = (stepObj?.reserveFeeYuan || stepInfo?.salesInfo?.price || 0) + '元'
          this.cardMsgs[2].content = this.stepInfo.data["GDM_M5G"].reservsFeeYuan || 0
					this.cardMsgs[3].content = this.salesInfoPrice || this.salesInfo.price || 0
					this.cardMsgs[4].content = this.minFees || 0
					this.params.sim = stepObj?.sim
        }
        this.params.sim = stepInfo?.data?.M5GNOBS?.sim
        // if (stepInfo.process[0].data) {
        //   this.cardMsgs[1].content = stepInfo.process[0].data.resNum
        //   this.cardMsgs[2].content = (stepInfo.process[0].data.reserveFeeYuan || 0) + '元'
        // } else {
        //   uni.showToast({
        //     title: '请先选择手机号码!',
        //     icon: 'none'
        //   })
        //   let timers = setTimeout(() => {
        //     clearTimeout(timers)
        //     uni.hideToast()
        //     that.$rauter.navigateTo({
        //     	url: (stepInfo.process[0].page || 'pickNumber') + '?code=' + stepInfo.process[0].code
        //     })
        //   }, 1000)
        // }
      }
    },
		getMinFees() {
			// 如果选择了靓号，这边是会有靓号费用的
			let reservsFeeYuan = this.stepInfo.data["GDM_M5G"].reservsFeeYuan
			let price = this.salesInfo.price;
			this.salesInfoPrice = price
			// this.stepInfo.objid 是在选择套餐的时候选择的促销优惠的id
			if (this.stepInfo.objid) {
				this.$http.post(this.$apis.callBoss, {
				  service: this.$serviceId.QUE_FGB_DEPOSITFEES,
				  params: {
				    salesid: this.salesInfo.objid,
				    salespkgid: this.stepInfo.objid
				  }
				}).then(resp => {
				  if (resp.responseBody.data.status == '0') {
						if (reservsFeeYuan) {
							this.minFees = Number(reservsFeeYuan) + Number(resp.responseBody.data.output.fees)
						} else {
							this.minFees = resp.responseBody.data.output.fees
						}
						this.salesInfoPrice = resp.responseBody.data.output.fees
					}
					this.echoInfo()
				})
			} else {
				if (reservsFeeYuan) {
					this.minFees = Number(reservsFeeYuan) + Number(price)
				} else {
					this.minFees = price
				}
				this.echoInfo()
			}
		},
    onBangClick() {
      let that = this,
          stepInfo = JSON.parse(JSON.stringify(this.stepInfo)),
          params = JSON.parse(JSON.stringify(this.params)),
          optionsCode = this.options?.code
      if (!params.sim) {
        uni.showToast({
          title: '请输入SIM卡号',
          icon: 'none'
        })
      } else {
        uni.showModal({
          title: '提示',
          content: '是否确定保存当前流程？',
          success(res) {
            if (res.confirm) {
              console.log('用户点击确定')
              if (stepInfo && stepInfo.poid) {
                let data = {
                  poid: stepInfo.poid,
                  cpcode: 'GDM_M5G',
                  data: JSON.stringify({...stepInfo.data["GDM_M5G"], ...params}),
                  temp: 1
                }
                let postParams = {
                  url: 'm5gSimOccupy',
                  data: {
                    iccid: params.sim,
                    custid: that.custInfo && that.custInfo.custid ? that.custInfo
                        .custid : '',
                  }
                }
                let keepData = {
                  data,
                  postParams,
                  unNeedNext: true,
                  afterProcess: () => {
                    that.$rauter.navigateTo({
                      url: 'FusionGDM',
                      data: {
                        poid: stepInfo.poid
                      }
                    })
                  }
                }
                that.removeMessage()
                that.saveProcess(keepData)
              } else {
                uni.showToast({
                  title: '暂未此套餐信息，请重新选择套餐',
                  icon: 'none'
                })
                let timers = setTimeout(() => {
                  clearTimeout(timers)
                  uni.hideToast()
                  that.$rauter.navigateTo({
                    url: 'locationCustFifthG'
                  })
                }, 1000)
              }
            } else if (res.cancel) {

            }
          }
        })
      }
    },
    readSIM() {
      this.invokeSIMCardReader().then(() => {
        console.info('SIMCardReader success')
      })
    },
    readSIMData() {
      let param = {
        orderId: this.orderId,
        //时间转换为年月日
        basisTime: this.$util.dataProcess.formatDate(new Date(), 'yyyy/MM/dd hh:mm:ss')
      }
      console.info('readSIMData url: ', this.$apis.queSimInfo)
      console.info('readSIMData params: ', JSON.stringify(param))
      this.$http.post(this.$apis.queSimInfo, param).then(response => {
        let res = response.responseBody
        console.info('readSIMData result:', JSON.stringify(res))
        if (res) {
          const list = res.list || []
          const siminfo = list[0].siminfo || {}
          this.$set(this.params, 'sim', siminfo.iccid)
        }
      })
    },
    scanQRCodeResult() {
      window.addEventListener('message', this.receiveMessage, false)
    },
    receiveMessage(event) {
      if (event && event.data && event.data && event.data.type == this.iframe && event.data.data) {
        let data = event.data.data
        if (data.code == 'scanQRCode' && data.resultStr) {
          // 接收到扫码识别到的SIM码
          this.params.sim = data.resultStr
        }
      }
    },
    removeMessage() {
      if (this.iframe && this.readVisible) {
        window.removeEventListener('message', this.receiveMessage, false)
      }
    },
  }
}
</script>

<style lang='scss' scoped>
.msg-group {
  padding: 0 20rpx;
  background-color: #FFFFFF;

  .msg-item {
    padding: 20rpx 0;
    justify-content: space-between;

    .title {
      color: #5A5A5A;
    }

    .content {
      color: #000000;
      height: inherit;
      text-align: right;
      width: 68%;
    }
  }
}

.bind-btn {
  width: 100%;
  padding: 20rpx 0rpx;
  text-align: center;
  box-sizing: border-box;
  font-size: 0;
  display: flex;
  justify-content: space-evenly;

  .btn {
    width: 30%;
    display: inline-block;
    overflow: hidden;
    color: #fff;
    font-size: 32rpx;
    background-color: #1F77FF;
    line-height: 80rpx;
    height: 80rpx;
    border-radius: 8rpx;
    padding: 8rpx;
    text-align: center;

    &:not(:first-child) {
      margin-left: 20rpx;
    }
  }
}
</style>

