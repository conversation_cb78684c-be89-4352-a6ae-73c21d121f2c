<template>
  <div>
    <div class="title-box">订单信息</div>
    <div v-if="orderInfo">
      <div class="card-f">
        <div class="item">
          <span class="sp-mid">boss订单号:</span>
          <span class="sp-right">{{orderInfo.resporderid}}</span>
        </div>
        <div class="item">
          <span class="sp-mid">订单编号:</span>
          <span class="sp-right">{{orderInfo.ordercode}}</span>
        </div>
        <div class="item">
          <span class="sp-mid">订单类型:</span>
          <span class="sp-right">{{orderInfo.opcodename}}</span>
        </div>
        <div class="item">
          <span class="sp-mid">订单状态:</span>
          <span class="sp-right">{{orderInfo.orderstatusname}}</span>
        </div>
        <div class="item">
          <span class="sp-mid">客户编号:</span>
          <span class="sp-right">{{orderInfo.custid}}</span>
        </div>
        <div class="item">
          <span class="sp-mid">客户姓名:</span>
          <span class="sp-right">{{orderInfo.custname}}</span>
        </div>
        <div class="item">
          <span class="sp-mid">客户地址:</span>
          <span class="sp-right-addr">{{orderInfo.addr}}</span>
        </div>
        <div class="item">
          <span class="sp-mid">订单金额:</span>
          <span class="sp-right">{{orderInfo.fees}}元</span>
        </div>
        <div class="item" v-if="ejectFees && ejectFees > 0">
          <span class="sp-mid">转购金额:</span>
          <span class="sp-right">{{ejectFees}}元</span>
        </div>
        <div class="item">
          <span class="sp-mid">支付状态:</span>
          <span class="sp-right">{{orderInfo.paystatusname}}</span>
        </div>
        <div class="item" v-if="orderInfo.paywayname">
          <span class="sp-mid">支付方式:</span>
          <span class="sp-right">{{orderInfo.paywayname}}</span>
        </div>
        <div class="item" v-if="orderInfo.descrip">
          <span class="sp-mid">业务说明:</span>
          <span class="sp-right">{{orderInfo.descrip}}</span>
        </div>
      </div>

      <div v-if="orderInfo.custorderDet.applyProducts && orderInfo.custorderDet.applyProducts.length > 0">
        <div class="title-box">订购信息</div>
        <div class="card" v-for="(item,index) in orderInfo.custorderDet.applyProducts" :key="index">
          <div class="item">
            <span class="sp-mid">订购:</span>
            <span class="sp-right">{{item.knowname}}</span>
          </div>
          <div class="item">
            <span class="sp-mid">订购周期:</span>
            <span class="sp-right">{{item.count}}{{item.unitname}}</span>
          </div>
        </div>
      </div>

      <div class="opt-order-box" v-if="showOpt">
        <!-- <button class="btn-opt open-elec-btn"
          v-if="orderInfo.resporderid != null && orderInfo.fees > 0
          && orderInfo.paystatus == '2' && orderInfo.printedinv != '2' 
          && orderInfo.printedinv != 'Y' && orderInfo.opcode != 'BIZ_CHARGE_BOTHNET_ORDER'"
          @click="doOpenElec()">开电子发票</button> -->
        <!-- <button class="btn-opt open-elec-btn"
          v-if=" orderInfo.paystatus == '2' && orderInfo.giftCardFlag == 'Y' "
          @click="doOpenElecSign()">电子签名</button> -->
        <!-- <button class="btn-opt open-elec-btn"
          v-if=" orderInfo.opcode == 'BIZ_USER_NEW' && orderInfo.paystatus != '0'"
          @click="doSign()">签署受理单</button> -->
        <!--<button class="btn-opt rollbacn-btn" @click="doRollback()" v-if="orderInfo.rollbackEnable == 'Y'">业务回退</button>-->
        <!-- <button class="btn-opt rollbacn-btn" @click="doRollback()" v-if="orderInfo.rollbackPermit == 'Y'">业务回退</button> -->
        <button class="btn-opt delete-btn" @click="doDeleteOrder()"
          v-if="orderInfo.paystatus == 0&&orderInfo.fivegFlag!='Y'">删除订单</button>
        <button class="btn-opt confirm-btn" @click="toPay()"
          v-if="orderInfo.paystatus == '0'&&orderInfo.fivegFlag!='Y'">支 付</button>
      </div>

    </div>
    <div class="empty-content" v-else>
      <img src="../../../../static/fifthg/2023/sucai_zanwuyewu.png" alt="" class="img-empty">
      <span class="sp-empty">未查询到订单详情</span>
    </div>
  </div>
</template>
<script>
import {
  mapGetters,
  mapActions,
  mapMutations,
  mapState,
} from 'vuex';
export default {
  data() {
    return {
      list: null,
      userinfo: null,
      currentPage: 1,
      pagesize: 1,
      custorderid: null,
      city: null,
      orderInfo: null,
      operid: null,
      deptid: null,
      showOpt: true,
      canFeebookPay: 1,
      flag: null,
      ejectSerialno: null,
      ejectFees: null,
      bizType: null
    }
  },
  computed: {
    ...mapGetters({
      custInfo: "getCustInfo",
      stepInfo: "getStepInfo",
      salesInfo: 'getSalesInfo',
    }),
  },
  activated() {
  },
	onLoad(options) {
    this.custorderid = this.options.custorderid || ''
    this.city = this.options.city || ''
    this.operid = this.options.operid || ''
    this.deptid = this.options.deptid || ''
    this.flag = this.options.flag || ''
    this.ejectSerialno = this.options.ejectSerialno || ''
    this.ejectFees = this.options.sum || ''
    this.canFeebookPay = this.options.canFeebookPay
    this.bizType = this.options.bizType
    if (!this.custorderid) {
      uni.showToast({
        title: '订单号为空',
        icon: 'none'
      })
      return
    }
    this.loadData()
	},
  mounted() {
  },
  methods: {
    async loadData() {
      console.log('loadData')
      this.list = null
      
      const {responseBody: result} = await this.$http.post(this.$apis.queCustorder, {
        currentPage: this.currentPage,
        pagesize: this.pagesize,
        detail: true,
        city: this.city,
        operator: this.operid,
        oprdep: this.deptid,
        custorderid: this.custorderid,
      })
        
      this.orderInfo = result.custorders[0]

    },
    async doDeleteOrder() {
      var that = this
      this.$vux.confirm.setInputValue('value')
      this.$vux.confirm.show({
        // 组件除show外的属性
        onCancel() { },
        onConfirm() {
          that.delete()
        },
        content: '是否删除订单？'
      })
    },
    async delete() {
      
      const {responseBody: result} = await this.$http.post(this.$apis.deleteOrder, {
        orderid: this.custorderid,
        orderstatus: this.orderInfo.orderstatus,
        syncmode: this.orderInfo.syncmode
      })
      console.log(result)
      this.showOpt = false
      uni.showToast({
        title: '订单删除成功',
        icon: 'none'
      })
    },
    toPay() {
      this.$rauter.navigateTo({
        url: 'Pay',
        data: {
          custorderid: this.custorderid, fees: this.orderInfo.fees, opcodename: this.orderInfo.opcodename, canFeebookPay: this.canFeebookPay, flag: this.flag, ejectSerialno: this.ejectSerialno,
          ejectFees: this.ejectFees, bizType: this.bizType, opcode: this.orderInfo.opcode
        }
      });
    },
  }
}
</script>

<style lang="scss" scoped>
* {}

.title-box {
  height: 0.8 * 100rpx;
  line-height: 0.8 * 100rpx;
  width: 7.5 * 100rpx;
  color: #666666;
  text-align: left;
  padding-left: 0.2 * 100rpx;
  box-sizing: border-box;
  font-size: 0.3 * 100rpx;
}

.item {
  display: flex;
  width: 7.1 * 100rpx;
  align-items: baseline;
}

.sp-mid {
  min-width: 2 * 100rpx;
  display: inline-block;
  text-align: justify;
  text-align-last: justify;
  padding-left: 0.2 * 100rpx;
  font-weight: 400;
  font-size: 0.32 * 100rpx;
  padding-top: 0.1 * 100rpx;
  padding-bottom: 0.1 * 100rpx;
}

.sp-right {
  width: 5 * 100rpx;
  text-overflow: ellipsis;
  overflow-x: hidden;
  text-align: left;
  padding-left: 0.2 * 100rpx;
  display: inline-block;
  white-space: nowrap;
  color: #666;
  font-size: 0.32 * 100rpx;
  padding-top: 0.1 * 100rpx;
  padding-bottom: 0.1 * 100rpx;
}

.sp-right-addr {
  width: 5 * 100rpx;
  text-overflow: ellipsis;
  overflow-x: hidden;
  text-align: left;
  padding-left: 0.2 * 100rpx;
  display: inline-block;
  color: #666;
  padding-top: 0.1 * 100rpx;
  padding-bottom: 0.1 * 100rpx;
  font-size: 0.32 * 100rpx;
}

.card-f {
  width: 7.1 * 100rpx;
  margin-left: 0.2 * 100rpx;
  background-color: #fff;
  border-radius: 0.1 * 100rpx;
}

.empty-content {
  margin-top: 2 * 100rpx;
}

.img-empty {
  width: 1.1 * 100rpx;
  height: 1.3 * 100rpx;
  background-size: 1.1 * 100rpx 1.3 * 100rpx;
  background-repeat: no-repeat;
  background-image: url("../../../../static/fifthg/2023/sucai_zanwuyewu.png");
}

.sp-empty {
  display: block;
  font-size: .28 * 100rpx;

  font-weight: 400;
  color: #333333;
}

.opt-order-box {
  width: 7.1 * 100rpx;
  height: 0.8 * 100rpx;
  margin: 0 auto;
  margin-top: 0.2 * 100rpx;
  text-align: left;
}

.btn-opt {
  display: inline-block;
  outline: none;
  border: none;
  height: 0.6 * 100rpx;
  line-height: 0.6 * 100rpx;
  padding-left: 0.3 * 100rpx;
  padding-right: 0.3 * 100rpx;
  border-radius: 0.1 * 100rpx;
  margin-left: 0.1 * 100rpx;
}

.open-elec-btn {
  background-color: #3875D0;
  color: #fff;
}

.rollbacn-btn {
  color: #3875D0;
  background-color: #fff;
}

.confirm-btn {
  background-color: #3875D0;
  color: #fff;

  font-weight: 400;
}

.delete-btn {
  background: #E13F3F;
  ;
  color: #fff;

}

.card {
  width: 7.1 * 100rpx;
  margin-left: 0.2 * 100rpx;
  background-color: #fff;
  border-radius: 0.1 * 100rpx;
  margin-top: 0.1 * 100rpx;
  box-sizing: border-box;
  padding-top: 0.1 * 100rpx;
  padding-bottom: 0.1 * 100rpx;
}
</style>
