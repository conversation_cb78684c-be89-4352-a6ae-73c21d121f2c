<template>
  <c-layout title="产品详情">

    <div class="card">
      <div class="item">
        <span class="prd-info" v-if="prdInfo.objtype == '0'">产品</span>
        <span class="prd-info pkg-info" v-else-if="prdInfo.objtype == '1'">套餐</span>
        <span class="prd-info-permark digit" v-if="prdInfo.permarks && prdInfo.permarks.indexOf('1') > -1">数字</span>
        <span class="prd-info-permark cm" v-if="prdInfo.permarks && prdInfo.permarks.indexOf('2') > -1">宽带</span>
        <span class="prd-info-permark vod" v-if="prdInfo.permarks && prdInfo.permarks.indexOf('3') > -1">互动</span>
        <span class="prd-info-permark ott" v-if="prdInfo.permarks && prdInfo.permarks.indexOf('4') > -1">OTT</span>
      </div>
      <div>
        <div class="item">
          <span class="right-info">{{ prdInfo.knowname }}</span>
        </div>
        <div class="item">
          <span class="right-info red">价格：{{ prdInfo.price }}/{{ prdInfo.unitName }}</span>
        </div>
        <div class="content-brief" v-if="knowsInfo && knowsInfo.brief">{{ knowsInfo.brief }}</div>
      </div>
    </div>

    <div class="title">订购参数</div>
    <div class="card">
      <div class="order-item">
        <div class="left">订购周期</div>
        <div class="right">
          <input type="number" class="ipt-count" v-model="orderNum">
          <span class="sp-unit">{{ prdInfo.unitName }}</span>
        </div>
      </div>

      <div class="order-item">
        <div class="left">是否顺延</div>
        <div class="right">
          <span class="sp-post-on sp-post-select" style="margin-right: 0.2rem" v-if="isPoston == 'Y'">是</span>
          <span class="sp-post-on" style="margin-right: 0.2rem" v-else>是</span>
          <span class="sp-post-on sp-post-select" v-if="isPoston == 'N'">否</span>
          <span class="sp-post-on" v-else>否</span>
        </div>
      </div>

      <div v-if="prdInfo.objtype == '1'">
        <div class="order-item">
          <div class="left">数字互动预约时间</div>
          <picker mode='date' class="selector" :value="digitVodPreoperationTime" @change="changeDigitVodDate"
            format="YYYY-MM-DD">
            <span class="right">{{ digitVodPreoperationTime || '选择数字互动预约时间' }}</span>
          </picker>
        </div>
        <div class="order-item">
          <div class="left">宽带预约时间</div>
          <picker mode='date' class="selector" :value="cmPreoperationTime" @change="changeCmPreDate" format="YYYY-MM-DD">
            <span class="right">{{ cmPreoperationTime || '选择宽带预约时间' }}</span>
          </picker>
        </div>
      </div>
      <div v-else>
        <div class="order-item">
          <div class="left">预约开始时间</div>
          <picker mode='date' class="selector" :value="prdPreoperationTime" @change="changePrdPreDate"
            format="YYYY-MM-DD">
            <span class="right">{{ prdPreoperationTime || '选择预约开始时间' }}</span>
          </picker>
        </div>
      </div>
    </div>

    <template v-if="knowsInfo && knowsInfo.detaillist">
      <div class="title">选择设备账号</div>
      <div class="card">
        <div class="order-item" v-for="(it, idx) in knowsInfo.detaillist" :key="idx">
          <div class="left">{{ it.objmsg }}</div>
          <select class="select-sty" @change="onSelectChange(it)"
            v-model="modArray[it.objcode == 'S' ? ST_PERMARK : it.objcode]">
            <option :value="info.mcode" v-for="(info, i) in it.objlist" :key="i">{{ info.mname }}</option>
          </select>
        </div>
      </div>
    </template>

    <LxPackage :resource="lxRes" :scopeList="scopeList" :deliveryList="deliveryList" ref="lxPackageRef"></LxPackage>

    <div class="btn-box-o">
      <button class="join-gwc" @click="createUserSelectPrd">实时开户</button>
    </div>
  </c-layout>
</template>

<script>
import CForm from "../../components/form/CForm";
import CQueryCard from "../../components/list/CQueryCard";
import CQueryInfoItem from "../../components/list/CQueryInfoItem";
import CNoData from "../../components/list/CNoData";
import { toRange } from "../../enums/enums";
import CLayout from "../../components/layout/CLayout";
import { getMonth } from "../../../../common/js/DateUtils";
import { fenToYuan } from "../../../../common/js/AmountUtils";

import LxPackage from '@/pages/FifthG/components/gdm/LxPackage'
import {
  mapGetters,
  mapActions,
  mapMutations,
  mapState,
} from 'vuex';
export default {
  
  components: { CLayout, LxPackage },
  data() {
    return {
      options: {},
      list: null,
      prdInfo: {},
      knowsInfo: null,
      maxNumber: null,
      minNumber: null,
      orderNum: '',
      modArray: [],
      developerName: '',
      prdPreoperationTime: null,
      digitVodPreoperationTime: null,
      cmPreoperationTime: null,
      houseid: null,
      keyno: null,
      patchid: null,
      permark: null,
      devListKyes: [],
      isPoston: 'N',
      flag: null,
      operInfo: null,
      ejectSerialno: null,
      sum: null,
      defaultKeyno: null,
      ST_PERMARK: 9, // 软终端业务类型
      enjoyMark: null,    // 是否5g乐享套餐
      lxRes: {},
      selectList: {},
      poolSelList: {},
      psiprddets: [],    // 进销存
      scopeList: [],   // 出库方式
      salespkgcode: '',
      deliveryList: [],   // 派送方式
      selectPoolSel: [],
      reFresh: true,
    };
  },
  computed: {
    ...mapGetters({
      custInfo: "getCustInfo",
			userInfo: 'getUserInfo',
      selectProduct:'getSelectProduct'
    }),
  },
  // beforeRouteLeave(to, from, next) {
  //   const targetPath = ['/lxPackage/rights', '/custdev/createUser']
  //   if (!targetPath.includes(to.path)) {
  //     localStorage.removeItem('poolSels')
  //     localStorage.removeItem('produceCodes')
  //   }
  //   next()
  // },
  // beforeRouteEnter(to, from, next) {
  //   const fromPath = from.path;
  //   if (fromPath === '/prds/prdLists') {
  //     localStorage.removeItem('poolSels');
  //     localStorage.removeItem('produceCodes');
  //   }
  //   next();
  // },
  mounted() {
    const options = this.$route.query
    this.prdInfo = {};
    this.prdInfo.catalogid = options.catalogid;
    this.prdInfo.knowid = options.knowid;
    this.prdInfo.knowname = options.knowname;
    this.prdInfo.objid = options.objid;
    this.prdInfo.objtype = options.objtype || '0';
    this.prdInfo.permarks = options.permarks || '';
    this.prdInfo.price = options.price;
    this.prdInfo.unitName = options.unitName;
    this.flag = options.flag || '';
    this.ejectSerialno = options.serialno || '';
    this.sum = options.sum || '';
    this.defaultKeyno = options.defaultKeyno || '';
    this.operInfo = this.userInfo;
    this.initBaseParam();
    this.getscopeListFn()
    this.getdeliveryListFn()
    this.loadData();
  },
  activated() {
    const options = this.$route.query
    this.prdInfo = {};
    this.prdInfo.catalogid = options.catalogid;
    this.prdInfo.knowid = options.knowid;
    this.prdInfo.knowname = options.knowname;
    this.prdInfo.objid = options.objid;
    this.prdInfo.objtype = options.objtype || '0';
    this.prdInfo.permarks = options.permarks || '';
    this.prdInfo.price = options.price;
    this.prdInfo.unitName = options.unitName;
    this.flag = options.flag || '';
    this.ejectSerialno = options.serialno || '';
    this.sum = options.sum || '';
    this.defaultKeyno = options.defaultKeyno || '';
    this.operInfo = this.userInfo;
    this.initBaseParam();
    this.getscopeListFn()
    this.getdeliveryListFn()
    this.loadData();
    this.selectPoolSel = this.selectProduct
  },
  watch:{
    prdInfo(){
      this.reFresh= false
    }
  },
  methods: {
    ...mapMutations({
      setLxPost: "setLxPost"
    }),
    changeDigitVodDate(e) {
      this.digitVodPreoperationTime = e.detail.value
    },
    changeCmPreDate(e) {
      this.cmPreoperationTime = e.detail.value
    },
    changePrdPreDate(e) {
      this.prdPreoperationTime = e.detail.value
    },
    async getscopeListFn() {
      const {responseBody: result} = await this.$http.post(this.$apis.getdata, {
        gcode: 'STOCK_OUT_METHOD'
      })
      console.log('STOCK_OUT_METHOD', result);
      this.scopeList = result.paramdata
    },
    async getdeliveryListFn() {
      const {responseBody: result} = await this.$http.post(this.$apis.getdata, {
        gcode: 'DELIVERY_METHOD'
      })
      console.log('DELIVERY_METHOD', result);
      this.deliveryList = result.paramdata
    },

    async loadData() {
      var param = {};
      param.custid = this.custInfo.custid;
      if (this.flag == 'createUser') {
        param.opcode = 'BIZ_USER_NEW';
      } else if (this.flag == 'inversion') {
        param.opcode = 'BIZ_USER_INVERSION';
      } else {
        param.opcode = 'BIZ_PRD_ORDER';
      }
      param.catalogid = this.prdInfo.catalogid;
      param.knowid = this.prdInfo.knowid;
      param.knowname = this.prdInfo.knowname;
      param.needdetail = true;
      const {responseBody: result} = await this.$http.post(this.$apis.querySalespkgKnowFixed, param)

      console.log('result.knows', result.knows)
      if (result.knows && result.knows.length > 0) {
        if (this.flag != 'createUser') {
          if (result.knows[0].detaillist) {
            result.knows[0].detaillist.forEach(it => {
              // 由于软终端业务类型是S，非数字，放入list中不生成长度，这里将S转换成9特殊处理
              var k1, k2, k3;
              if (it.objcode == 'S') {
                k1 = this.ST_PERMARK;
                k2 = this.ST_PERMARK + '_keyno';
                k3 = this.ST_PERMARK + '_mname';
              }
              else {
                k1 = it.objcode;
                k2 = it.objcode + '_keyno';
                k3 = it.objcode + '_mname';
              }
              if (this.defaultKeyno) {
                it.objlist.forEach(i => {
                  if (i.keyno == this.defaultKeyno) {
                    this.modArray[k1] = i.mcode;
                    this.modArray[k2] = i.keyno;
                    this.modArray[k3] = i.mname;
                  }
                });
                this.modArray[k1] = this.modArray[k1] || it.objlist[0].mcode;
                this.modArray[k2] = this.modArray[k2] || it.objlist[0].keyno;
                this.modArray[k3] = this.modArray[k3] || it.objlist[0].mname;
              } else {
                this.modArray[k1] = it.objlist[0].mcode;
                this.modArray[k2] = it.objlist[0].keyno;
                this.modArray[k3] = it.objlist[0].mname;
              }
              this.devListKyes.push(it.objcode);
            });
          }
        }
        console.log('modArray', this.modArray);
        this.knowsInfo = result.knows[0];
        this.maxNumber = this.knowsInfo.maxNumber;
        this.minNumber = this.knowsInfo.minNumber;
        this.orderNum = this.knowsInfo.defauNumber;
        this.prdInfo.knowsInfo = this.knowsInfo;
        this.enjoyMark = this.knowsInfo.enjoyMark
        let selectList = []
        let poolSelList = []
        if (this.knowsInfo.selectList && this.knowsInfo.selectList.length > 0) {
          selectList = this.knowsInfo.selectList[0]    // 软件优惠
        }
        if (this.knowsInfo.poolSelList && this.knowsInfo.poolSelList.length > 0) {
          poolSelList = this.knowsInfo.poolSelList[0]    // 权益
        }
        let psiprddets = this.knowsInfo.psiprddets   // 进销存
        let salespkgcode = this.knowsInfo.salespkgcode
        this.salespkgcode = this.knowsInfo.salespkgcode

        this.lxRes = {
          selectList,
          poolSelList,
          psiprddets,
          salespkgcode,
        }

        console.log(this.lxRes, 'lxRes')
      }
    },

    onSelectChange() {
      console.log(this.modArray);
    },
    
    filterUserServs() {
      const { allServs, servs } = this.custInfo;
      let list = this.$_ua == 'CMMS-Android-app' ? allServs : servs;
      const res = list.filter(n => {
        return !!n.houseid;
      });
      return res;
    },
    initBaseParam() {
      if (this.flag == 'createUser') {
        return;
      }
      const userServer = this.filterUserServs();
      this.houseid = userServer[0].houseid;
      this.patchid = userServer[0].patchid;
      this.permark = userServer[0].permark;
    },
    cancelDigitVod() {
      this.digitVodPreoperationTime = null;
    },
    cancelCm() {
      this.cmPreoperationTime = null;
    },
    cancelPrdPreoperation() {
      this.prdPreoperationTime = null;
    },

    createUserSelectPrd() {
      if (!this.orderNum) {
        uni.showToast({
          title: '订购周期不能为空',
          icon: 'none'
        })
        return;
      }
      if (this.orderNum && this.orderNum <= 0) {
        uni.showToast({
          title: '订购周期不能小于等于0',
          icon: 'none'
        })
        return;
      }
      var receiptform = {}
      if (this.enjoyMark == 'Y') {
        console.log('this.selectList.selectid', this.selectList.selectid);
        console.log('this.poolSelList.pooltype', this.poolSelList.pooltype);
        console.log('this.selectPoolSel', this.selectPoolSel);
        console.log('this.selectProduct', this.selectProduct);
        console.log('this.$refs.lxPackageRef.selectProduct', this.$refs.lxPackageRef.selectProduct);    // 选择的优惠
        if (this.poolSelList.pooltype && this.poolSelList.selList.length > 0) {
          console.log('1', this.$refs.lxPackageRef.produceCodes);
          if (this.selectPoolSel.length <= '0') {
            uni.showModal({
              title: '温馨提示',
              content: '未选择权益池权益商品，请选择后提交。',
              showCancel: true, //没有取消按钮的弹框
              buttonText: '确定',
              success: function (res) {
                if (res.confirm) {
                }
              }
            })
            return
          }
        }
        console.log('5G乐享套餐', this.$refs.lxPackageRef.radio);
        if (this.psiprddets.length > 0 && this.$refs.lxPackageRef.radio == '3') {
          uni.showModal({
            title: '温馨提示',
            content: '未选择配送方式，请选择后再提交业务。',
            showCancel: true, //没有取消按钮的弹框
            buttonText: '确定',
            success: function (res) {
              if (res.confirm) {
              }
            }
          })
          return
        }
        if (this.psiprddets && this.psiprddets.length > 0) {
          console.log('lxPackageRef');
          if (this.$refs.lxPackageRef.radio == '1') {
            if (!this.$refs.lxPackageRef.receipttel) {
              uni.showToast({
                title: '请输入手机号',
                icon: 'none'
              })
              return
            }
            if (this.$refs.lxPackageRef.receipttel && !this.$refs.lxPackageRef.receipttel.match("1[3-9]\\d{9}")) {
              uni.showToast({
                title: '请填入正确的手机号',
                icon: 'none'
              })
              return
            }
            if (!this.$refs.lxPackageRef.receiptname) {
              uni.showToast({
                title: '请输入收货人',
                icon: 'none'
              })
              return
            }
            if (!this.$refs.lxPackageRef.receiptaddr) {
              uni.showToast({
                title: '请输入收货地址',
                icon: 'none'
              })
              return
            }
            receiptform.receipttel = this.$refs.lxPackageRef.receipttel
            receiptform.receiptname = this.$refs.lxPackageRef.receiptname
            receiptform.receiptaddr = this.$refs.lxPackageRef.receiptaddr
          }
          receiptform.delivermode = this.$refs.lxPackageRef.radio
          localStorage.setItem('receiptform', JSON.stringify(receiptform))
        }
        if (this.psiprddets && this.psiprddets.length > 0) {
          for (const item of this.psiprddets) {
            item.artcode = this.$refs.lxPackageRef.produceCodes[item.pkgrecid]
            item.retrieval = this.$refs.lxPackageRef.scopeType[item.pkgrecid]
            if (item.retrieval == 'LOCAL_SALE' && !item.artcode) {
              uni.showModal({
                title: '温馨提示',
                content: '出库方式选择店销模式时必须输入商品码。',
                showCancel: true, //没有取消按钮的弹框
                buttonText: '确定',
                success: function (res) {
                  if (res.confirm) {
                  }
                }
              })
              return
            }
          }
        }
      }
      console.log('psiprddets', this.psiprddets);
      var selectPrdsList = localStorage.getItem('createUserPrds');
      if (selectPrdsList) {
        selectPrdsList = JSON.parse(selectPrdsList);
      } else {
        selectPrdsList = [];
      }
      var has = false;
      var prdsList = selectPrdsList;
      selectPrdsList.forEach(it => {
        if (it.knowid == this.prdInfo.knowid) {
          has = true;
        }
      });
      if (has) {
        console.log('111');
        this.$router.go(-2);
        return;
      }
      var prd = this.prdInfo;
      prd.orderNum = this.orderNum;
      if (this.prdInfo.objtype == '0') {
        prd.stime = this.prdPreoperationTime;
      } else {
        prd.stime = this.digitVodPreoperationTime;
      }
      prd.cmstime = this.cmPreoperationTime;
      prdsList.push(prd);
      var prdseltdet = []
      var prd = {}
      prd.selectid = this.selectList.selectid
      prd.pidstr = this.$refs.lxPackageRef.selectProduct.map(item => item.pid).join(',')
      console.log(this.$refs.lxPackageRef.selectProduct, 'selectProduct')
      prdseltdet.push(prd)
      localStorage.setItem('createUserPrds', JSON.stringify(prdsList));
      if (this.enjoyMark == 'Y') {
        console.log('lxPost');
        console.log('selectList',this.selectList);
        console.log('psiprddets',this.psiprddets);
        console.log('selectPoolSel',this.selectPoolSel);
        console.log('salespkgcode',this.salespkgcode);
        if (this.selectList.selectid || this.psiprddets.length > 0 || this.selectPoolSel.length > 0) {
          // localStorage.setItem('selectPoolSel', JSON.stringify(this.selectPoolSel))
          // localStorage.setItem('psiprddets', JSON.stringify(this.psiprddets))
          // localStorage.setItem('prdseltdet', JSON.stringify(prdseltdet))
          // localStorage.setItem('salespkgcode', JSON.stringify(this.salespkgcode))
          
          this.setLxPost({
            selectPoolSel: this.selectPoolSel, 
            psiprddets: this.psiprddets, 
            prdseltdet, 
            salespkgcode: this.salespkgcode,
            receiptform
          })

          console.log({
            selectPoolSel: this.selectPoolSel, 
            psiprddets: this.psiprddets, 
            prdseltdet, 
            salespkgcode: this.salespkgcode,
            receiptform})
        }
      }
      this.$router.go(-2);
    },
    inversionUserSelectPrd() {
      if (!this.orderNum) {
        uni.showToast({
          title: '订购周期不能为空',
          icon: 'none'
        })
        return;
      }
      if (this.orderNum && this.orderNum <= 0) {
        uni.showToast({
          title: '订购周期不能小于等于0',
          icon: 'none'
        })
        return;
      }
      var prd = this.prdInfo;
      prd.orderNum = this.orderNum;
      if (this.prdInfo.objtype == '0') {
        prd.stime = this.prdPreoperationTime;
      } else {
        prd.stime = this.digitVodPreoperationTime;
      }
      prd.cmstime = this.cmPreoperationTime;
      localStorage.setItem('inversionUserPrds', JSON.stringify(prd));
      this.$router.go(-2);
    }
  },
}
</script>

<style lang="scss" scoped>
* {
  text-align: center;
}

.card {
  background-color: #fff;
  width: 7.5 * 100rpx;
  margin: 0 auto;
  padding-top: 0.2 * 100rpx;
  padding-bottom: 0.2 * 100rpx;
}

.item {
  height: 0.5 * 100rpx;
  width: 7.1 * 100rpx;
  display: flex;
  padding-left: 0.3 * 100rpx;
  box-sizing: border-box;
}

.right-info {
  display: inline-block;
  text-align: left;
  height: 0.5 * 100rpx;
  line-height: 0.5 * 100rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 5.5 * 100rpx;
}

.prd-info {
  display: inline-block;
  background-color: #9BB9E6;
  color: #fff;
  font-size: 0.24 * 100rpx;
  padding-left: 0.1 * 100rpx;
  padding-right: 0.1 * 100rpx;
  height: 0.4 * 100rpx;
  line-height: 0.4 * 100rpx;
  border-radius: 0.06 * 100rpx;
}

.pkg-info {
  background-color: #EE904C;
  color: #fff;
}

.prd-info-permark {
  display: inline-block;
  color: #fff;
  font-size: 0.24rem;
  font-size: 0.24 * 100rpx;
  padding-left: 0.1 * 100rpx;
  padding-right: 0.1 * 100rpx;
  height: 0.4 * 100rpx;
  line-height: 0.4 * 100rpx;
  border-radius: 0.06 * 100rpx;
  margin-left: 0.1 * 100rpx;
}

.digit {
  background-color: #9BB9E6;
}

.cm {
  background-color: #9BB9E6;
}

.vod {
  background-color: #FB9999;
}

.ott {
  background-color: #F4C082;
}

.title {
  width: 7.5 * 100rpx;
  height: 0.8 * 100rpx;
  line-height: 0.8 * 100rpx;
  text-align: left;
  padding-left: 0.3 * 100rpx;
  box-sizing: border-box;
  color: #999999;
}

.order-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 7.1 * 100rpx;
  height: 0.8 * 100rpx;
  border-bottom: 1px solid #DDE4EB;
  margin: 0 auto;
  box-sizing: border-box;
}

.left {
  line-height: 0.8 * 100rpx;
}

.right {
  display: flex;
  align-items: center;
  line-height: 0.8 * 100rpx;
}

.ipt-count {
  border: none;
  outline: none;
  width: 1 * 100rpx;
  background-color: #eee;
  text-align: center;
}

.sp-unit {
  margin-left: 10rpx;
}

.content-brief {
  width: 7.1 * 100rpx;
  display: flex;
  padding-left: 0.3 * 100rpx;
  box-sizing: border-box;
  text-align: left;
  color: #666666;
}

.red {
  color: #DF492C;
}

.sp-post-on {
  display: inline-block;
  width: 0.6 * 100rpx;
  height: 0.4 * 100rpx;
  border: 1px solid #eee;
  border-radius: 0.08 * 100rpx;
  margin-top: 0.15 * 100rpx;
  color: #ccc;
  line-height: 40rpx;
}

.sp-post-select {
  background-color: #9BB9E6;
  color: #fff;
}

.select-sty {
  outline: none;
  border: none;
  float: right;
  height: 0.6 * 100rpx;
  margin-top: 0.1 * 100rpx;
  max-width: 5.2 * 100rpx;
}

.title-box {
  width: 7 * 100rpx;
  height: 0.8 * 100rpx;
  line-height: 0.8 * 100rpx;
  text-align: left;
  padding-left: 0.3 * 100rpx;
  color: #999;
  margin: 0 auto;
  font-size: 0.28 * 100rpx;

  font-weight: 400;
}

/deep/.dp-header .dp-item.dp-right {
  color: #3875D0 !important;
}

.date-sty {
  width: 3 * 100rpx;
  height: 0.3 * 100rpx;
}

.btn-box-o {}

.join-gwc {
  border: none;
  outline: none;
  background-color: #EE904C;
  color: #fff;
  font-size: .32 * 100rpx;
  font-weight: 400;
  border-radius: 0.08 * 100rpx;
  margin: 100rpx 40rpx;
  padding: 20rpx 0;
}

.order-confirm {
  border: none;
  outline: none;
  width: 3.3 * 100rpx;
  height: .8 * 100rpx;
  background-color: #E13F3F;
  color: #fff;
  font-size: .32 * 100rpx;

  font-weight: 400;
  border-radius: 0.08 * 100rpx;
  margin-top: 0.1 * 100rpx;
}

/deep/ .weui-dialog {
  right: 0.6rem !important;
}
</style>