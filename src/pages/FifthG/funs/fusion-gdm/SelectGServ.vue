<template>
  <c-layout title="固网用户">
    <div class="main">
      <view class="card sim">
        <view class="check-group">
          <view class="item" :class="{ active: n.isChecked }" v-for="n, i in deviceOptions" :key="i"
            @click="changDevice(n, i)">
            <view class="checkbox">
              <view class="label">
                <div>{{ n.logicdevno }}</div>
                <div>{{ n.salesname }}</div>
              </view>
              <view class="status" :class="{ checked: n.isChecked }"></view>
            </view>
          </view>
        </view>
      </view>

      <div class="errorinfo">{{ errorInfo }}</div>
      <button class="btn-submit can-btn" @click="createGServ()">创建固网用户</button>
      <button class="btn-submit can-btn" @click="doSubmit()">提 交</button>
    </div>

    <!-- 安装地址 -->
    <div class="dialog" v-show="dialog">
      <div class="mask"></div>
      <div class="sheet">
        <div class="head">
          <div class="title">选择固网商品用户</div>
          <div class="close" @click="dialog = false"></div>
        </div>
        <div class="body">
          <div class="list">
            <div class="item" :class="{ active: n.isChecked }" v-for="n, i in deviceOptions" :key="i"
              @click="changDevice(n, i)">
              <view class="ctn">
                <view>
                  <text class="user">{{ n.logicdevno }}</text>
                </view>
                <view class="props">
                  <view class="attr">开户时间：{{ n.intime.replace('T', " ") }}</view>
                  <view class="attr">订购套餐：{{ n.salesname }}</view>
                </view>
              </view>
              <div class="radiobox" :class="{ checked: n.isChecked }"></div>
            </div>
          </div>
        </div>
        <div class="foot">
          <div class="btn" @click="confirmDevice">确认</div>
          <div class="btn" @click="createGServ">创建固网用户</div>
        </div>
      </div>
    </div>
  </c-layout>
</template>

<script>
import CForm from "../../components/form/CForm";
import CQueryCard from "../../components/list/CQueryCard";
import CQueryInfoItem from "../../components/list/CQueryInfoItem";
import CNoData from "../../components/list/CNoData";
import { mapGetters, mapActions } from "vuex";
import { toRange } from "../../enums/enums";
import CLayout from "../../components/layout/CLayout";
import { getMonth } from "../../../../common/js/DateUtils";
import { fenToYuan } from "../../../../common/js/AmountUtils";

export default {
  components: { CLayout },
  data() {
    return {
      citylist: [],
      userinfo: null,
      operinfo: null,
      cityCode: null,
      cardType: null,
      cardTypeList: [],
      canSubmit: false,
      custName: null,
      cardNo: null,
      phone: null,
      errorInfo: '',
      picArr: null,

      selected: {},
      deviceOptions: [],
      dialog: false,
      department: null,
      departments: [],
      options: {}
    }
  },
  onLoad(options) {
    this.options = options;
    this.initSaveProcess()
  },
  async mounted() {
    localStorage.setItem('GServe', JSON.stringify(this.$route.query))
    if (!this.salesInfo.objid || !this.stepInfo.poid) {
      await this.queProcess({
        poid: this.$route.query.poid,
      })
    }
    console.log('this.salesInfo', this.salesInfo)
    console.log('this.stepInfo', this.stepInfo)
    console.log('this.custInfo', this.custInfo)
    await this.loadDepartment()
    // this.queFixorders(this.salesInfo.pcode)
    this.queFixorders()
  },

  computed: {
    ...mapGetters({
      custInfo: 'getCustInfo',
      salesInfo: 'getSalesInfo',
      stepInfo: 'getStepInfo',
      userInfo: 'getUserInfo'
    })
  },

  methods: {
    ...mapActions({
      queProcess: 'queProcess',
      saveProcess: 'saveProcess',
      setUserInfo: "setUserInfo",
    }),
    createGServ() {
      const that = this
      if (localStorage.getItem('autoDeptSwitch') === 'Y' && that.custInfo && that.userInfo && that.custInfo.areaid != that.userInfo.areaid) {
        let list = that.departments.filter(i => i.areaid == that.custInfo.areaid && i.agentid === that.userInfo.agentid)
        if (list.length === 1) {
          that.department = list[0]
          that.bindSystemChange()
          return
        }

        uni.showModal({
          title: '提示',
          content: `工号【${that.userInfo.loginname}】，当前业务区【${that.userInfo.areaid}】，客户业务区【${that.custInfo.areaid}】，客户业务区下部门数量【${list.length}】，自动切换部门异常，请手动切换部门`,
          showCancel: false,
          success: (res) => {
            if (res.confirm) {
              window.location.href =  process.env.TOOL_BOX_URL + '/custdev/createUser?type=GDM&poid=' + this.stepInfo.poid
            }
          }
        })
        return
      }


      window.location.href =  process.env.TOOL_BOX_URL + '/custdev/createUser?type=GDM&poid=' + this.stepInfo.poid
      // window.location.href = 'http://localhost:8082/apk/cmms-data/custdev/createUser?type=GDM&poid=' + this.stepInfo.poid
    },
    selectChangeCardType(val) {
      console.log(this.cardType)
      this.cardType = val
    },

    openSelectDialog() {
      this.dialog = true
    },

    queFixorders(pcode) {
      let params = {
        bizcode: 'BIZ_FGUSER_NEW',
        custid: this.custInfo.custid,
        // salesid: this.salesInfo.objid
      };
      console.log(this.salesInfo)
      this.$http.post(this.$apis.queFixorders, params).then(resp => {
        console.log('this.$apis.queFixorders')
        console.log(resp)
        let datas = resp.responseBody.data;
        if (datas && datas.length > 0) {
          datas.forEach(n => {
            n.isChecked = false
          })
          this.fixorders = datas;
          if (pcode === 'MO') {
            const filterFixorders = datas.filter(function (data) {
              return data.permark === '1' || data.permark === 'S'
            })
            this.fixorders = filterFixorders
          }
          console.log('this.fixorders', this.fixorders)
          // this.selected = this.fixorders[0];
          if (this.stepInfo?.data?.GDM_GWDX) {
            const serv = this.stepInfo.data.GDM_GWDX
            this.fixorders.forEach(item => {
              if (item.servid == serv.servid) {
                item.isChecked = true
                this.selected = item
              }
            })
          }

          this.deviceOptions = this.fixorders
        }
      });
    },

    showModal(content) {
      uni.showModal({
        title: '温馨提示',
        content: content,
        confirmText: '我知道了',
        showCancel: false,
        cancelColor: '#576B95',
        success: res => {
          if (res.confirm) {
            this.$rauter.navigateBack({
              delta: 1
            });
          }
        }
      });
    },

    changDevice(item, index) {
      this.deviceOptions = this.deviceOptions.map(n => ({
        ...n,
        isChecked: n.servid === item.servid
      }));

      this.selected = item;
    },

    doSubmit() {
      if (!this.selected.servid) {
        this.$util.feeback.showToast({ title: '请选择固网对象' })
        return
      }

      this.selected.broadband = `${this.selected.logicdevno}(${this.selected.servid})${this.selected.salesname}`

      console.log('this.selected.broadband',this.selected.broadband)

      this.saveProcess({
        data: {
          poid: this.stepInfo.poid,
          cpcode: 'GDM_GWDX',
          data: JSON.stringify(this.selected),
          "temp": "1"
        },
        unNeedNext: true,
        afterProcess: () => {
          this.$rauter.navigateTo({
            url: "FusionGDM",
            data: {
              poid: this.stepInfo.poid
            }
          });
        }
      })
    },
    async loadDepartment() {
      if (this.userInfo.loginname) {
        let queryParam = {
          "loginname": this.userInfo.loginname
        }
        let res = await this.$http.post("queryDepartment", queryParam)

        this.departments = res.responseBody;
        console.log('this.departments', this.departments)
      }
    },
    // 选择部门id
    bindSystemChange(menuItem) {
      console.log('this.department', this.department)
      if (!this.department) {
        this.$util.feeback.showToast({
          title: "部门不能为空",
        })
        return
      }

      let parmams = {
        deptid: this.department.id,
        loginname: this.userInfo.loginname,
        accesskey: process.env.LOGIN_ACCESS_KEY,
        md5: true
      }

      this.$http.post(this.$apis.changeDept, parmams)
        .then(resp => {
          this.loginData = resp.responseBody

          this.$util.storage.setStorageSync('userToken', this.loginData.token); // 把登录的token信息缓存到H5中，重要

          this.setUserInfo(this.loginData.loginInfo) // 防止getLoginInfo接口调用失败

          sessionStorage.removeItem('recentFunkList') //清除常用功能

          window.location.href =  process.env.TOOL_BOX_URL + '/custdev/createUser?type=GDM&poid=' + this.stepInfo.poid
        }).catch(error => {
        console.log('error=====>',error)
        this.$util.feeback.showToast({
          title: "切换部门失败",
        })
      })
    },
    initSaveProcess() {
      console.log('onLoad========>saveProcess');
      let stepInfo = JSON.parse(JSON.stringify(this.stepInfo)),
        optionsCode = this.options?.code;
      console.log('stepInfo.poid',stepInfo.poid)
      console.log('this.$route.query.poid',this.$route.query.poid)
      let data = {
        poid: stepInfo.poid || this.$route.query.poid,
        cpcode: optionsCode ? optionsCode : '',
        temp: 0
      };
      let keepData = {
        data,
        unNeedNext: true
      };
      this.saveProcess(keepData);
    }
  }
}
</script>


<style lang="scss" scoped>
/deep/.weui-cell:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 0px solid #D9D9D9 !important;
  color: #D9D9D9;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  left: 15px;
}

.main-content {
  background-color: #fff;
  margin-top: 0.2 * 100rpx;
  padding-top: 0.2 * 100rpx;
  padding-bottom: 0.2 * 100rpx;
}

.item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin: 20rpx 0;
  padding: 18rpx 30rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.info-lf {
  line-height: 0.6 * 100rpx;
  text-align: justify;
  text-align-last: justify;
  white-space: nowrap;
}

.info-rt {
  padding-left: 20rpx;
  line-height: 0.6 * 100rpx;
  text-align: left;
  color: #1F8FF7;
}

.input-type {
  margin-left: 0.2 * 100rpx;
  border: none;
  border-radius: 0.1 * 100rpx;
  padding: 10rpx;
  background: #F2F5F8;
  outline: none;
}

.btn-submit {
  margin: 30rpx;
  border-radius: 0.08 * 100rpx;
  padding: 20rpx 0;
  background: #C3D2E0;
  font-size: 0.32 * 100rpx;
  font-weight: 400;
  color: #FFFFFF;
}

.can-btn {
  background-color: #3875D0;
}

.errorinfo {
  width: 6.6 * 100rpx;
  height: 0.3 * 100rpx;
  font-size: .28 * 100rpx;

  font-weight: 400;
  color: #F23C3C;
  line-height: 36px;
}

/deep/ .weui-select {
  font-size: 0.26rem !important;
}

/deep/ .weui-cell {
  padding: .2rem 0rem !important;
  padding-left: 0.3rem !important;
}

/deep/ .weui-label {
  float: left;
  text-align: left;
  min-width: 1.5 * 100rpx;
  text-align: justify;
  text-align-last: justify;
  margin-right: 0.2 * 100rpx;
  font-size: 0.28 * 100rpx;
  color: #000;
}

.pic-box {
  background-color: #fff;
}


.card.sim {
  display: flex;
  flex-direction: column;

  .field {
    margin-top: 20rpx;
    border-top: solid 1px #f1f1f1;
    padding-top: 20rpx;

    &:nth-child(1) {
      margin-top: 0;
      border-top: none;
      padding-top: 0;
    }

    .main {
      flex: 1;
      display: flex;
      align-items: flex-start;
      font-size: 28rpx;

      .perfix {
        position: relative;
        padding-right: 22rpx;

        .red {
          position: absolute;
          top: 4rpx;
          right: 0;
          font-size: 42rpx;
          color: #f00;
        }

        .sub {
          padding-left: 6rpx;
          font-weight: normal;
          color: #F9A63F;
        }
      }

      .body {
        flex: 1;
        padding: 0 20rpx;
        font-weight: 500;

        .text {
          line-height: 2em;
          color: #1F8FF7;

          &.placeholder {
            font-weight: normal;
            color: #999;
          }

          &.expire {
            color: #f00;
          }
        }

        .input {
          display: block;
          width: 100%;
          border: none;
          padding: 10rpx 0;
          line-height: 2em;

          .input-placeholder {
            font-weight: normal;
            color: #999;
          }

          .uni-input-input {
            line-height: 2em;
          }
        }

        .radio-group {
          display: flex;
          align-items: center;

          .item {
            flex: 1;
            display: flex;
            align-items: center;

            .status {
              position: relative;
              width: 26rpx;
              height: 26rpx;
              margin-right: 10rpx;
              border: solid 1px #212121;
              border-radius: 50%;

              &.checked {
                border-color: #1F8FF7;
                background-color: #1F8FF7;
                background-image: url(../../../../static/fifthg/2023/icon-check.png);
                background-position: center center;
                background-repeat: no-repeat;
                background-size: 20rpx 20rpx;
              }
            }
          }
        }
      }

      .suffix {
        align-self: stretch;

        .btn {
          border: solid 1px #0E67B9;
          border-radius: 40rpx;
          padding: 6rpx 20rpx;
          font-weight: normal;
          color: #0E67B9;
        }

        .icon-arw {
          width: 20rpx;
          height: 100%;
          background-image: url(../../../../static/fifthg/2023/icon-arw.png);
          background-position: center center;
          background-repeat: no-repeat;
          background-size: 20rpx 20rpx;
        }
      }
    }

    .extra {
      padding-top: 10rpx;
      font-size: 28rpx;
      color: #999;
    }
  }

  .check-group {
    margin: 20rpx;

    .item {
      display: flex;
      align-items: center;
      margin: 10rpx 0;
      border-radius: 10rpx;
      padding: 20rpx;

      .checkbox {
        flex: 1;
        display: flex;
        align-items: center;
      }

      .status {
        position: relative;
        width: 26rpx;
        height: 26rpx;
        margin-right: 10rpx;
        border: solid 1px #0E67B9;
        border-radius: 50%;

        &.checked {
          border-color: #1F8FF7;
          background-color: #1F8FF7;
          background-image: url(../../../../static/fifthg/2023/icon-check.png);
          background-position: center center;
          background-repeat: no-repeat;
          background-size: 20rpx 20rpx;
        }
      }

      .label {
        flex:1;
        color: #333;
      }
    }
  }

  .contracts {
    margin: 10rpx 0;
    border-radius: 10rpx;
    border: none;
    padding: 20rpx;
    background-color: #FFF1E1;

    .text {
      text-align: right;
      color: #F9A63F !important;
    }

    .icon-arw {
      background-image: url(../../../../static/fifthg/2023/icon-arw-yellow.png) !important;
    }
  }

}


.dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;

  .mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .4);
    z-index: 1;
  }

  .sheet {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    z-index: 10;

    .head {
      position: relative;
      padding: 20rpx;
      border-bottom: solid 1px #EAEAEA;

      .title {
        padding: 10rpx;
        font-size: 32rpx;
        font-weight: bold;
        color: #212121;
        text-align: center;
      }

      .close {
        position: absolute;
        top: 50%;
        right: 20rpx;
        width: 40rpx;
        height: 40rpx;
        background-image: url(../../../../static/fifthg/2023/icon-close.png);
        background-repeat: no-repeat;
        background-size: 40rpx 40rpx;
        transform: translate(0, -50%);
      }
    }

    .body {
      padding: 20rpx;

      .list {
        max-height: 600rpx;
        overflow: auto;

        .item {
          display: flex;
          align-items: center;
          margin: 20rpx 0;
          border: solid 1px transparent;
          border-radius: 10rpx;
          padding: 20rpx;
          background-color: #F3F3F3;
          font-size: 28rpx;
          color: #212121;

          &.active {
            border-color: #1F8FF7;
            background-color: #F8FCFF;
            color: #1F8FF7;
          }

          .addr {
            flex: 1;
          }

          .radiobox {
            width: 40rpx;
            height: 40rpx;
            margin-left: 10rpx;
            border: solid 1px #666;
            border-radius: 50%;

            &.checked {
              border-color: #1F8FF7;
              background-color: #1F8FF7;
              background-image: url(../../../../static/fifthg/2023/icon-check.png);
              background-position: center center;
              background-repeat: no-repeat;
              background-size: 20rpx 20rpx;
            }
          }
        }
      }

      .input {
        border-radius: 40rpx;
        padding: 20rpx 40rpx;
        background-color: #F3F3F3;
        line-height: 2em;
      }
    }

    .foot {
      display: flex;
      align-items: center;
      padding: 20rpx;
      margin: 40rpx 0;

      .btn {
        flex: 2;
        border-radius: 60rpx;
        padding: 20rpx;
        margin: 0 10rpx;
        background-color: #1F8FF7;
        color: #fff;
        font-size: 32rpx;
        text-align: center;

        &.sub {
          flex: 1;
          background-color: #DEEBFF;
          color: #1F8FF7;
        }
      }


    }
  }
}


.dialog .sheet .body .list .item {
  border: solid 1px #d1d1d1;
  background-color: #FAFAFA;

  .ctn {
    flex: 1;

    .user {
      font-weight: bold;
    }

    .tag {
      margin-left: 10rpx;
      padding: 4rpx 10rpx;
      border: solid 1px #212121;
      border-radius: 6rpx;
    }

    .props .attr {
      color: #666;
    }
  }

  &.active {
    border-color: #F9A63F;
    background-color: #FFF7ED;

    .user {
      color: #F9A63F;
    }

    .tag {
      border-color: #F9A63F;
      color: #F9A63F;
    }

    .radiobox.checked {
      border-color: #F9A63F;
      background-color: #F9A63F;
    }
  }
}
</style>
