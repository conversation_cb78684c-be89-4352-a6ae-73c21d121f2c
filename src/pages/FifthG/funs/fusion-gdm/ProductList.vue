<template>

	<view class="background-white">
		<view id="headerGroupView">
			<!-- <action-bar title="套餐选择" leftIcon="back"></action-bar> -->
			<uni-search-bar placeholder=" 请输入套餐名称、套餐编号" @cancel="cancel" @confirm="search"></uni-search-bar>
			<v-radio-group class="border-bottom" :labelList="conditions" :width="240"
				@radioChangeType="onConditionsChange" />
		</view>
		<ProductCategory ref="productCategory"  :delta="delta" :height="scrollHeight" @categoryMainClick="categoryMainClick" @productClick="productClick">
		</ProductCategory>
	</view>

</template>

<script>
	import ProductCategory from "../../components/ProductCategory.vue"
	import vRadioGroup from "@/components/select/radio.vue"
  import {mapActions, mapGetters} from "vuex";

	export default {
		name:"productSelect",
		data() {
			return {
				conditions: [{
					name: "全部业务",
					code: "all",  // code: "all", 如何全部的话就是有"固移业务"出现，闲置根据客户需求改为只有“移动业务”就行了，后期在放出来 lzt 20220819
					value: "0",
					checked: true
				}, {
					name: "移动业务",
					value: "1",
					code: "MO",
				}
				/*, {
					name: "固移业务",
					value: "2",
					code: "MGD,GDM",
				}*/
				],
				scrollHeight: 0,
				delta:0,
        options: {}
			};
		},
		created() {
			//计算滑动区域的高度
			this.$nextTick(() => {
				this.calScrollHeight()
			})
			// this.subCategoryList = this.categoryList[0].subCategoryList;

			//this.onConditionsChange(0)
		},
		components: {
			ProductCategory,
			vRadioGroup
		},

		computed: {
      ...mapGetters({
        stepInfo: 'getStepInfo',
      })
    },

		onLoad(data) {
      this.options = data
      this.initSaveProcess()
			this.delta = data.delta
      console.log('onload',data)
		},

		methods: {
      ...mapActions({
        saveProcess: 'saveProcess',
      }),
			calScrollHeight() {
				let element = "#headerGroupView";
				let query = uni.createSelectorQuery().in(this);
				query.select(element).boundingClientRect(data => {
					const windowHeight = uni.getSystemInfoSync().windowHeight;
					this.scrollHeight = windowHeight - data.height;
				}).exec();
			},

			categoryMainClick(category) {
				this.subCategoryList = category.subCategoryList;
			},

			productClick(productItem) {
				this.$rauter.navigateTo({
					url: "ProductDetail",
					data: {
						product: productItem,
						delta : this.delta
					}
				});
			},

			search(e) {
				let searchVal = e.value;
				this.$refs.productCategory.searchProductList({
					key: "knowname",
					searchVal: searchVal
				})
			},

			cancel() {
				this.$refs.productCategory.resetProducts()
			},

			onConditionsChange(e) {
				let permarkSearch = this.conditions[e.value]
				this.$refs.productCategory.searchProductList({
					key: "pcode",
					searchVal: permarkSearch.code
				})
				this.$refs.productCategory.resetCategoryActive()
			},
      initSaveProcess() {
        console.log('onLoad========>saveProcess');
        let stepInfo = JSON.parse(JSON.stringify(this.stepInfo)),
          optionsCode = this.options?.code;
        let data = {
          poid: stepInfo.poid || this.$route.query.poid,
          cpcode: optionsCode ? optionsCode : '',
          temp: 0
        };
        let keepData = {
          data,
          unNeedNext: true
        };
        this.saveProcess(keepData);
      }
		}
	}
</script>

<style lang='scss'>

</style>
