<template>
	<view class="damages-detail-box">
		<view class="detail-info-box">
			<c-form :form-config="formConfig" :form-data="formData" label-width="36%" f-padding="0 40rpx">
				<template slot="form-items">
					<c-form-item label-width="36%" label="姓名" type="input" f-key="name" placeholder="请输入姓名"
						:show-border="true" :form="formData">
					</c-form-item>
						
					<c-form-item label-width="36%" label="身份证" type="input" f-key="certNo"
						placeholder="请输入身份证" :show-border="true" :form="formData">
					</c-form-item>
					
					<c-form-item label-width="36%" label="认证方式" type="picker_radio" range-value-key="value"
						range-display-key="certType" f-key="certType" :icon-flag="true" :show-border="true"
						:range="certTypeList" :form="formData">
					</c-form-item>
						
					<c-form-item v-if="formData.certType === '1'" label-width="36%" label="手机号码" type="input" f-key="phone"
						placeholder="请输入手机号码" :show-border="true" :form="formData">
					</c-form-item>
					
					<c-form-item label-width="36%" label="证件归属" type="picker_radio" range-value-key="value"
						range-display-key="infoOwner" f-key="infoOwner" :icon-flag="true" :show-border="true"
						:range="infoOwnerList" :form="formData">
					</c-form-item>
				</template>
			</c-form>
			<button class="next-btn" @click="authIdentifyH5">下一步</button>
		</view>
	</view>
</template>

<script>
	import {
		mapGetters,
		mapMutations,
		mapActions
	} from "vuex";
	import CForm from '@/pages/FifthG/components/form/CForm';
	import CNoData from "@/pages/FifthG/components/list/CNoData";
	import CLayout from "@/pages/FifthG/components/layout/CLayout";
	import CFormItem from "@/pages/FifthG/components/form/CFormItem";

	export default {
		data() {
			return {
				certTypeList: [{
					certType: '内嵌H5链接',
					value: '2'
				}, {
					certType: '短信认证',
					value: '1'
				}],
				infoOwnerList: [
					{
						infoOwner: '机主',
						value: '1'
					}
					// , {
					// 	infoOwner: '代/经办人',
					// 	value: '2'
					// }, {
					// 	infoOwner: '责任人',
					// 	value: '3'
					// }, {
					// 	infoOwner: '单位信息',
					// 	value: '4'
					// }
				],
				formConfig: [],
				formData: {
					phone: '',
					name: '',
					certNo: '',
					certType: '2',
					pageRedirectUrl: '',
					channelCode: '',
					city: '',
					operType: '1',
					infoOwner: '1',
					poid: ''
				}
			};
		},
		props: {
			options: {
				type: Object,
				default: {}
			}
		},
		components: {
			CLayout,
			CNoData,
			CForm,
			CFormItem
		},
		created() {
			console.log(this.custInfo)
			if (this.custInfo) {
				this.$set(this.formData, 'name', this.custInfo.custname)
				this.$set(this.formData, 'certNo', this.custInfo.cardno)
			}
		},

		computed: {
			...mapGetters({
				userInfo: 'getUserInfo',
				custInfo: 'getCustInfo',
				stepInfo: 'getStepInfo'
			})
		},

		methods: {
			...mapActions({
				saveProcess: 'saveProcess',
			}),
			authIdentifyH5() {
				if (!this.formData.name) {
					uni.showToast({
						title: '请输入实名认证人姓名！',
						icon: 'none'
					});
					return false;
				}
				if (!this.formData.certNo) {
					uni.showToast({
						title: '请输入实名认证人证件号码！',
						icon: 'none'
					});
					return false;
				}
				if (this.formData.certType === '1' && !this.formData.phone) {
					uni.showToast({
						title: '请输入实名认证人手机号码！',
						icon: 'none'
					});
					return false;
				}
				const poid = this.stepInfo.poid
				let accessToken = uni.getStorageSync('userToken') || (window.baseAppJs && window.baseAppJs.sendToken());
				if (this.formData.certType === '2') {
					// http://192.168.31.228:8080/#/pages/FifthG/funs/verifiedID
					const [href] = window.location.href.split('?')
					console.log('process.env.PAGE_REDIRECT_URL = ', href)
					this.$set(this.formData, 'pageRedirectUrl', href + '?code='+ this.options?.code +'&poid='+ poid +'&custid='+ this.custInfo.custid)
				} else {
					this.$set(this.formData, 'pageRedirectUrl', '')
				}
				
				if (this.formData.certType === '2' && !this.formData.pageRedirectUrl) {
					uni.showToast({
						title: '请配置实名认证回调路径！',
						icon: 'none'
					});
					return false;
				}
				
				this.$set(this.formData, 'poid', poid);
				this.$set(this.formData, 'city', this.custInfo.city);
				
				this.$http
					.post(this.$apis.authIdentifyH5, this.formData)
					.then(resp => {
						console.log(resp);
						if (resp && resp.responseBody) {
							const result = resp.responseBody
							const optionsCode = this.options?.code;
							const params = {
								temp: '0',
								poid: poid,
								data: '',
								cpcode: optionsCode ? optionsCode : '',
								afterSaveData: (resp) => {
									if (resp && resp.responseBody && resp.responseBody.msg === 'success') {
										if (this.formData.certType === '1') {
											// 跳转到查询认证结果页面
											this.$rauter.navigateTo({
												url: 'H5VerifiedResult'
											})
										} else {
											// 跳转到认证页面
											window.open(result.identifyUrl, '_self')
										}
									}
								}
							}
							const data = JSON.parse(JSON.stringify(this.formData))
							this.$set(data, 'temp', '0')
							this.$set(data, 'identifyUrl', result.identifyUrl)
							this.$set(data, 'orderid', result.orderid)
							this.$set(params, 'data', JSON.stringify(data))
							
							// 增加一证五号校验
							let check5gParams = {
								url: 'bizCheckRule5g',
								data: {
									querytype: '4', // QUERYTYPE=4，暂时只能是身份证
									queryvalue: this.formData.certNo, // 身份证号码
									name: this.formData.name, // 名字
                  certInfo: {
                    cardtype: '1',
                    personname: this.formData.name,
                    cardno: this.formData.certNo
                  }
								}
							};
							let keepData = {
								data: params,
								check5gParams,
								refreshCustInfo: true,
								custid: this.custInfo.custid
							}
							
							console.log(data)
							this.saveProcess(keepData)
							// this.saveData(params, result.identifyUrl)
						}
					})
					.catch(err => {
						uni.showToast({
							title: err.message,
							icon: 'none'
						});
					})
					.finally(arg => {});
			}
			// saveData(params, url) {
			// 	this.$http
			// 		.post(this.$apis.saveData, params)
			// 		.then(resp => {
			// 			console.log(resp);
			// 			if (resp && resp.responseBody && resp.responseBody.msg === 'success') {
			// 				if (this.formData.certType === '1') {
			// 					// 跳转到查询认证结果页面
			// 					this.$rauter.navigateTo({
			// 						url: 'H5VerifiedResult'
			// 					})
			// 				} else {
			// 					// 跳转到认证页面
			// 					// this.$rauter.navigateTo({
			// 					// 	url: 'H5VerifiedPage',
			// 					// 	data: {
			// 					// 		url: url
			// 					// 	}
			// 					// })
			// 					window.open(url, "_blank")
			// 				}
							
			// 			}
			// 		})
			// }
		}
	}
</script>

<style lang="scss" scoped>
	@import '@/pages/FifthG/components/OpenMsgItem.scss';

	/* 	/deep/ .border-bottom {
            border-bottom: 0;
        } */

	.button-class {
		margin: 40 rpx 40 rpx 0 40 rpx;
	}

	.title {
		/* min-width: 66px; */
		min-width: 20%;
		text-align: justify;
		text-align-last: justify;
	}

	.border-bottom {
		border-bottom: 1px #ebebeb solid;
	}

	.msg-item {
		padding: 30rpx 30rpx;
	}

	.next-btn {
		margin: 80rpx 30rpx;
		padding: 20rpx 0;
		background-color: rgb(0, 132, 255);
		color: #fff;
		// line-height: 45px;
	}
</style>
