<template>
  <view class="verifiedID" :style="`height: ${tabsLength == 2 ? verifiedHeight2 : verifiedHeight}`">
    <!-- <custom-navbar title="上传证件" @back="back">
    </custom-navbar> -->
    <!-- <action-bar style="width: 100%;" title="上传证件" leftIcon="back"></action-bar> -->
    <view class="verifiedID-main">
      <view class="uploadImg"><verified-upload ref="verifiedUpload" :data="verifiedData" :params="imgParams"
          @change="verifiedUploadChange" @getAliUrl="setAliUrl"></verified-upload></view>
      <view class="form"><verified-form ref="verifiedForm" :data="formData" :params="formParams"
          @change="verifiedFormChange"></verified-form></view>
    </view>

    <live-detection v-if="aliUrl" :url="aliUrl"></live-detection>

    <view class="verifiedID-btn">
      <view class="btn" v-if="verifiedButton == '读取身份证'" @click="readIDCard()">读取身份证</view>
      <view class="btn" @click="readID()">识别身份证</view>
      <view class="btn" @click="verifiedIDNext()">下一步</view>
    </view>
  </view>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import customNavbar from '@/components/customNavbar/customNavbar.vue';
import verifiedForm from '@/components/FifthG/verifiedForm';
import verifiedUpload from '@/components/FifthG/verifiedUpload';
import launchMiniprogram from '@/sdk/eWechat/launchMiniprogram';
import { base64ToBlob } from '@/common/js/drawWaterMarkUtils.js'
import CryptoJS from 'crypto-js';

import { idType } from '@/store/modules/sysparam.js';
import LiveDetection from "../faceDetect/liveDetection.vue";

export default {
  name: 'VerifiedID',
  mixins: [launchMiniprogram],
  data() {
    return {
			verifiedHeight: 'calc(100vh - 0) !important',
			verifiedHeight2: 'calc(100vh - 55px) !important',
      verifiedButton: '读取身份证',
      params: {
        ali: '',
        front: '',
        back: '',
        avatar: '',
        idperson: '',
        name: '',
        certificate: '',
        license: '',
        ownertype: '',
        validity: '',
        idenaddress: '',
        nationaltypename: '',
        gender: ''
      },
      imgParams: {
        front: '',
        back: '',
        avatar: ''
        // idperson: '',
      },
      formParams: {
        name: '',
        certificate: '',
        license: '',
        ownertype: '',
        validity: '',
        idenaddress: '',
        nationaltypename: '',
        gender: '',
        idperson: '',
        devdn: '',
        vendorName: ''
      },
      uploadRes: {},
      uploadStatus: false,
      formData: [
        {
          value: '',
          label: '姓名',
          field: 'name',
          tag: 'input',
          type: '',
          placeholder: '请输入姓名'
        },
        {
          value: '',
          label: '地址',
          field: 'idenaddress',
          tag: 'input',
          type: 'textarea',
          placeholder: '请输入身份证上的地址'
        },
        {
          value: '',
          label: '民族',
          field: 'nationaltypename',
          tag: 'input',
          type: '',
          placeholder: '请输入民族'
        },
        {
          value: '',
          label: '性别',
          field: 'gender',
          tag: 'picker',
          index: 0,
          range: ['男', '女']
        },
        {
          value: '',
          label: '证件类型',
          field: 'certificate',
          tag: 'picker',
          index: 0,
          range: ['身份证','外国人永久居留证']
        },
        {
          value: '',
          label: '证件号',
          field: 'license',
          tag: 'input',
          type: '',
          placeholder: '请输入证件号'
        },
        {
          value: '',
          label: '证件归属',
          field: 'ownertype',
          tag: 'picker',
          index: 0,
          rangeKey: 'mname',
          abled: true,
          range: [
            {
              mcode: 1,
              mname: '机主'
            },
            {
              mcode: 2,
              mname: '代/经办人'
            },
            {
              mcode: 3,
              mname: '责任人'
            },
            {
              mcode: 4,
              mname: '单位信息'
            }
          ]
        },
        {
          value: '',
          label: '有效期',
          field: 'validity',
          tag: 'date',
          type: '',
          placeholder: '请选择有效期'
        }
      ],
      verifiedData: [
        {
          title: '上传身份证照片',
          tips: '（国徽面为正面，头像面为反面）',
          list: [
            {
              value: '',
              field: 'front',
              desc: '国徽面',
              defaultImg: require('@/static/fifthg/imgs/img_beimian.png')
            },
            {
              value: '',
              field: 'back',
              desc: '人像面',
              defaultImg: require('@/static/fifthg/imgs/img_zhengmian.png')
            }
          ]
        },
        {
          title: '办理人照片',
          list: [
            {
              value: '',
              field: 'avatar',
              desc: '现场头像照',
              defaultImg: ''
            }
            // {
            // 	value: '',
            // 	field: 'idperson',
            // 	desc: '身份证头像照',
            // 	defaultImg: '',
            // }
          ]
        }
      ],
      cardTypeData: [],
      incomeFileData: null,
      aliUrl: ''
    };
  },
  components: {
    customNavbar,
    verifiedForm,
    verifiedUpload,
    LiveDetection
  },
  computed: {
    ...mapGetters({
      custInfo: 'getCustInfo',
      userInfo: 'getUserInfo',
      stepInfo: 'getStepInfo',
      getDatas: 'getDatas'
    }),
    iframe() {
      return uni.getStorageSync('iframe') || '';
    }
  },
  watch: {
    stepInfo: {
      handler(newVal, oldVal) {
        // this.echoInfo()
      },
      immediate: false,
      deep: true
    }
  },
  props: {
    options: {
      type: Object,
      default: {}
    },
    tabsLength: {
      type: Number,
      default: 1
    }
  },
  async created() {
    console.log('onload------------------')
    await this.InitDigital([idType]);
    this.InitData();
    this.refreshOrderId(); // 刷新orderId
    await this.agentConfig(); // 唤起小程序基础配置

    this.$bus.$on('incomeFileData', (data) => {
      this.incomeFileData = data
      console.log('ordinaryVerified====>this.incomeFileData===>', this.incomeFileData)
    })
  },
  onShow() {
    // this.echoInfo()
  },
  onUnload() {
    if (window.baseAppJs) {
      //清除图片缓存
      window.baseAppJs.clearImageCache();
    }
  },
  methods: {
    ...mapActions({
      InitDigital: 'init',
      saveProcess: 'saveProcess',
      goNextPage: 'goNextPage',
      queProcess: 'queProcess'
    }),

    readIDCard() {
      this.invokeIDCardReader().then(() => {
        console.info('idCardReader success');
      });
    },

    decryptlLegalImg(word) {
      const key = CryptoJS.enc.Utf8.parse(process.env.LEGALIMG_SECRET_KEY),
        iv = CryptoJS.enc.Utf8.parse(process.env.LEGALIMG_OFFSET);
      let decrypt = CryptoJS.AES.decrypt(word, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });
      let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
      return decryptedStr.toString();
    },
    readID() {
      if (process.env.RUN_ENV == 'local') {
        this.readIDCardDataTest()
      } else {
        this.readIDCardData()
      }
    },
    // 测试
    readIDCardDataTest() {
      const ID_RESULT = {address: "湖北省红安县觅儿寺镇杨家田村1组", birthday: "20010408", devdn: "SR720211105030", dn: "0001484201130C03000000000000147A", effectDate: "20200511", expireDate: "20300511", gender: "男", genderCode: "1", idNum: "******************", issueOrg: "红安县公安局", name: "活体检测", nation: "汉", nationCode: "01", photo: "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", vendorName: ""}
            // const ID_RESULT = {"birthday":"19800101","englishName":"ZHENGJIAN, YANGBEN","effectDate":"20230808","address":"","idType":"Y","gender":"女","nation":"巴基斯坦","prevForeignIdAssoc":"","photo":"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","dn":"0001424A03170A17000000000005D401","idCardRepNum":"00","genderCode":"0","name":"ZHENGJIAN, YANGBEN","chineseName":"无中文姓名","expireDate":"20330807","idNum":"931586198001010028","nationCode":"PAK","oldIdNum":""}
      // 测试身份证信息
      var base64Img = "data:image/jpg;base64," + ID_RESULT.photo;
      this.formParams.idperson = base64Img
      this.formParams.name = ID_RESULT.name
      this.formParams.license = ID_RESULT.idNum
      this.formParams.idenaddress = ID_RESULT.address
      this.formParams.nationaltypename = ID_RESULT.nation
      this.formParams.gender = ID_RESULT.gender
      this.formParams.birthdate = ID_RESULT.birthday
      this.formData[3].index = this.formParams.gender == "男" ? 0 : 1;

      this.formParams.devdn = 'SR830220400199' //序列号
      this.formParams.vendorName = '广州磐信计算机科技有限公司' //厂家名字

      this.formParams.encustnameext = ID_RESULT.englishName
      this.formParams.chcustname = ID_RESULT.chineseName
      this.formParams.chgidnum = ID_RESULT.idCardRepNum
      this.formParams.idrelitem = ID_RESULT.prevForeignIdAssoc

      this.formParams.orderId = 1
      this.formParams.basisTime = '2999-12-31'

      let formatSDate = ID_RESULT.effectDate
      formatSDate = formatSDate.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3")//result.effectDate.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3")
      let formatEDate = ID_RESULT.expireDate
      if (formatEDate == "长期") {
        formatEDate = "2999-12-31"
      } else {
        formatEDate = formatEDate.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3")
      }

      this.formParams.validity = formatSDate + "至" + formatEDate
      console.log("临时值this.formParams====" + JSON.stringify(this.formParams))

      let paramsCopy = Object.assign({}, this.params, this.formParams)
      this.params = JSON.parse(JSON.stringify(paramsCopy))
      this.formParams = JSON.parse(JSON.stringify(this.formParams))
      console.log("临时值lztparams====" + JSON.stringify(this.params))
      console.log("临时值lztformParams====" + JSON.stringify(this.formParams))

      this.$refs.verifiedUpload.setFormParams(this.formParams)
    },
    // 识别身份证或是SIM卡信息  身份证：1，SIM卡：2
    readIDCardData() {
      let param = {
        orderId: this.orderId,
        //时间转换为年月日
        basisTime: this.$util.dataProcess.formatDate(new Date(), 'yyyy/MM/dd hh:mm:ss')
      };
      console.info('readIDCardData url: ', this.$apis.queIdCardInfo);
      console.info('readIDCardData params: ', JSON.stringify(param));
      this.$http
        .post(this.$apis.queIdCardInfo, param)
        .then(response => {
          let res = response.responseBody;
          if (res && res.data) {
            var datas = res.data;
            let isNewForeginId = ['I', 'Y'].includes(datas.idCardInfo.cardType)
            console.info('readIDCardData result:', JSON.stringify(res));
            var photo = this.decryptlLegalImg(datas.legalImg.replaceAll(' ', '')); // 身份证图片串
            let base64Img = 'data:image/jpg;base64,' + photo;

            this.formParams.orderId = param.orderId
            this.formParams.basisTime = param.basisTime

            this.formParams.isNewForeginId = isNewForeginId ? "Y" : "N"
            this.formParams.idperson = base64Img;
            this.formParams.name = datas.idCardInfo.name;
            this.formParams.license = datas.idCardInfo.cardId;
            this.formParams.idenaddress = datas.idCardInfo.address;
            this.formParams.nationaltypename = datas.idCardInfo.nation;
            this.formParams.gender = datas.idCardInfo.sex;
            this.formParams.birthdate = datas.idCardInfo.birthday
            this.formData[3].index = this.formParams.gender == '男' ? 0 : 1;
            if(isNewForeginId) {
              this.formData[4].range.forEach((n, i) => {
                if(n == '外国人永久居留证') {
                  this.formData[4].index = i
                  this.formParams.certificate = n
                }
              })
            }

            this.formParams.encustnameext = datas.idCardInfo.name
            this.formParams.chcustname = datas.idCardInfo.chCustName
            this.formParams.chgidnum = datas.idCardInfo.idCardRepNum
            this.formParams.idrelitem = datas.idCardInfo.oldIdNun

            this.formParams.devdn = datas.idCardInfo.deviceId; //序列号
            let formatSDate = datas.idCardInfo.validStart;
            formatSDate = formatSDate.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3'); //result.effectDate.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3")
            let formatEDate = datas.idCardInfo.validEnd;
            if (formatEDate == '长期') {
              formatEDate = '2999-12-31';
            } else {
              formatEDate = formatEDate.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3');
            }

            this.formParams.validity = formatSDate + '至' + formatEDate;
            console.log('临时值this.formParams====' + JSON.stringify(this.formParams));

            let paramsCopy = Object.assign({}, this.params, this.formParams);
            this.params = JSON.parse(JSON.stringify(paramsCopy));
            this.formParams = JSON.parse(JSON.stringify(this.formParams));
            console.log('临时值lztparams====' + JSON.stringify(this.params));
            console.log('临时值lztformParams====' + JSON.stringify(this.formParams));

            this.$refs.verifiedUpload.setFormParams(this.formParams)
          } else {
          }
        })
        .catch(err => {
          console.error('readIDCardData error:', err);
        });
    },

    InitData() {
      // console.log('stepInfo');
      // console.log(this.custInfo);
      // console.log(this.userInfo);
      // console.log(this.stepInfo);
      let list = this.getDatas(idType) || [];
      console.log(list);
      if (list && list.length) {
        this.cardTypeData = [...list];
        let range = [];
        list.forEach(val => {
          range.push(val.mname);
        });
        this.formData[4].range = range;
      }
      // this.getProcess()
      this.echoInfo();
    },

    getProcess() {
      let that = this,
        stepInfo = JSON.parse(JSON.stringify(this.stepInfo));
      if (stepInfo && stepInfo.poid) {
        this.queProcess({
          poid: stepInfo.poid
        });
      }
    },

    echoInfo() {
      console.log('echoInfo')
      console.log(this.stepInfo)
      let that = this,
        stepInfo = JSON.parse(JSON.stringify(this.stepInfo)),
        formParams = JSON.parse(JSON.stringify(this.formParams)),
        params = JSON.parse(JSON.stringify(this.params)),
        code = this.options?.code;
      if (stepInfo && stepInfo.custid) {
        let stepObj = {};
        if (code && stepInfo.data[code]) {
          stepObj = stepInfo.data[code];
        } else if (stepInfo.process && stepInfo.process.length && stepInfo.process[1].data) {
          stepObj = stepInfo.process[1].data;
        }
        if (stepObj && Object.keys(stepObj).length) {
          let { avatarUrl, backUrl, frontUrl, idpersonUrl, aliUrl } = stepObj;
          let { name, license, ownertype, validity, nationaltypename, idenaddress, gender, certificate } = stepObj;
          formParams = {
            name: name,
            license: license,
            ownertype: ownertype,
            validity: validity,
            idenaddress: idenaddress,
            nationaltypename: nationaltypename,
            gender: gender,
            certificate: certificate,

          };
          let findIndexGender = this.formData[3].range.findIndex(val => val == gender);
          this.formData[3].index = findIndexGender != -1 ? findIndexGender : 0;
          if (certificate) {
            let findIndexCertifcate = this.formData[4].range.findIndex(val => val == gender);
            this.formData[4].index = findIndexCertifcate != -1 ? findIndexCertifcate : 0;
          } else {
            formParams.certificate = this.formData[4].range[0];
          }
          if (ownertype && ownertype.mname) {
            let findIndexOrgan = this.formData[6].range.findIndex(val => val.mname == ownertype.mname);
            this.formData[6].index = findIndexOrgan != -1 ? findIndexOrgan : 0;
          } else {
            formParams.ownertype = this.formData[6].range[0];
          }
          let paramsCopy = Object.assign({}, params, formParams);
          this.formParams = JSON.parse(JSON.stringify(formParams));
          this.params = JSON.parse(JSON.stringify(paramsCopy));
          if (avatarUrl) {
            this.verifiedImgRead(avatarUrl, 'avatar', avatarUrl.substring(avatarUrl.lastIndexOf('.') + 1).toLowerCase());
            this.params.avatar = avatarUrl;
          }
          if (backUrl) {
            this.verifiedImgRead(backUrl, 'back', backUrl.substring(backUrl.lastIndexOf('.') + 1).toLowerCase());
            this.params.back = backUrl;
          }
          if (frontUrl) {
            this.verifiedImgRead(frontUrl, 'front', frontUrl.substring(frontUrl.lastIndexOf('.') + 1).toLowerCase());
            this.params.front = frontUrl;
          }
          if (idpersonUrl) {
            this.verifiedImgRead(idpersonUrl, 'idperson', idpersonUrl.substring(idpersonUrl.lastIndexOf('.') + 1).toLowerCase());
            this.params.idperson = idpersonUrl;
          }
          if (aliUrl) {
            this.params.ali = aliUrl;
          }

          if ((avatarUrl || aliUrl) && backUrl && frontUrl) {
            this.uploadStatus = true;
            this.uploadRes.avatarUrl = avatarUrl;
            this.uploadRes.frontUrl = frontUrl;
            this.uploadRes.backUrl = backUrl;
            this.uploadRes.idpersonUrl = idpersonUrl;
            this.uploadRes.aliUrl = aliUrl;
          }
        }
      }
    },
    echoImg() {
      let that = this,
        stepInfo = JSON.parse(JSON.stringify(this.stepInfo));
    },
    arrDeep(arr) {
      let list = [];
      if (arr && arr.length) {
        for (let i = 0; i < arr.length; i++) {
          list.push(JSON.parse(JSON.stringify(arr[i])));
        }
      }
      return list;
    },
    back() {
      window.baseAppJs.back();
    },
    verifiedUploadChange(obj, type, field) {
      console.log('verifiedUploadChange', obj)
      // 设置图片
      this.uploadRes[obj.name + 'Url'] = obj.url
    },
    verifiedFormChange(obj, type, field) {
      let params = JSON.parse(JSON.stringify(this.params));
      let formParams = JSON.parse(JSON.stringify(this.formParams));
      let paramsCopy = Object.assign({}, params, obj);
      this.params = JSON.parse(JSON.stringify(paramsCopy));
      this.formParams = JSON.parse(JSON.stringify(obj));
    },
    uploadImg(files) {
      let params = JSON.parse(JSON.stringify(this.params)),
        that = this
      let url = process.env.BASE_URL + process.env.UPLOAD
      let AccessToken = uni.getStorageSync('userToken') || (window.android && window.android.sendToken())
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: url,
          files: files,
          header: {
            'AccessToken': AccessToken
          },
          success(uploadFileRes) {
            if (uploadFileRes && uploadFileRes.data) {
              let data = JSON.parse(uploadFileRes.data)
              if (data.responseBody && Object.keys(data.responseBody).length) {
                that.uploadRes = data.responseBody
                that.uploadStatus = true
                resolve(data)
              }
            }
          },
          fail(err) {
            uni.showToast({
              title: err.errMsg,
              icon: 'none'
            })
            uni.hideLoading()
            reject(err)
          }
        })
      })
    },
    async verifiedIDNext() {
      let timer = setTimeout(() => {
        clearTimeout(timer);
        let params = { ...JSON.parse(JSON.stringify(this.params)), ...this.uploadRes },
          that = this,
          url = process.env.BASE_URL + process.env.UPLOAD,
          AccessToken = uni.getStorageSync('userToken') || (window.baseAppJs && window.baseAppJs.sendToken());
        if (!params.frontUrl) {
          uni.showToast({
            title: '请先上传身份证正面照',
            icon: 'none'
          });
        } else if (!params.backUrl) {
          uni.showToast({
            title: '请先上传身份证反面照',
            icon: 'none'
          });
        } else if (!params.avatarUrl && !params.aliUrl) {
          uni.showToast({
            title: '请先上传办理人人像照或活体检测',
            icon: 'none'
          });
        } else if (!params.name) {
          uni.showToast({
            title: '请输入姓名',
            icon: 'none'
          });
        } else if (!params.license) {
          uni.showToast({
            title: '请输入证件号',
            icon: 'none'
          });
          // } else if (!params.ownertype) {
          // 	uni.showToast({
          // 		title: '请输入签发机关',
          // 		icon: 'none'
          // 	})
        }
        // lzt 临时注释
        else if (!params.validity) {
          uni.showToast({
            title: '请选择有效期',
            icon: 'none'
          });
        } else {
          // 校验身份证有效期
          const validityMatch = params.validity.match(/\d{4}([\-])\d{1,2}\1\d{1,2}/g);
          if (validityMatch && validityMatch.length >= 2) {
            const expiryDate = validityMatch[1]; // 有效期结束日期
            const currentDate = this.$util.dataProcess.formatDate(new Date(), 'yyyy-MM-dd');

            // 比较当前日期与有效期结束日期
            const isValid = this.dateCompare(currentDate, expiryDate);
            console.log('ordinaryVerified当前日期：', currentDate);
            console.log('ordinaryVerified有效期结束日期：', expiryDate);
            console.log('ordinaryVerified是否有效：', isValid);

            if (!isValid) {
              // 有效期已过期
              uni.showToast({
                title: '身份证有效期已过期，请更换有效证件重新读取',
                icon: 'none',
                duration: 3000
              });

              // 清除身份证照片
              this.clearIdCardPhotos();
              return;
            }
          }

          if (that.custInfo.custname !== params.name) {
            uni.showModal({
              title: '温馨提示',
              content: '当前实名认证姓名【' + params.name + '】和客户姓名【' + that.custInfo.custname + '】不一致，请仔细检查证件信息和当前客户信息，是否确认继续？',
              success(res) {
                if (res.confirm) {
                  that.saveDataPro()
                }
              }
            })
          } else if (that.custInfo.cardno !== params.license) {
            uni.showModal({
              title: '温馨提示',
              content: '当前实名认证的证件号【' + params.license + '】和客户证件号【' + that.custInfo.cardno + '】不一致，请仔细检查证件信息和当前客户信息，是否确认继续？',
              success(res) {
                if (res.confirm) {
                  that.saveDataPro()
                }
              }
            })
          } else {
            that.saveDataPro()
          }
        }
      }, 100);
    },

    saveDataPro() {
      let params = JSON.parse(JSON.stringify(this.params)),
        that = this,
        url = process.env.BASE_URL + process.env.UPLOAD,
        AccessToken = uni.getStorageSync('userToken') || (window.baseAppJs && window.baseAppJs.sendToken());
      that.$http.post(that.$apis.callBoss, {
        service: that.$serviceId.CHECK_FGB_LIMITCONDITION,
        params: {
          idenaddress: that.formParams.idenaddress,
          cardno: that.formParams.license
        }
      }).then(resp => {
        if (resp.responseBody.data.status == '0') {
          const limitlevel = resp.responseBody.data.output.limitlevel
          const limitmsg = resp.responseBody.data.output.limitmsg
          if (limitlevel === '1') {
            uni.showModal({
              title: '温馨提示',
              content: limitmsg,
              success(res) {
                if (res.confirm) {
                  that.saveData();
                }
              }
            })
          } else if (limitlevel === '2') {
            uni.showModal({
              title: '温馨提示',
              content: limitmsg
            })
          } else {
            uni.showModal({
              title: '提示',
              content: '是否确定保存当前流程',
              async success(res) {
                if (res.confirm) {
                  that.saveData();
                }
              }
            });
          }
        }
      })
    },

    async saveData() {
      // 上传芯片照
      const idPersonUrl = await this.uploadIDPerson()
      console.log('idPersonUrl=====>',idPersonUrl)
      if (!idPersonUrl) {
        uni.showToast({
          title: '芯片照上传失败，请重新提交！',
          icon: 'none'
        });
        return
      }

      let params = JSON.parse(JSON.stringify(this.params)),
        stepInfo = JSON.parse(JSON.stringify(this.stepInfo)),
        that = this,
        formParams = JSON.parse(JSON.stringify(this.formParams)),
        optionsCode = this.options?.code;
      if (!formParams.gender) {
        formParams.gender = this.formData[3].range[0];
      }
      if (!formParams.certificate) {
        formParams.certificate = this.formData[4].range[0].mname;
      }
      if (!formParams.ownertype) {
        formParams.ownertype = this.formData[6].range[0];
      }

      let incomeFiles = {}
      if (this.incomeFileData) {
        if (this.incomeFileData.incomeFile1) {
          incomeFiles.incomeUrl = this.incomeFileData.incomeFile1.incomeId
          incomeFiles.incomePath = this.incomeFileData.incomeFile1.incomeUrl
        }

        if (this.incomeFileData.incomeFile2) {
          incomeFiles.incomeUrl2 = this.incomeFileData.incomeFile2.incomeId
          incomeFiles.incomePath2 = this.incomeFileData.incomeFile2.incomeUrl
        }

        if (this.incomeFileData.incomeFile3) {
          incomeFiles.incomeUrl3 = this.incomeFileData.incomeFile3.incomeId
          incomeFiles.incomePath3 = this.incomeFileData.incomeFile3.incomeUrl
        }
      }

      let saveData = {
        ...formParams,
        ...that.uploadRes,
        desens: formParams.license.replace(/(\d{6})(\d{8})(\d{4})/, "$1********$3"),
        ...incomeFiles
      };
      console.log('打印saveData');
      console.log(saveData);
      console.log('saveData',saveData);

      if (stepInfo && stepInfo.poid) {
        let data = {
          poid: stepInfo.poid,
          // cpcode: stepInfo.process[1].child && stepInfo.process[1].child.length && stepInfo.process[1].child[0].code ? stepInfo.process[1].child[0].code : stepInfo.process[1].code,
          cpcode: optionsCode ? optionsCode : '',
          data: JSON.stringify(saveData),
          temp: 1
        };
        let find = {}
        if (this.cardTypeData && this.cardTypeData.length) {
          find = this.cardTypeData.find(val => val.mname == formParams.certificate);
        }

        let aliObj = {}
        if (that.uploadRes.aliUrl) {
          aliObj = JSON.parse(that.uploadRes.aliUrl)
        }

        // /\d{4}([.\-/ ])\d{2}\1\d{2}/g
        let postParams = {
          url: 'm5gRealNameAuth',
          data: {
            custid: that.custInfo && that.custInfo.custid ? that.custInfo.custid : '', //lzt临时注释
            // certtype: that.custInfo && that.custInfo.certtype ? that.custInfo.certtype : '0',
            cardtype: '1',
            cardno: formParams.license,
            personname: formParams.name,
            ideneffdate: formParams.validity.match(/\d{4}([\-])\d{1,2}\1\d{1,2}/g)[0],
            idenexpdate: formParams.validity.match(/\d{4}([\-])\d{1,2}\1\d{1,2}/g)[1] || formParams.validity.match(/\d{4}([\-])\d{1,2}\1\d{1,2}/g)[0],
            gender: formParams.gender,
            idenaddress: formParams.idenaddress,
            nationaltypename: formParams.nationaltypename,
            ownertype: formParams.ownertype.mcode,
            picurl2: that.uploadRes.avatar2Url, //、lzt临时注释
            picurl: that.uploadRes.avatarUrl, //、lzt临时注释
            frontUrl: that.uploadRes.frontUrl, //lzt临时注释
            backUrl: that.uploadRes.backUrl, //lzt临时注释
            idpicImgStr: formParams.idperson,
            devdn: formParams.devdn, //序列号
            vendorName: formParams.vendorName, //厂家名字
            cardtype: find ? find.mcode : '1',
            countryofbirth: formParams.nationaltypename,
            birthdate: formParams.birthdate.replace(/\./g, '').replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3'),

            encustnameext: formParams.encustnameext,
            chcustname: formParams.chcustname,
            chgidnum: formParams.chgidnum,
            idrelitem: formParams.idrelitem,
            isNewForeigners: formParams.isNewForeginId,

            orderId: formParams.orderId,
            basisTime: formParams.basisTime,
            idPersonUrl: idPersonUrl,
            certify_id: aliObj.certify_id,
            liveness_biz_no: aliObj.liveness_biz_no,
            ...incomeFiles
          }
        };

        // 增加一证五号校验
        let check5gParams = {
          url: 'bizCheckRule5g',
          data: {
            querytype: '4', // QUERYTYPE=4，暂时只能是身份证
            queryvalue: formParams.license, // 身份证号码
            name: formParams.name, // 名字
            certInfo: {
              cardtype: find ? find.mcode : '1',
              personname: formParams.name,
              cardno: formParams.license
            }
          }
        };

        console.info('postParams:', postParams);

        // if (this.cardTypeData && this.cardTypeData.length && this.cardTypeData[formParams.certificate]) {
        //   postParams.data.cardtype = this.cardTypeData[formParams.certificate].mcode
        // }
        //
        let keepData = {
          data,
          postParams,
          check5gParams,
          refreshCustInfo: true,
          custid: this.custInfo.custid
        };
        console.log(keepData);
        console.log(params);
        that.$emit('result', keepData)
        // that.saveProcess(keepData);
      } else {
        uni.showToast({
          title: '暂无此套餐信息，请重新选择套餐',
          icon: 'none'
        });
        let timers = setTimeout(() => {
          clearTimeout(timers);
          uni.hideToast();
          that.$rauter.navigateTo({
            url: 'locationCustFifthG'
          });
        }, 1000);
      }
    },

    verifiedImgRead(path, key, pattern) {
      let uploadRes = JSON.parse(JSON.stringify(this.uploadRes));
      pattern = pattern || 'png';
      this.$http
        .post(this.$apis.m5gPhotoRead, {
          photoPath: path
        })
        .then(response => {
          let res = response.responseBody;
          if (res && res.photoBase64) {
            let src = res.photoBase64;
            this.imgParams[key] = src;
            this.verifiedData.map(val => {
              val.list.map(item => {
                if (item.field == key) {
                  item.value = src;
                }
                return item;
              });
              return val;
            });
          }
        })
        .catch(err => { })
        .finally(arg => { });
    },
    async uploadIDPerson(attempt = 0) {
      if (!this.formParams.idperson) {
        return;
      }

      let fileInfo = {
        uploadUrl: '/gridboot5g/realnameauth/uploadidperson',
        name: 'idperson'
      };

      let blob = await base64ToBlob(this.formParams.idperson);
      const cutNewFile = new File([blob], fileInfo.name, {type: 'image/jpeg'});
      const uri = window.URL.createObjectURL(cutNewFile);

      // 获取上传链接
      const uploadUrl = process.env.BASE_URL + fileInfo.uploadUrl;
      // 获取token
      let accessToken = uni.getStorageSync('userToken') || (window.android && window.android.sendToken());

      try {
        // 上传图片
        const task = await new Promise((resolve, reject) => {
          uni.uploadFile({
            url: uploadUrl,
            name: fileInfo.name,
            filePath: uri,
            header: {
              'AccessToken': accessToken
            },
            timeout: 30000, // 30秒没有响应，自动关闭
            success: (res) => {
              console.log('idperson', res);
              resolve(res);
            },
            fail: (err) => {
              console.log(err);
              reject(err);
            },
            complete: (res) => {
              console.log(res);
              uni.hideLoading();
            }
          });
        });

        console.log('task=====>', task);

        if (!task.data) {
          throw new Error('idPersonUrl上传失败');
        }

        const data = JSON.parse(task.data);
        if (data.responseBody && data.responseBody.idPersonUrl) {
          return data.responseBody.idPersonUrl;
        } else {
          throw new Error('idPersonUrl上传失败');
        }
      } catch (e) {
        console.log(e);
        if (attempt > 5) {
          console.log('重传失败5次，上传终止');
          return null;
        }

        console.log(`重传次数 ${attempt}...`);
        await this.uploadIDPerson(attempt + 1);
      }
    },
    setAliUrl(data) {
      const vm = this
      console.log('setAliUrl====>', data)
      vm.aliUrl = data
    },

    /**
     * 比较时间大小
     * @param {String} startDate 开始日期
     * @param {String} endDate 结束日期
     * @returns {Boolean} 如果startDate <= endDate返回true，否则返回false
     */
    dateCompare(startDate, endDate) {
      // 计算截止时间
      startDate = new Date(startDate.replace('-', '/').replace('-', '/'))
      // 计算详细项的截止时间
      endDate = new Date(endDate.replace('-', '/').replace('-', '/'))
      if (startDate <= endDate) {
        return true
      } else {
        return false
      }
    },

    /**
     * 清除身份证照片
     */
    clearIdCardPhotos() {
      // 清除身份证正反面照片
      this.imgParams.front = '';
      this.imgParams.back = '';

      // 清除上传结果中的照片URL
      this.uploadRes.frontUrl = '';
      this.uploadRes.backUrl = '';

      // 重置verifiedData中的照片值
      this.verifiedData.forEach(section => {
        section.list.forEach(item => {
          if (item.field === 'front' || item.field === 'back') {
            item.value = '';
          }
        });
      });

      // 通知上传组件更新并清除图片显示
      if (this.$refs.verifiedUpload) {
        this.$refs.verifiedUpload.setFormParams(this.formParams);
        // 调用上传组件的删除方法
        this.$refs.verifiedUpload.onDeleteImg('frontImg');
        this.$refs.verifiedUpload.onDeleteImg('backImg');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.verifiedID {
  box-sizing: border-box;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
  padding: 0rpx 0rpx 0rpx;
  height: calc(100vh - var(--window-top) - var(--window-bottom));
  // height: calc(100vh - 105px);
  overflow: hidden;

  view {
    box-sizing: border-box;
    /* padding: 0; */
    margin: 0;
  }

  image {
    width: 100%;
    height: 100%;
  }

  &-main {
    width: 100%;
    flex: 1;
    overflow: hidden;
    overflow-y: auto;
    padding: 30rpx 40rpx;
  }

  &-btn {
    width: 100%;
    padding: 20rpx 0rpx;
    text-align: center;
    box-sizing: border-box;
    font-size: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;

    .btn {
      width: 30%;
      display: inline-block;
      overflow: hidden;
      color: #fff;
      font-size: 32rpx;
      background-color: #1f77ff;
      line-height: 63rpx;
      height: 80rpx;
      border-radius: 8rpx;
      padding: 8rpx;
      text-align: center;

      &:not(:first-child) {
        margin-left: 20rpx;
      }
    }
  }
}

.uploadImg {
  //background-color: red;
  position: relative;
}
</style>
