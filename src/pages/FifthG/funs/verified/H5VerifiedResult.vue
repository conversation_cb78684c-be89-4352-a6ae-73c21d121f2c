<template>
	<c-layout title="认证结果查询">
		<view class="c-no-data-box" :style="{ marginTop: marginTop}">
			<view class="c-no-data-icon">
				<i class="iconfont icon-wushuju" :style="{fontSize: iconSize}"></i>
			</view>
			<view class="c-no-data-title" :style="{marginTop: '1.5em',fontSize: titleSize, padding: '0 20px'}">
				认证短信已发送到手机，请注意查收短信；<br>
				实名认证完成后请点击下方按钮；<br>
				继续后续操作
			</view>
			<button class="next-btn" @click="qryH5AuthResult">我已认证完成</button>
		</view>
	</c-layout>
</template>

<script>
	import CTabs from "@/pages/FifthG/components/list/CTabs";
	import CLayout from "@/pages/FifthG/components/layout/CLayout";
	import { mapState, mapGetters, mapActions } from 'vuex';
	export default {
		data() {
			return {
				iconSize: "4.6em",
				marginTop: "30%",
				titleSize: "1.2em"
			};
		},
		components: {
			CTabs,
			CLayout
		},
		computed: {
			...mapGetters({
				custInfo: 'getCustInfo',
				userInfo: 'getUserInfo',
				stepInfo: 'getStepInfo'
			})
		},
		async onLoad() {
			const poid =this.stepInfo.poid
			if (poid) {
				await this.queProcess({
					poid: poid
				})
			}
		},

		methods: {
			...mapActions({
				saveProcess: 'saveProcess',
				queProcess: 'queProcess'
			}),
			// 查询实认证结果
			qryH5AuthResult() {
				const poid =this.stepInfo.poid
				if (!poid) {
					return
				}
				const vm = this
				let orderid = ''
				let step = this.stepInfo.data
				if(!step){
					return
				}
				orderid = step.IDCONF.orderid
				vm.$http.post(vm.$apis.qryH5AuthResult, {
					orderid: orderid,
					poid: poid
				}).then(resp => {
				  console.log(resp)
					if (resp && resp.responseBody) {
						if (resp.responseBody.code == '0') {
							// 已实名认证
							this.saveData(poid)
						} else {
							let content = ''
							if (resp.responseBody.code == '-1') {
								content = '未查询到实名认证结果，请确认已通过短信中的实名认证连接进行了实名认证'
							} else {
								content = '实名认证结果已核销！'
							}
							uni.showModal({
								title: '温馨提示',
								content: content,
								showCancel: false, //没有取消按钮的弹框
								buttonText: '确定',
								success(res) {
									if (res.confirm) {
									}
								}
							});
						}
					}
				})
			},
			saveData(poid) {
				let step = this.stepInfo.data
				if(!step){
					return
				}
				const idconfig = step.IDCONF
				this.$set(idconfig, 'identifyStatus', '0')
				const data = {
					poid: poid,
					temp: '1',
					cpcode: 'IDCONF',
					data: JSON.stringify(idconfig)
				}
				let keepData = {
					data,
					refreshCustInfo: true,
					custid: this.custInfo.custid
				};
				this.saveProcess(keepData);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.c-no-data-box {
	  width: 100%;
	  text-align: center;
	}
	.next-btn {
		margin: 80rpx 30rpx;
		background-color: rgb(0, 132, 255);
		color: #fff;
	}
</style>
