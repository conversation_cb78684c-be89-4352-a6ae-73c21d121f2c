<template>
  <div class="face-instant__box">
    <div class="face-instant__video-box">
      <div class="face-instant__video-round">
        <view class="cd" v-if="running && countdown + 1 > 0 && !videoSrc">{{ countdown + 1 }}</view>
        <view class="start" v-if="!running" @click="startRecord"></view>
        <view class="back" v-if="!running" @click="backRecord"></view>
        <view class="turn" v-if="!running" @click="turnRecord(isFront)"></view>
        <video v-if="isFront" ref="videoRef" v-show="!flag" object-fit="cover" class="face-instant__video-camera rotate" :show-center-play-btn="false" :controls="false" preload autoplay loop muted playsinline webkit-playsinline/>
        <video v-else ref="videoRef" v-show="!flag" object-fit="cover" class="face-instant__video-camera" :show-center-play-btn="false" :controls="false" preload autoplay loop muted playsinline webkit-playsinline/>
      </div>
      <view class="face-video__video-box"></view>
    </div>
  </div>
</template>

<script>
require('@/static/tracking/tracking-min')
require('@/static/tracking/data/face-min')
require('@/static/tracking/data/mouth-min')
require('@/static/tracking/data/eye-min')

export default {
  name: "Face",
  props: {
    isInit: {
      type: Boolean,
      default: false
    },
    screenCount: {
      type: Number,
      default: 1
    },
  },
  data() {
    return {
      trackerTask: null,
      tracker: null,
      mediaStreamTrack: null,
      videoSrc: null,
      faceVideo: null,
      faceCamera: null,
      faceContext: null,
      faceCanvas: null,
      flag: false, // 是否符合规格
      prompting: false, // 是否显示提示
      faceBase64Str: '', // 人脸base64
      trackerConfig: { // 人脸识别配置
        initialScale: 4,
        stepSize: 1.7,
        edgesDensity: 0.1
      },
      tip: '保持人脸在框内', // 提示
      timer: null, // 定时器
      countdown: 4, // 倒计时
      countdownTask: null, // 倒计时任务
      screenTask: null,
      running: false, // 是否正在运行
      height: 0, // 高度
      width: 0, // 宽度
      base64: [],
      mediaRecorder: null,
      isFront: false,
    }
  },
  watch: {
    isInit:{
      immediate:true,
      handler(val){
        console.log('vla', val)
        if(val) {
          setTimeout(() => {
          }, 0)
        }
      }
    }
  },
  methods: {
    async startRecord() {
      const vm = this
      let chunks = []
      const support = [ "video/webm", "video/webm;codecs=vp8",
        "video/webm;codecs=daala", "video/webm;codecs=h264",
        "video/mp4",].filter(n => MediaRecorder.isTypeSupported(n))

      console.log('support', support)

      vm.mediaRecorder = new MediaRecorder(window.stream, {
        mimeType : support[0]
      });

      vm.mediaRecorder.ondataavailable = function(e) {
        chunks.push(e.data);
      }
      vm.mediaRecorder.onstop = function(e) {
        var url = URL.createObjectURL(new Blob(chunks, { 'type' : support[0] }));
        console.log(url)
        vm.openVideo(url);
      }

      vm.mediaRecorder.start();

      vm.countdown = 4
      vm.prompting = false
      vm.clearInter()
      // 一旦开始识别，则修改状态
      vm.running = true
      vm.countdownTask = setInterval(() => {
        console.log(vm.countdown - vm.screenCount)

        if (vm.countdown === 0) {
          vm.mediaRecorder.stop();
          vm.clearInter()
        } else {
          vm.countdown--
        }
      }, 1000)
    },

    backRecord() {
      this.resetCamera()
      this.$emit('cancel')
    },

    // 初始化
    async initCamera() {
      const vm = this
      // 由于引入了uni导致video对象实际上是uni-video
      const uniVideo = vm.$refs.videoRef.$el
      // 获取video元素
      vm.faceCamera = uniVideo.getElementsByClassName('uni-video-container')[0].getElementsByTagName('video')[0]

      // 初始化人脸识别，增强识别添加嘴巴和眼睛
      vm.tracker = new window.tracking.ObjectTracker(['face','mouth','eye'])
      vm.tracker.setInitialScale(vm.trackerConfig.initialScale)
      vm.tracker.setStepSize(vm.trackerConfig.stepSize)
      vm.tracker.setEdgesDensity(vm.trackerConfig.edgesDensity)

      // 打开摄像头
      await vm.openCamera()

    },

    turnRecord() {
      this.isFront = !this.isFront
      setTimeout(() => {
        this.openCamera()
      },0)

    },

    // 打开摄像头
    openCamera() {
      const vm = this
      console.log(navigator)
      if (navigator.mediaDevices === undefined) {
        navigator.mediaDevices = {}
      }
      console.log(navigator.getUserMedia, navigator.webkitGetUserMedia, navigator.mozGetUserMedia, navigator.msGetUserMedia)
      console.log(navigator.mediaDevices.getUserMedia, 'getUserMedia')
      if (navigator.mediaDevices.getUserMedia === undefined) {
        navigator.mediaDevices.getUserMedia = function(constraints) {
          // 获取getUserMedia实现
          const getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.msGetUserMedia
          // 如果没实现，则返回一个错误到promise的reject来保持一个统一的接口
          if (!getUserMedia) {
            return Promise.reject(new Error('getUserMedia 浏览器不支持摄像头'))
          }
          return new Promise(function(resolve, reject) {
            getUserMedia.call(navigator, constraints, resolve, reject)
          })
        }
      }

      const constraints = {
        audio: false,
        video: {
          facingMode: this.isFront ? "user" : "environment"
        }
      }

      const promise = navigator.mediaDevices.getUserMedia(constraints)
      promise.then(stream => {
        vm.mediaStreamTrack = stream.getTracks()[0]

        window.stream = stream
        const video = vm.faceCamera
        // 旧的浏览器可能没有srcObject
        if ('srcObject' in video) {
          video.srcObject = stream
        } else {
          video.src = window.URL.createObjectURL(stream)
        }
        video.onloadedmetadata = function(e) {
          video.play()
          vm.width = video.videoWidth
          vm.height = video.videoHeight
        }
      }).catch(err => {
        console.error(err.name + ': ' + err.message)
        vm.tip = '打开摄像头失败'
      })
    },

    // 清除倒计时
    clearInter() {
      const vm = this
      if (vm.countdownTask) {
        clearInterval(vm.countdownTask)
        vm.countdownTask = null
        vm.countdown = 4
      }

      if(vm.screenTask) {
        clearInterval(vm.screenTask)
        vm.screenTask = null
      }
    },

    initTracker() {
      const vm = this
      // 由于引入了uni导致canvas对象实际上是uni-canvas
      if (vm.faceCanvas) {
        vm.faceContext.clearRect(0, 0, vm.faceCanvas.width, vm.faceCanvas.height)
      } else {
        vm.faceCanvas = document.createElement('canvas')
        vm.faceCanvas.width = vm.width
        vm.faceCanvas.height = vm.height
        vm.faceContext = vm.faceCanvas.getContext('2d')
      }
      vm.trackTime = 4
      // 初始化人脸识别，增强识别添加嘴巴和眼睛
      vm.tracker = new window.tracking.ObjectTracker(['face','mouth','eye'])
      vm.tracker.setInitialScale(vm.trackerConfig.initialScale)
      vm.tracker.setStepSize(vm.trackerConfig.stepSize)
      vm.tracker.setEdgesDensity(vm.trackerConfig.edgesDensity)
      vm.trackerTask = window.tracking.track(vm.faceVideo, vm.tracker)
      vm.tracker.on('track', (event) => {
        console.log(event)
        // 一旦开始识别，则修改状态
        vm.running = true
        if (!vm.flag) {
          // 开启定时监听
          if (!vm.timer) {
            vm.timer = setInterval(() => {
              console.log(vm.trackTime)
              if (vm.trackTime === 0) {
                // 倒计时结束
                clearInterval(vm.timer)
                vm.timer = null
                vm.trackTime = 4

                // 如果倒计时结束还没检测出人脸，则提示检测失败，并关闭任务
                if (!vm.flag) {
                  vm.tip = '检测失败'
                  vm.prompting = false
                  // 停止播放
                  vm.faceVideo.pause()
                  vm.faceVideo.src = null
                  vm.trackerTask.stop();
                  vm.trackerTask = null;
                  // 设置错误
                  vm.checkError = true
                  // 关闭加载
                  uni.hideLoading()
                }

              } else {
                vm.trackTime--
              }
            }, 1000)
          }
          if (event.data.length > 0) {
            console.log(vm.base64)
            let rect = event.data[0];
            vm.screenShot(rect)
            if(vm.base64.length >= vm.screenCount) {
              vm.flag = true
              vm.faceVideo.pause()
              vm.faceVideo.src = null
              vm.trackerTask.stop();

              vm.mediaStreamTrack.stop();
              window.stream = null;

              uni.hideLoading()
              clearInterval(vm.timer)
              this.clearVideo();

              uni.setStorageSync('faceBase64Str', vm.base64)
              vm.$emit('success', vm.base64)
            }
          } else {
            vm.tip = '检测失败'
            vm.prompting = false
          }
        }
      })
    },
    clearVideo() {
      let videoBox = document.querySelector('.face-video__video-box')
      if (videoBox.childNodes.length > 0) {
        // 删除所有子节点
        while (videoBox.firstChild) {
          videoBox.removeChild(videoBox.firstChild)
        }
      }
    },
    openVideo(src) {
      const vm = this
      let videoBox = document.querySelector('.face-video__video-box')

      uni.showLoading({
        title: '检测中...',
        mask: true
      })

      this.clearVideo();
      let video = document.createElement('video')
      video.src = src
      video.className = 'face-video__video'
      video.id = 'faceVideo'
      video.width = vm.width
      video.height = vm.height
      video.autoplay = true
      video.controls = true
      videoBox.appendChild(video)
      vm.faceVideo = video
      vm.videoSrc = src
      vm.checkError = false

      vm.faceVideo.play()
      // 延时
      setTimeout(() => {
        vm.initTracker()
      }, 100)

    },
    // 保存图片
    screenShot(rect) {
      const vm = this

      // 直接截取整个视频大小
      vm.faceContext.drawImage(vm.faceVideo, 0, 0, vm.faceVideo.videoWidth, vm.faceVideo.videoHeight);

      vm.faceBase64Str = vm.faceCanvas.toDataURL('image/jpeg');

      if (vm.faceBase64Str.length > 1024) {
        vm.tip = '拍照完成，正在上传...'
      } else {
        vm.tip = '拍照失败，请重试'
      }

      vm.base64.push(this.base64toBlob(vm.faceBase64Str))
    },
    base64toBlob(base64Data) {
      //console.log(base64Data);//data:image/png;base64,
      var byteString;
      if(base64Data.split(',')[0].indexOf('base64') >= 0)
          byteString = atob(base64Data.split(',')[1]);//base64 解码
      else{
          byteString = unescape(base64Data.split(',')[1]);
      }
      var mimeString = base64Data.split(',')[0].split(':')[1].split(';')[0];//mime类型 -- image/png

      // var arrayBuffer = new ArrayBuffer(byteString.length); //创建缓冲数组
      // var ia = new Uint8Array(arrayBuffer);//创建视图
      var ia = new Uint8Array(byteString.length);//创建视图
      for(var i = 0; i < byteString.length; i++) {
          ia[i] = byteString.charCodeAt(i);
      }
      var blob = new Blob([ia], {
          type: mimeString
      });
      return blob;
    },

    resetCamera() {
      const vm = this
      vm.trackerTask = null
      vm.tracker = null
      vm.mediaStreamTrack = null
      vm.faceVideo = null
      vm.faceContext = null
      vm.faceCanvas = null
      vm.flag = false
      vm.prompting = false
      vm.faceBase64Str = ''
      vm.timer = null
      vm.countdown = 2
      vm.countdownTask = null
      vm.running = false
      vm.height = 0
      vm.width = 0
    }
  },
  mounted() {
    console.log('mmmmmmm')
    this.initCamera()
  }
}
</script>

<style lang="scss" scoped>
.face-instant__box {
  width: 100vw;
  height: 100vh;
  //background-color: #fff;
  .face-instant__title {
    font-size: 36rpx;
    font-weight: normal;
    padding: 8vh 10vw 5vw 10vw;
    height: 10vh;
  }
  .face-instant__video-box {
    width: 100vw;

    .face-instant__video-round {
      position: relative;
      width: 100vw;
      height: 100vh;
      .face-instant__video-camera {
        width: 100vw;
        height: 100vh;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 99998;
        &.rotate {
          transform: rotateY(180deg);
        }
      }

      .cd {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        color: rgba(255,255,255,.5);
        font-size: 120px;
        text-shadow: 0 1px 10px rgba(0,0,0,.3);
        z-index: 99999;
      }

      .start {
        position: absolute;
        left: 50%;
        bottom: 200rpx;
        transform: translate(-50%, 0);
        width: 100rpx;
        height: 100rpx;
        border: solid 2px #fff;
        border-radius: 50%;
        z-index: 99999;

        &::after {
          content: "";
          display: block;
          position: absolute;
          left: 50%;
          top: 50%;
          width: 80rpx;
          height: 80rpx;
          transform: translate(-50%, -50%);
          background-color: #1b90ff;
          border-radius: 50%;
        }
      }

      .back {
        position: absolute;
        left: 40rpx;
        top: 100rpx;
        width: 100rpx;
        height: 100rpx;
        line-height: 100rpx;
        z-index: 99999;
        font-size: 32rpx;
        color: #fff;
        background-image: url(@/static/fifthg/imgs/ic_in.png);
        background-repeat: no-repeat;
        background-size: 24rpx 40rpx;
        background-position: center;
        transform: rotate(-180deg);
      }

      .turn {
        position: absolute;
        right: 40rpx;
        top: 100rpx;
        width: 100rpx;
        height: 100rpx;
        line-height: 100rpx;
        z-index: 99999;
        font-size: 32rpx;
        color: #fff;
        background-image: url(@/static/fifthg/imgs/turn.png);
        background-repeat: no-repeat;
        background-size: 60rpx 60rpx;
        background-position: center;
        transform: rotate(-180deg);
      }
    }
    .face-video__video-box {
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
      visibility: hidden;
      width: 0;
      height: 0;
      overflow: hidden;
    }

  }
  .face-instant__tip {
    font-weight: bold;
    text-align: center;
    margin-top: 5vh;
    p {
      height: 2em;
    }
  }

  .face-instant__btn-box {
    padding: 0 20rpx;
    margin-top: 5vh;
    .face-instant__btn {
      border: 1px solid #1b90ff;
      text-align: center;
      color: #1b90ff;
      line-height: 2.6em;
      border-radius: 4px;
      margin: 20rpx;
    }
  }
  .face-img {
    width: 300px;
    height: 300px;
    object-fit: cover;
  }
}
</style>
