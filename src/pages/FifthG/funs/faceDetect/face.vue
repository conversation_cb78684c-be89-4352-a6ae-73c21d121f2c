<template>
  <div ref="faceBoxRef" class="face__box">
    <face-instant ref="faceInstantRef" v-show="!completed" @success="onSuccess" :is-init="true" :screen-count="2" />
    <div class="face-img-box" v-show="completed">
      <img class="face-img" :src="faceBase64Str" alt="" />
    </div>
    <div class="face-check-result" v-show="completed">检测人脸成功</div>

    <div class="face-result-btn-box" v-show="completed">
      <div class="face-result-btn-fill" @click="onUploadAgain">再次上传</div>
      <div class="face-result-btn" @click="onEnterAgain">重新录入</div>
    </div>
  </div>
</template>

<script>
import FaceInstant from './faceInstant'
import * as imageConversion from "image-conversion";

export default {
  name: "Face",
  components: {
    FaceInstant,
  },
  data() {
    return {
      faceBase64Str: '', // 人脸base64
      completed: false, // 是否检测完成
      env: 'h5' // 环境 app | h5
    }
  },
  onLoad(query) {
    // 获取url参数
    const token = query.token
    if (token) {
      // 如果token不为空，则将token存入缓存
      uni.setStorageSync("userToken", token)
    }
  },
  methods: {
    /**
     * 人脸识别回调
     * @param faceBase64Str
     */
    onSuccess(faceBase64Str) {
      console.log(faceBase64Str)
      const vm = this
      vm.completed = true
      vm.faceBase64Str = faceBase64Str[0]
      // 人脸认证
      //vm.onFaceAuth()
    },
    // 重新录入
    onEnterAgain() {
      const vm = this
      // 置空
      vm.faceBase64Str = ''
      vm.completed = false
      if (vm.env === 'h5') {
        vm.$refs.faceInstantRef.onRefresh()
      } else {
        vm.$refs.faceVideoRef.onRefresh()
      }
    },
    // 再次上传
    onUploadAgain() {
      this.onFaceAuth();
    },
    // 人脸认证
    async onFaceAuth() {
      const vm = this
      uni.showLoading({
        title: '上传中...'
      })
      let file = vm.base64ToFile(vm.faceBase64Str, 'face.jpg')
      // 压缩图片compress(file, quality) quality: 0.1 - 1 之间，越小压缩率越高
      let zipFile = await imageConversion.compress(file, 0.3)
      vm.fileToBase64Async(zipFile).then(base64 => {
        console.log(base64)

        vm.faceBase64Str = base64

        vm.$http.post(vm.$apis.faceAuth, {
          faceImg: base64 // 图片最大300KB
        }).then(res => {
          uni.hideLoading()
          console.log('人脸成功后:',res, vm.env, res.responseBody && res.responseBody.authResult, window.FaceAuth)
          if (res.responseBody && res.responseBody.authResult) {
            uni.showToast({
              title: '验证成功'
            })
            // const fromPickNumber = vm.$route.query.fromPickNumber
            // if (vm.env === 'app') {
            //   // 通知app操作成功
            //   try {
            //     window.FaceAuth.callback()
            //   } catch (error) {
            //     console.log(error)
            //   }
            // } else {
            //   if(fromPickNumber){
            //     uni.redirectTo({
            //       url: '/pages/FifthG/funs/open-account/pickNumber'
            //     })
            //   }else{
            //     // 跳转到首页
            //     uni.redirectTo({
            //       url: '/pages/FifthG/index'
            //     })
            //   }
            // }
            // 跳转到首页
            uni.redirectTo({
              url: '/pages/FifthG/index'
            })
          } else {
            uni.showToast({
              title: res.returnInfo.message || '验证失败'
            })
          }
        })
      })
    },
    // 文件转Base64
    fileToBase64Async(file) {
      return new Promise((resolve, reject) => {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (e) => {
          resolve(e.target.result);
        };
      });
    },
    // base64图片转file的方法（base64图片, 设置生成file的文件名）
    base64ToFile(base64, fileName) {
      // 将base64按照 , 进行分割 将前缀  与后续内容分隔开
      let data = base64.split(',');
      // 利用正则表达式 从前缀中获取图片的类型信息（image/png、image/jpeg、image/webp等）
      let type = data[0].match(/:(.*?);/)[1];
      // 从图片的类型信息中 获取具体的文件格式后缀（png、jpeg、webp）
      let suffix = type.split('/')[1];
      // 使用atob()对base64数据进行解码  结果是一个文件数据流 以字符串的格式输出
      const bstr = window.atob(data[1]);
      // 获取解码结果字符串的长度
      let n = bstr.length
      // 根据解码结果字符串的长度创建一个等长的整形数字数组
      // 但在创建时 所有元素初始值都为 0
      const u8arr = new Uint8Array(n)
      // 将整形数组的每个元素填充为解码结果字符串对应位置字符的UTF-16 编码单元
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return  new File([u8arr], `${fileName}.${suffix}`, {
        type: type
      });
}
  },
  mounted() {

  }
}
</script>

<style lang="scss" scoped>
.face__box {
  background-color: #fff;
  height: 100vh;
  .face-img-box {
    display: flex;
    justify-content: center;
    overflow: hidden;
    padding: 10vh 0;
    .face-img {
      width: 300rpx;
      height: auto;
      border: 2px #FFFFFF solid;
      box-shadow: 2px 20px 30px rgba(0,0,0,0.4);
    }
  }
  .face-check-result {
    text-align: center;
    font-weight: bold;
  }
  .face-result-btn-box {
    margin-top: 5vh;
    .face-result-btn {
      border: 1px solid #1b90ff;
      text-align: center;
      color: #1b90ff;
      line-height: 2.6em;
      border-radius: 4px;
      margin: 20rpx;
    }
    .face-result-btn-fill {
      border: 1px solid #1b90ff;
      background-color: #1b90ff;
      text-align: center;
      color: #FFFFFF;
      line-height: 2.6em;
      border-radius: 4px;
      margin: 20rpx;
    }
    .face-result-btn-text {
      font-size: 0.9em;
      color: #999;
      text-align: center;
    }
  }

}
</style>
