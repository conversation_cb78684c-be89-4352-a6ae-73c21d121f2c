<template>
  <web-view class="body" allow="microphone;camera;midi;encrypted-media;" :webview-styles="webviewStyles"  v-if="url" :src="url"></web-view>
</template>

<script>
export default {
  name: "liveDetection",
  props: {
    url: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      webviewStyles: {
        progress: {
          color: '#1F8FF7'
        },
        width: '100%',
        height: '100%',
      }
    }
  },
  mounted() {
    console.log('liveDetection====>receiveMessage========>mounted')
    window.addEventListener("message", this.receiveMessage, false);
  },
  destroyed() {
    console.log('liveDetection====>receiveMessage========>destroyed')
    window.removeEventListener("message", this.receiveMessage, false);
  },
  methods: {
    receiveMessage(event) {
      console.log('liveDetection====>receiveMessage========>', event)
      if (event.data && event.data.key === 'T') {
        uni.$emit('aliRedirect', {
          aliProcess: 100,
          aliClose: true,
          aliErr: '',
          aliUrl: '',
          url: event.data?.url
        })
      } else {
        uni.$emit('aliRedirect', {
          aliProcess: 0,
          aliClose: false,
          aliErr: '',
          aliUrl: '',
        })
      }

    }
  }
}
</script>

<style scoped>
.body {
  background-color: #FFFFFF;
  width: 100%;
  height: 100%;
}
</style>
