<!-- 流程界面 -->
<template>
	<view>
		<action-bar title="开户流程引导" leftIcon="back"></action-bar>
		<CustMsgHeader v-if="custInfo" :needLeavebtn="false" :needCustDetail="false"></CustMsgHeader>
		<view class="display-flex-column select-prod-group" @click="rePickProd">
			<text class="select-title titleBlue">{{ productTip }}</text>
			<view class="border-bottom"></view>
			<view v-if="salesInfo" class="display-flex-row select-prod-msg flex-1">
				<text class="product-name flex-1">{{ salesInfo.knowname }}</text>
				<text class="product-price">{{ salesInfo.tips || salesInfo.priceDisplay }}</text>
				<image class="product-img" :src="salesInfo.imageL" />
			</view>
			<button v-if="!isComplete" size="mini" type="primary" style="margin-top: 20rpx;">重新选择</button>
		</view>

		<view class="step-group">
			<text class="titleBlue">请参照以下流程指引为客户开户入网</text>

			<view class="border-bottom"></view>

			<view class="display-flex-column out-card-view step-info" v-for="(stepBean, index) in stepInfo.process"
				:key="index" @click="onClickStepBean(stepBean, index, stepInfo.process)">
				<view class="crad-view display-flex-row">
					<text class="index-txt">{{ index + 1 }}</text>
					<view class="name-txt">
						<text>{{ stepBean.name }}</text>
						<text v-if="stepBean.name == '移动5G选号'" style="margin-left: 20upx">{{ stepBean.display }}</text>
					</view>
					<!-- <text class="name-txt">{{stepBean.name}}</text> -->
					<i class="state-txt iconfont" :style="isStepActivity(stepBean)" v-html="getIcon(stepBean)" />
				</view>
				<view class="sub-tip" v-if="stepBean.code == 'IDCONF'">实名认证有效期仅1小时，如果超时请重新认证</view>
				<!-- <view class="crad-view display-flex-row in-card-view" v-for="(subStepBean, index) in stepBean.child"
					:key="index">
					<text class="index-txt"></text>
					<text class="name-txt">{{ subStepBean.name }}</text>
					<i class="state-txt iconfont" :style="isStepActivity(subStepBean)" v-html="getIcon(subStepBean)" />
				</view> -->
			</view>
		</view>

		<!-- 暂时不需要放弃办理 -->
		<view class="giveup-btn" v-if="false">放弃办理</view>

		<van-popup class="popup" v-model="showAuth">
			<view class="title">请选择认证方式</view>
			<view class="ctn">
				<view class="type" :class="'t' + n.value" v-for="n, i in tabs" :key="i" @click="toVerifiedPage(n)">
					<view class="h1">{{ n.name }}</view>
					<view class="icons">
						<view v-if="n.value != '1'" class="reader">读卡器</view>
						<view class="idcard">身份证</view>
					</view>
				</view>
			</view>
		</van-popup>
	</view>
</template>

<script>
import Vue from 'vue'
import {
	mapGetters,
	mapActions,
	mapMutations
} from 'vuex';
import ActionBar from "@/components/ActionBar.vue"
import CustMsgHeader from "../../components/CustMsgHeader.vue"
import { Popup } from 'vant'
Vue.use(Popup)

import {
	v4 as uuidv4
} from 'uuid';
export default {
	data() {
		return {
			showAuth: false,
			tabs: [],
			verifiedData: {},
		};
	},
	created() {

	},
	mounted() {
    console.log('behalfStep=========>custInfo=========>',this.custInfo)
	},
	components: {
		ActionBar,
		CustMsgHeader
	},

	async onLoad(data) {
		console.log('step', data)
		if (data.poid) {
			let poid = data.poid
			await this.queProcess({
				poid: poid
			});
			this.resetSubmitData()//清掉所有开户数据，避免上一个业务的残留数据影响
			this.checkOrder();
		}
		// this.qryAuthIdetifyType()

	},

	onUnload() {
		console.log("onUnload step")
		//this.resetStep()
	},

	computed: {
		...mapGetters({
			custInfo: "getCustInfo",
			stepInfo: "getStepInfo",
			salesInfo: 'getSalesInfo',
		}),
		productTip() {
			let productTip = "您已选择了移动套餐"
			if (this.salesInfo && this.salesInfo.offertypename) {
				productTip = "您已选择了" + this.salesInfo.offertypename + "套餐"
			}
			return productTip
		},
		isComplete() {
			//检测是否完成，当已支付完就认为流程已经完成
			let stepProcess = this.stepInfo.process || []
			for (let processBean of stepProcess) {
				if (processBean.code == 'DBKHINFO') {
					return processBean.done
				}
			}
			return false;
		},
		isPay() {
			let stepProcess = this.stepInfo.process
			if (!stepProcess) {
				return
			}
			for (let processBean of stepProcess) {
				if (processBean.code == 'PAYCONF') {
					return processBean.done
				}
			}
			return false;
		}
	},

	methods: {
		...mapActions({
			queProcess: 'queProcess',
			needAppAgreement: 'needAppAgreement',
			saveProcess: 'saveProcess',
		}),

		...mapMutations({
			resetStep: "resetStepInfo",
			resetSubmitData: "resetSubmitData"
		}),
		toVerifiedPage(item) {
			this.$rauter.navigateTo({
				url: item.url,
				data: {
					...this.verifiedData,
					type: item.value,
				},
			})
		},
		qryAuthIdetifyType() {
			const vm = this
			const names = {
				'0': {
					name: "读卡器认证",
					url: 'verifiedID'
				},
				'1': {
					name: "国网H5实名认证",
					url: 'verifiedID'
				},
				'2': {
					name: "读卡器实名认证",
					url: 'verifiedID'
				}
			}
			vm.$http.post(vm.$apis.qryAuthIdetifyType, {}).then(resp => {
				console.log(resp)
				if (resp && resp.responseBody.type) {
					const type = resp.responseBody.type
					type.split('~').forEach(item => {
						vm.tabs.unshift({
							...names[item],
							value: item
						})
					})
				}
			});
		},
		isStepActivity(stepBean) {
			return stepBean.done ? {
				color: '#52C41A'
			} : {
				color: '#F5222D'
			};
		},
		getIcon(stepBean) {
			return stepBean.done ? '&#xe730;' : '&#xe655';
		},
		async onClickStepBean(stepBean, index, arr) {
			console.log('stepBean,', stepBean);
			this.verifiedData = {}
			if (this.isPay && this.isComplete && stepBean.page != "appAgreement" && stepBean.page != "noPaper") {
				//已完成，不能继续办理业务
				this.$util.feeback.showToast({
					title: "支付已完成，不能进行该操作。"
				})
				return;
			}
			let cmms_status = this.stepInfo?.data?.DBKHINFO?.cmms_status || '';
			//如果是漏单的，不能继续办理业务
			if (cmms_status == '7' && stepBean.page != "SignDBKH") {
				this.$util.feeback.showToast({
					title: "支付已完成，不能进行该操作。"
				})
				return;
			}
			if (stepBean.page == 'SignDBKH' && stepBean.done) {
				this.$util.feeback.showToast({
					title: "业务受理单电子签名已完成。"
				});
				return;
			}

			let page = '',
				data = {
					code: stepBean.code,
          cpcode: stepBean.code,
					poid: stepBean.poid,
					custid: this.custInfo.custid,
          serialno: this.stepInfo.data.DBKHINFO?.serialno,
          custOrderId: this.stepInfo.data.DBKHINFO?.custOrderid
				}
			let findIndex = arr.findIndex(val => !val.done)
			page = stepBean.page
			// if (findIndex >= 0 && index && findIndex < index) {
			// 	uni.showToast({
			// 		title: '请按照流程完成步骤',
			// 		icon: 'none'
			// 	})
			// 	return
			// } else {
			// }
			// let needAppAgreement = await this.needAppAgreement(stepBean)
			// if (needAppAgreement) {
			// 	return;
			// }

			if (page) {
				console.log(page)
				if(page == 'verifiedID') {
					// this.showAuth = true
					// this.verifiedData = data
					// return
					this.$rauter.navigateTo({
						url: 'realName',
						data:{
							verifiedData:data
						}
					})
				}else{
					this.$rauter.navigateTo({
						url: page,
						data,
					})
				}
			}
		},

		rePickProd() {
			if (this.isComplete) {
				//已完成，不能继续办理业务
				return;
			}
			this.$rauter.navigateTo({
				url: "behalfProductsSelect",
				data: {
					delta: 2
				}
			});
		},
		checkOrder() {
			// 判断当前流程到支付确认环节，且未完成
			var payStep = null
			if (this.stepInfo.process) {
				for (let i = 0; i < this.stepInfo.process.length; i++) {
					var step = this.stepInfo.process[i];
					if (step.code === 'PAYCONF') {
						if (step.pstatus * 1 !== 2) {
							if (i > 0) {
								var preStep = this.stepInfo.process[i - 1]
								if (preStep.done === true) {
									payStep = step
								}
							} else {
								payStep = step
							}
						}
					}
				}
			}
			if (payStep && this.stepInfo?.data?.DBKHINFO?.custOrderid) {
				let custOrderId = this.stepInfo?.data?.DBKHINFO?.custOrderid || '';
				let payway = this.stepInfo?.data?.DBKHINFO?.payway || '';
				// if(payway!='H' && payway!='G'){
				// 	return;
				// }

				this.$http.request({
					method: 'POST',
					api: this.$apis.queStepQdUnifyPayResultInfo,
					url: this.$baseUrl,
					isAutoLoading: false,
					data: { custorderid: custOrderId }
				}).then(resp => {
					let isPay = resp?.responseBody?.data?.cmms_status == '7'
					let isConfirm = resp?.responseBody?.data?.cmms_status == '200'
					if (isPay) {
						this.stepInfo.data.DBKHINFO.cmms_status = '7';
						this.payOrder(payway)
					} else if (isConfirm) {
						this.stepInfo.data.DBKHINFO.cmms_status = '200';
						let step = this.stepInfo.process.filter(o => {
							return o.code === 'PAYCONF'
						})
						if (step && step.pstatus * 1 !== 2) {
							this.saveData()
						}
					}
				});
			}
		},
		payOrder(payway) {
			let paycode = "000000";
			let payreqid = uuidv4();
			let params = {
				paycode: paycode,
				payreqid: payreqid,
				payway: payway,
			}
			if (this.stepInfo?.data?.DBKHINFO?.custOrderid) {
				//实时开户的支付
				this.commitOpenAccount(params)
				return;
			}
		},
		//实时开户的确认
		commitOpenAccount(params) {
			params.multipaywayflag = "N"
			params.custOrderid = this.stepInfo?.data?.DBKHINFO?.custOrderid
			params.poid = this.stepInfo?.poid
			this.$http.post(this.$apis.commitOpenAccount, params).then(resp => {
				this.saveData();
			});
		},
		saveData() {
			let saveData = {
				"desc": "自动补单"
			};
			this.saveProcess({
				data: {
					"poid": this.stepInfo.poid,
					"cpcode": "PAYCONF",
					"data": JSON.stringify(saveData),
					"temp": "1"
				},
				refreshCustInfo: true,
				custid: this.custInfo.custid,
				unNeedNext: true
			})
		},
	},
}
</script>

<style lang='scss' scoped>
.select-prod-group {
	background-color: #FFFFFF;
	padding: 30rpx 30rpx;


	.select-prod-msg {
		position: relative;
		margin-top: 20rpx;

		.product-name {
			font-weight: bold;
			height: 120rpx;
			line-height: 1.5;
			cursor: pointer;
			word-break: break-all;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 3; // 数字代表文字需要显示几行
		}

		.product-price {
			position: absolute;
			line-height: 1;
			color: #F5222D;
			margin-top: 15rpx;
			bottom: 0;
		}

		.product-img {
			width: 160rpx;
			height: 160rpx;
			/* position: absolute;
				right: 0;
				top: 0;
				bottom: 0; */
		}
	}


}

.step-group {
	margin-top: 40rpx;
	background-color: #FFFFFF;
	padding: 20rpx;

	.crad-view {


		.index-txt {
			padding-right: 20rpx;
		}

		.name-txt {
			flex: 10;

		}

		.state-txt {
			font-size: large;
		}
	}

	.sub-tip {
		position: relative;
		margin-left: 32rpx;
		padding-left: 40rpx;
		font-size: 24rpx;
		line-height: 40rpx;
		color: #b1b1b1;

		&::after {
			content: '';
			display: block;
			position: absolute;
			top: 50%;
			left: 0;
			width: 30rpx;
			height: 30rpx;
			background-image: url(../../../../static/fifthg/imgs/error-warning-fill.png);
			background-repeat: no-repeat;
			background-position: center;
			background-size: contain;
			transform: translate(0, -50%);
		}
	}

	.out-card-view {
		border: 1px solid #BBBBBB;
		border-radius: 18rpx;
		padding: 20rpx;
	}

	.in-card-view {
		margin-top: 20rpx;
	}

	.step-info {
		margin-top: 30rpx !important;
	}

}

.giveup-btn {
	text-align: center;
	margin: 40rpx 120rpx;
	padding: 20rpx 30rpx;
	background-color: #FF7B3A;
	color: #FFFFFF;
	border-radius: 10rpx;
}

.popup {
	padding: 30rpx;
	border-radius: 10rpx;

	.title {
		padding-bottom: 20rpx;
		font-size: 32rpx;
		color: #232323;
		text-align: center;
	}

	.ctn {
		.type {
			margin: 20rpx 0;
			padding: 30rpx 50rpx 30rpx 220rpx;
			border: solid 1px #C4E1FC;
			border-radius: 10rpx;
			background-color: #F8FCFF;

			&.t0 {
				background-image: url(../../../../static/fifthg/imgs/bg_readcard.png);
				background-repeat: no-repeat;
				background-size: 245rpx 148rpx;
				background-position: 0 bottom;
			}

			&.t1 {
				background-image: url(../../../../static/fifthg/imgs/bg_h5.png);
				background-repeat: no-repeat;
				background-size: 245rpx 148rpx;
				background-position: 0 bottom;
			}

			&.t2 {
				background-image: url(../../../../static/fifthg/imgs/bg_readcard.png);
				background-repeat: no-repeat;
				background-size: 245rpx 148rpx;
				background-position: 0 bottom;
			}

			.h1 {
				font-size: 32rpx;
				color: #1F8FF7;
				white-space: nowrap;
			}

			.icons {
				display: flex;
				align-items: center;
				.reader, .idcard {
					flex: 1;
					margin-right: 10rpx;
					padding-left: 60rpx;
					color: #999;
					white-space: nowrap;
				}

				.reader {
					background-image: url(../../../../static/fifthg/imgs/iccid.png);
					background-repeat: no-repeat;
					background-size: 48rpx 36rpx;
					background-position: 0 center;
				}

				.idcard {
					background-image: url(../../../../static/fifthg/imgs/idno.png);
					background-repeat: no-repeat;
					background-size: 48rpx 36rpx;
					background-position: 0 center;
				}
			}
		}
	}
}
</style>
