<template>
  <div>
<!--    <action-bar title="机主实名" leftIcon="back" :winHisBack="true"></action-bar>-->
    <div class="body">
      <div class="card">
        <div class="item">
          <div class="item-left">证件姓名<span class="red">*</span> </div>
          <div class="item-right">
            {{ params.name }}
          </div>
        </div>
        <div class="item">
          <div class="item-left">证件地址<span class="red">*</span></div>
          <div class="item-right">
            {{ params.addr }}
          </div>
        </div>
        <picker class="picker" range-key="mname" :range="cardTypeList" :disabled="true">
          <div class="item">
            <div class="item-left">证件类型 <span class="red">*</span></div>
            <div class="item-right dis">
              <div class="text" v-if="cardtype">{{ cardtype.mname }}</div>
              <div class="text placeholder" v-else>请选择证件类型</div>
            </div>
          </div>
        </picker>
        <div class="item">
          <div class="item-left">证件号码<span class="red">*</span></div>
          <div class="item-right">
            {{ params.cardno }}
          </div>
        </div>
        <picker class="picker" range-key="mname" :range="ownertypelist" :disabled="true">
          <div class="item">
            <div class="item-left">证件归属 <span class="red">*</span></div>
            <div class="item-right dis">
              <div class="text" v-if="ownertype">{{ ownertype.mname }}</div>
              <div class="text placeholder" v-else>请选择证件归属</div>
            </div>
          </div>
        </picker>
        <div class="item">
          <div class="item-left">证件生效日期<span class="red">*</span></div>
          <div class="item-right">{{ params.startDate }}</div>
        </div>
        <div class="item only">
          <div class="item-left">证件失效日期<span class="red">*</span></div>
          <div class="item-right">{{ params.endDate }}</div>
        </div>
      </div>
      <div class="btn">
        <div class="btn1" @click="readIDCard">读取身份证</div>
        <div class="btn1" @click="readID">识别身份证</div>
        <div class="btn2" @click="nextFn">下一步</div>
      </div>
    </div>
  </div>
</template>

<script>
import {mapGetters,mapActions,mapMutations} from 'vuex'
import launchMiniprogram from '@/sdk/eWechat/launchMiniprogram';
export default{
  name: 'machineRealName',
  mixins: [launchMiniprogram],
  data(){
    return{
      readIdInfo:{},
      params:{
        name:"",
        addr:'',
        cardno:'',
        startDate:'',
        endDate:''
      },
      ownertype:null,
      cardtype:null,
      cardTypeList:[
        {
          mcode:1,
          mname:'身份证'
        }
      ],
      ownertypelist:[
        {
          mcode:1,
          mname:'机主'
        },
        // {
        //   mcode:2,
        //   mname:'代/经办人'
        // },
        // {
        //   mcode:3,
        //   mname:'责任人'
        // },
        // {
        //   mcode:4,
        //   mname:'单位信息'
        // }
      ],
      verifiedData:{},
      DBKHID: {},
      options: {}
    }
  },
  onLoad(options){
    this.options = options;
    this.initSaveProcess()
  },
  async created() {
    this.refreshOrderId(); // 刷新orderId
    await this.agentConfig(); // 唤起小程序基础配置
    this.ownertype = this.ownertypelist[0]
    this.cardtype = this.cardTypeList[0]
  },
  mounted(){
    console.log('stepInfo=======>',this.stepInfo);
    console.log('this.stepInfo?.data?.DBKHID=======>',this.stepInfo?.data?.DBKHID);
    this.DBKHID = {...this.stepInfo?.data?.DBKHID}
    console.log('this.DBKHID=======>',this.DBKHID);

    let machineOwnerText = this.stepInfo?.data?.DBKHID?.machineOwnerText
    let poid = this.stepInfo?.poid
    if(poid && machineOwnerText){
      this.params = machineOwnerText
    }
  },
  computed:{
    ...mapGetters({
      custInfo:'getCustInfo',
      stepInfo: 'getStepInfo',
    })
  },
  methods:{
    ...mapActions({
      saveProcess: 'saveProcess',
    }),
    readIDCard(){
      this.invokeIDCardReader().then(() => {
        console.info('idCardReader success');
      });
    },
    readID() {
      if(process.env.RUN_ENV == 'local') {
        this.readIDCardDataTest()
      } else {
        this.readIDCardData()
      }
    },
    readIDCardDataTest() {
      const ID_RESULT = {address: "湖北省红安县觅儿寺镇杨家田村1组", birthday: "20010408", devdn: "SR720211105030", dn: "0001484201130C03000000000000147A", effectDate: "20200511", expireDate: "20300511", gender: "男", genderCode: "1", idNum: "******************", issueOrg: "红安县公安局", name: "湖北小学生", nation: "汉", nationCode: "01", photo: "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", vendorName: ""}
      // 测试身份证信息
      var datas = {
        idCardInfo: ID_RESULT
      };

      console.info('readIDCardData params: ', ID_RESULT);
      this.params.name =  datas.idCardInfo.name
      this.params.cardno =  datas.idCardInfo.idNum
      this.params.addr = datas.idCardInfo.address;
      let formatSDate = datas.idCardInfo.effectDate;
      formatSDate = formatSDate.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3');
      let formatEDate = datas.idCardInfo.expireDate;
      if (formatEDate == '长期') {
        formatEDate = '2999-12-31';
      } else {
        formatEDate = formatEDate.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3');
      }
      this.params.startDate = formatSDate;
      this.params.endDate = formatEDate;


      console.info('readIDCardData params: ', this.params);
    },
    readIDCardData() {
      let param = {
        orderId: this.orderId,
        //时间转换为年月日
        basisTime: this.$util.dataProcess.formatDate(new Date(), 'yyyy/MM/dd hh:mm:ss')
      };
      console.info('readIDCardData url: ', this.$apis.queIdCardInfo);
      console.info('readIDCardData params: ', JSON.stringify(param));

      this.$http
        .post(this.$apis.queIdCardInfo, param)
        .then(response => {
          let res = response.responseBody;
          console.info('readIDCardData result:', JSON.stringify(res));
          if (res && res.data) {
            var datas = res.data;
            this.params.name =  datas.idCardInfo.name
            this.params.cardno =  datas.idCardInfo.cardId
            this.params.addr = datas.idCardInfo.address;
            this.params.startDate = datas.idCardInfo.validStart;
            this.params.endDate = datas.idCardInfo.validEnd;
            console.log('临时值lztparams====' + JSON.stringify(this.params));
          }
        })
        .catch(err => {
          console.error('readIDCardData error:', err);
        });
    },
    async nextFn(){
      if(!this.params.name){
        uni.showToast({
					title: '请读取身份证',
					icon: 'none'
				})
				return
      }
      if (this.params.name != this.custInfo.custname || this.params.cardno != this.custInfo.cardno) {
        uni.showToast({
          title: '机主信息与客户信息不一致',
          icon: 'none'
        })
        return
      }

      const result = await this.$http.post(this.$apis.machineOwnerTextAuth,{
        name:this.params.name,
        custId:this.custInfo.custid,
        cardNo:this.params.cardno,
        effectDate:this.params.startDate,
        edate:this.params.endDate,
        idenAddress:this.params.addr
      })
      console.log('machineOwnerTextAuth',result);
      if(result.returnInfo.code == '0'){

        this.DBKHID = {
          ...this.DBKHID,
          custid: this.custInfo.custid,
          poid: this.verifiedData.poid,
          serialno: result.returnInfo.message,
          machineOwnerText: this.params
        }

        await this.saveProcess({
          data: {
            poid: this.stepInfo.poid,
            cpcode: 'DBKHID',
            data: JSON.stringify(this.DBKHID),
            temp: 0
          },
          unNeedNext: true,
          afterProcess: () => {
            this.$rauter.navigateTo({
              url: 'realName',
              data: {
                poid: this.stepInfo.poid,
                code: 'DBKHID',
                cpcode: 'DBKHID'
              },
            })
          }
        });


      }
    },
    initSaveProcess() {
      console.log('onLoad========>saveProcess');
      let stepInfo = JSON.parse(JSON.stringify(this.stepInfo)),
        optionsCode = this.options?.code;
      let data = {
        poid: stepInfo.poid || this.$route.query.poid,
        cpcode: optionsCode ? optionsCode : '',
        temp: 0
      };
      let keepData = {
        data,
        unNeedNext: true
      };
      this.saveProcess(keepData);
    }
  }
}
</script>

<style lang="scss" scoped>
.body{
  padding: 20rpx 20rpx;
  box-sizing: border-box;
  .title-content{
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    .title-line{
      width: 10rpx;
      height: 32rpx;
      border-radius: 60rpx;
      background-color: rgba(64, 149, 229, 1);
      margin-right: 20rpx;
    }
    .title{
      color: #212121;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
  .card{
    border-radius: 10rpx;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.1);
    padding:0 20rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;
    .item{
      padding: 35rpx 20rpx 30rpx 20rpx;
      display: flex;
      align-items: center;
      border-bottom: 1px solid rgba(239, 239, 239, 1);
      box-sizing: border-box;
      .item-left{
        flex: 1;
        color: #212121;
        font-size: 28rpx;
        .red{
          color: #FF2E2E;
        }
      }
      .item-right{
        flex: 2;
        color: #212121;
        font-weight: bold;
        font-size: 28rpx;
        white-space: pre-wrap;
        word-wrap: break-word;
        max-width: 66%;
        .textarea{
          width: 100%;
        }
        .placeholder{
          color: #999;
        }

        .icon-arw{
          width: 20rpx;
          height: 20rpx;
          background: url('../../../../static/fifthg/ioa/icon-arw.png');
          background-position: center;
          background-repeat: no-repeat;
          background-size: 20rpx 20rpx;
        }
      }
      .dis{
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
    .only{
      border-bottom: none;
    }
    .warning-item{
      padding: 15rpx 20rpx 23rpx 20rpx;
      .warning{
        width: 24rpx;
        height: 24rpx;
        background: url('../../../../static/oaBehalf/warning.png');
        background-position: center;
        background-repeat: no-repeat;
        background-size: 24rpx 24rpx;
        margin-right: 10rpx;
      }
      .prompt{
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
}
.btn{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 100rpx;
  .btn1{
    flex: 1;
    height: 90rpx;
    line-height: 90rpx;
    color: #1F8FF7;
    border-radius: 45rpx;
    font-size: 28rpx;
    text-align: center;
    margin-right: 20rpx;
    border: 1px solid #1F8FF7;
  }
  .btn2{
    flex: 2;
    height: 90rpx;
    line-height: 90rpx;
    border: 1px solid #1F8FF7;
    background: #1F8FF7;
    color: #FFFFFF;
    border-radius: 60rpx;
    font-size: 28rpx;
    text-align: center;
  }
}
</style>
