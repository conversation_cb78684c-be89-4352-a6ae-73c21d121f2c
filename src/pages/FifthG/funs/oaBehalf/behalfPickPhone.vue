<template>
	<PickMsgItem ref="phone" :title="'手机号'" :range="phoneList" rangeKey="mobileno">
	</PickMsgItem>
</template>

<script>
	import {
		mapGetters
	} from 'vuex'
	import PickMsgItem from "./behalfPickMsgItem.vue"
	export default {
		data() {
			return {
				phoneList: [],
			};
		},
		created() {
			this.initData()
		},
		components: {
			PickMsgItem
		},

		computed: {
			...mapGetters({
				custInfo: 'getCustInfo',
			}),
		},

		methods: {
			initData() {
				console.log(this.userInfo)
				if (this.custInfo && this.custInfo.servs && this.custInfo.servs.length) {
					let phoneList = []
					this.custInfo.servs.forEach(val => {
						if (val.mobileno) {
							phoneList.push(val)
						}
					})
					this.phoneList = [...phoneList]
				}
			},

			getPickBean() {
				return this.$refs.phone.getPickBean()
			}
		}
	}
</script>

<style lang='scss'>

</style>
