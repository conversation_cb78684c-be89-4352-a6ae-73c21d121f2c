<template>
	<view class="content">
		<view class="wrap">
			<view class="form">

<!--				<view class="card sim">-->
<!--					<view class="field">-->
<!--						<view class="main">-->
<!--							<view class="perfix">客户姓名<text class="red">*</text></view>-->
<!--							<view class="body">-->
<!--								<input class="input" disabled v-model="name" type="text" placeholder="" />-->
<!--							</view>-->
<!--							<view class="suffix">-->
<!--								<view class="btn" @click="readIDCard">读取身份证</view>-->
<!--							</view>-->
<!--						</view>-->
<!--					</view>-->
<!--				</view>-->

<!--				<view class="card sim">-->
<!--					<view class="field">-->
<!--						<view class="main">-->
<!--							<view class="perfix">身份证号<text class="red">*</text></view>-->
<!--							<view class="body">-->
<!--								<input class="input" disabled v-model="idno" @blur="changePrestorefee" type="text"-->
<!--									placeholder="" />-->
<!--							</view>-->
<!--						</view>-->
<!--					</view>-->
<!--				</view>-->

				<view class="card sim" v-if="options.isBehalf">
					<view class="field">
						<view class="main">
							<view class="perfix">联系电话<text class="red">*</text></view>
							<view class="body">
								<input class="input" v-model="phone" type="text" placeholder="请输入联系电话">
							</view>
						</view>
					</view>
				</view>
				<view class="card sim">
					<view class="field">
						<picker class="picker" @change="changeIdtype" range-key="mname" :range="idtypelist">
							<view class="main">
								<view class="perfix">证件类型<text class="red">*</text></view>
								<view class="body">
									<view class="text" v-if="idtype.mname">{{ idtype.mname }}</view>
									<view class="text placeholder" v-else>请选择证件类型</view>
								</view>
								<view class="suffix">
									<view class="icon-arw"></view>
								</view>
							</view>
						</picker>
					</view>
				</view>
				<view class="card sim">
					<view class="field">
						<picker class="picker" @change="changeOwnertype" range-key="mname" :range="ownertypelist">
							<view class="main">
								<view class="perfix">证件归属<text class="red">*</text></view>
								<view class="body">
									<view class="text" v-if="ownertype">{{ ownertype.mname }}</view>
									<view class="text placeholder" v-else>请选择证件归属</view>
								</view>
								<view class="suffix">
									<view class="icon-arw"></view>
								</view>
							</view>
						</picker>
					</view>
				</view>
				<view class="card sim" v-if="options.isBehalf">
					<view class="field">
						<picker class="picker" @change="changehasPrivType" range-key="mname" :range="hasPrivTypeList">
							<view class="main">
								<view class="perfix">代办类型<text class="red">*</text></view>
								<view class="body">
									<view class="text placeholder" v-if="!hasPrivType">请选择代办类型</view>
									<view class="text" v-else>{{ hasPrivType.mname }}</view>
								</view>
								<view class="suffix">
									<view class="icon-arw"></view>
								</view>
							</view>
						</picker>
					</view>
				</view>
				<view class="card sim" v-if="options.isBehalf">
					<view class="field">
						<picker class="picker" @change="changeRelationType" range-key="mname" :range="relationTypeList">
							<view class="main">
								<view class="perfix">与被代办人关系<text class="red">*</text></view>
								<view class="body">
									<view class="text placeholder" v-if="!relationType">请选择与被代办人关系</view>
									<view class="text" v-else>{{ relationType.mname }}</view>
								</view>
								<view class="suffix">
									<view class="icon-arw"></view>
								</view>
							</view>
						</picker>
					</view>
				</view>
				<view class="card sim" v-if="options.isBehalf">
					<view class="field">
						<view class="main">
							<view class="perfix">关系证明<text class="red">*</text></view>
							<view class="body" @click="goRelationFile">
								<view class="text placeholder" v-if="!relationFile">请上传关系证明图片</view>
								<view class="text" v-else>已上传关系证明</view>
							</view>
							<view class="suffix" @click="goRelationFile">
								<view class="icon-arw"></view>
							</view>
						</view>
					</view>
				</view>
				<button class="next-btn" @click="goNextIDCard">下一步</button>
			</view>
		</view>
	</view>
</template>

<script>
import {
	mapGetters,
	mapMutations,
	mapActions
} from "vuex";
import util from "../../../../common/js/util";
import Vue from 'vue';
import { Popup, Dialog } from 'vant';
import {
	cardType,
} from '@/store/modules/sysparam.js'
Vue.use(Dialog);
Vue.use(Popup);

export default {
	data() {
		return {
			readIdInfo: {},
			name: '',
			idno: '',
			phone: '',   // 联系电话
			showCompare: false,
			inMobileService: false,
			conflictInfo: [],
			tempCustInfo: {},
			ownertypeDialog: false,
			ownertype: null,
			ownertypename: null,
			ownertypelist: [
				// {
				// 	mcode: 1,
				// 	mname: '机主'
				// },
        {
					mcode: 2,
					mname: '代/经办人'
				},
        // {
				// 	mcode: 3,
				// 	mname: '责任人'
				// },
        // {
				// 	mcode: 4,
				// 	mname: '单位信息'
				// }
			],
			idtype: {},
			idtypelist: [],
			cardTypeEnum: {
				'8': '港澳台回乡证',
				'b': '士兵证',
				'a': '护照',
				'0': '其他类型',
				'7': '单位证明或营业执照',
				'6': '证件遗失证明',
				'5': '志愿兵证',
				'4': '警官证',
				'3': '军官证',
				'2': '户口簿',
				'1': '身份证',
			},
			urlObj: {},
			show: false,
			flowStatus: {
				aztStatus: 'loading',
				dataStatus: 'loading',
				uploadStatus: 'loading',
				authStatus: 'loading'
			},
			flowTips: '',
			buttonColor: '#7C7C7C',
			aztApi: null,
			hasPrivType: null,  // 代办类型
			hasPrivTypeList: [
				{
					mcode: '0',
					mname: '年满18周岁以上代办（包含18周岁）'
				},
				{
					mcode: 1,
					mname: '16周岁以下代办（不包含16周岁）'
				},
				{
					mcode:	2,
					mname: '16周岁到18周岁代办，包含16周岁'
				},
				// {
				// 	mcode: 3,
				// 	mname: '客户离世代办'
				// }
			],
			relationType:null,    // 代办人与被代办人关系
			relationTypeList:[
				{
					mcode:1,
					mname:'父亲'
				},
				{
					mcode:2,
					mname:'母亲'
				},
				{
					mcode:3,
					mname:'祖父母'
				},
				{
					mcode:4,
					mname:'外祖父母'
				},
				{
					mcode:5,
					mname:"其他"
				}
			],
			relationFile:null,    // 关系证明
      RNAT: 0,
      options: {}
		};
	},
	// props: {
	// 	options: {
	// 		type: Object,
	// 		default: {}
	// 	}
	// },
  onLoad(options) {
    console.log('AztVerified======>',options)
    this.options = options
  },

  created() {
		console.log('AztVerified', this.options);
    this.ownertype = this.ownertypelist[0]
		this.initRealNameFlow()
		this.$bus.$on('relationFileData', (data) => {
			this.relationFile = data
		})
	},

	computed: {
		...mapGetters({
			getDatas: 'getDatas',
			userInfo: 'getUserInfo',
			custInfo: 'getCustInfo',
			stepInfo: 'getStepInfo'
		})
	},

	onShow() {
		// console.log('onShow')
		// this.initData()
	},

	async mounted() {
		console.log('onShow')
		console.log('md5',);
		await this.InitDigital([cardType])
		this.urlObj = util.dataProcess.getUrlData(true) || {}
		let urlObj = this.urlObj
		if (urlObj && urlObj.system == 'cmms') {
			this.options.poid = this.getRandoms()
		}
		this.initData()
	},
	watch: {
		flowStatus: {
			deep: true,
			handler(newObj) {
				let { hasFail, allSuccess } = this.checkFlowStatus(newObj)
				if (hasFail || allSuccess) {
					this.buttonColor = '#4095E5';
				}
			}
		}
	},
	methods: {
		...mapActions({
			InitDigital: 'init',
			queProcess: 'queProcess',
			saveProcess: 'saveProcess',
		}),
		initData() {
			console.log(this.custInfo)
      let list = this.getDatas(cardType) || []
			console.log(list)
			if (list && list.length) {
        let cardTypeEnum = {}
				list.forEach(val => {
          cardTypeEnum[val.mcode] = val.mname
				})
        this.cardTypeEnum = cardTypeEnum
        this.idtypelist = list
				this.idtype = list[0]
			}

      if (this.stepInfo?.data?.DBKHID?.AgentInfoVerified) {
        const agentPersonInfo = this.stepInfo?.data?.DBKHID?.AgentInfoVerified
        if (agentPersonInfo) {
          this.phone = agentPersonInfo.phone
          this.hasPrivType = agentPersonInfo.hasPrivType
          this.relationType = agentPersonInfo.relationType
          this.relationFile = agentPersonInfo.relationFile
        }
      }
		},
		readIDCard() {
      this.$rauter.navigateTo({
        url: "IdentityCheckDBKH",
        data: {
          poid: this.stepInfo.poid,
          isBehalf: true
        }
      });
		},
    async goNextIDCard() {
      console.log('this.stepInfo?.data?.DBKHID', this.stepInfo?.data?.DBKHID)
      console.log('phone', this.phone)
      console.log('relationFile', this.relationFile)
      console.log('hasPrivType', this.hasPrivType)
      console.log('relationType', this.relationType)

      if (!this.phone) {
        this.$util.feeback.showToast({
          title: '请选择输入代办人联系电话'
        });
        return
      }

      if (!this.checkPhone(this.phone)) {
        this.$util.feeback.showToast({
          title: '代办人联系电话格式不正确'
        });
        return
      }

      if (!this.ownertype) {
        this.$util.feeback.showToast({
          title: '请选择证件归属'
        });
        return
      }

      if (!this.hasPrivType) {
        this.$util.feeback.showToast({
          title: '请选择代办类型'
        });
        return
      }

      if (!this.relationType) {
        this.$util.feeback.showToast({
          title: '请选择与被代办人关系'
        });
        return
      }

      if (!this.relationFile) {
        this.$util.feeback.showToast({
          title: '请上传关系证明图片'
        });
        return
      }

      let agentPersonInfo = {
        phone: this.phone,
        relationFile: this.relationFile,
        hasPrivType: this.hasPrivType,
        relationType: this.relationType
      }

      const DBKHID = {
        ...this.stepInfo?.data?.DBKHID,
        AgentInfoVerified: agentPersonInfo
      }

      console.log('AgentInfoVerified====>DBKHID====>', DBKHID)

      await this.saveProcess({
        data: {
          poid: this.stepInfo.poid,
          cpcode: 'DBKHID',
          data: JSON.stringify(DBKHID),
          temp: 0
        },
        unNeedNext: true,
        afterProcess: () => {
          this.$rauter.navigateTo({
            url: "IdentityCheckDBKH",
            data: {
              poid: this.options.poid,
              isBehalf: true
            }
          });
        }
      });


    },
		addCustInfo() {
			this.$rauter.navigateTo({
				url: 'customersAndBusiness'
			});
		},
		closeComparePop() {
			this.showCompare = false
		},
		// 简单比对系统信息以及读取信息
		async compareCustInfo() {
			return new Promise((resolve, reject) => {
				let arr = []
				const { tempCustInfo, custInfo } = this
				arr.push(tempCustInfo.personname !== custInfo.custname)
				arr.push(tempCustInfo.cardtype !== custInfo.cardtype)
				arr.push(tempCustInfo.cardno !== custInfo.cardnoOpen)
				console.log(custInfo, tempCustInfo, arr)
				this.conflictInfo = arr
				if (arr.filter(n => {
					return n
				}).length > 0) {
					this.showCompare = true
					reject()
				} else {
					resolve()
				}
			})
		},

		updateCustInfo() {
			const { tempCustInfo, custInfo, cardTypeEnum } = this
			this.$http.post(this.$apis.editBootCust, {
				id: custInfo && custInfo.custid ? custInfo.custid : '',
				name: tempCustInfo.personname,
				cardno: tempCustInfo.cardno,
				custid: custInfo && custInfo.custid ? custInfo.custid : '',
				cardtype: tempCustInfo.cardtype,
				review: JSON.stringify([
					{ "label": "客户姓名:", "type": "text", "value": custInfo.custname },
					{ "label": "证件类型:", "type": "select", "value": cardTypeEnum[custInfo.cardtype] },
					{ "label": "证件号码:", "type": "text", "value": custInfo.cardnoOpen },
				])
			}).then(response => {
				let res = response.responseBody
				this.showCompare = false
				uni.showToast({
					title: '更新成功！',
					icon: 'none'
				})
				this.tempCustInfo = {}
				this.conflictInfo = []
				this.saveData()
			}).catch(err => {
				uni.showToast({
					title: err.message,
					icon: 'none'
				})
			})
		},
		// 是否存在移网业务
		hasMobileService() {
			let str = false
			if (this.custInfo && this.custInfo.servs.length > 0) {
				this.custInfo.servs.forEach(n => {
					if (n.mobileno && n.servtype === '0') str = true
				})
			}
			this.inMobileService = str
			return str
		},
		//生成流水号(CMMS AUTH)
		generateSerialNumber() {
			const timestamp = Date.now(); // 获取当前时间戳
			const serialNumber = `CA${timestamp}`; // 根据时间戳生成流水号
			return serialNumber;
		},
		changeIdtype(e) {
			this.idtype = this.idtypelist[e.detail.value]
		},
		changeOwnertype(e) {
			console.log(e)
			this.ownertype = this.ownertypelist[e.detail.value]
		},
		changehasPrivType(e) {
			console.log(e);
			this.hasPrivType = this.hasPrivTypeList[e.detail.value]
		},
		changeRelationType(e){
			console.log(e);
			this.relationType = this.relationTypeList[e.detail.value]
		},
		goRelationFile(){
			this.$rauter.navigateTo({
				url: 'relationFile'
			});
		},
		getRandoms() {
			let poid = '';
			const characters = '0123456789';
			const length = 16;
			for (let i = 0; i < length; i++) {
				const randomIndex = Math.floor(Math.random() * characters.length);
				poid += characters.charAt(randomIndex);
			}
			return poid;
		},
		initRealNameFlow() {
			this.show = false
			this.flowTips = ''
			this.flowStatus.aztStatus = 'loading'
			this.flowStatus.dataStatus = 'loading'
			this.flowStatus.uploadStatus = 'loading'
			this.flowStatus.authStatus = 'loading'
			this.buttonColor = '#7C7C7C'
		},
		beforeClose(action, done) {
			let { hasFail, allSuccess } = this.checkFlowStatus(this.flowStatus)

			if (hasFail || allSuccess) {
				this.initRealNameFlow()
				done()
			} else {
				done(false)
			}
		},
		checkFlowStatus(newObj) {
			const hasFail = Object.values(newObj).includes('fail');
			const allSuccess = Object.values(newObj).every(status => status === 'success');
			return { hasFail, allSuccess }
		},
		aztRequestErr(data, response) {
			let params = {
				data: JSON.stringify(data),
				response: JSON.stringify(response)
			}
			let postParams = {
				url: 'aztRequestErrLog',
				data: params
			}
			this.$http.post(postParams.url, postParams.data, { isAutoLoading: false })
		},
    prob() {
			this.$rauter.navigateTo({
				url: 'aztHelp'
			});
    },
    checkPhone(phone) {
      if (phone.length != 11) {
        return false
      }

      let phoneReg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
      return phoneReg.test(phone)
    }
	}
}
</script>

<style lang="scss" scoped>
.content {
	height: 100%;
	overflow: auto;

	.wrap {
		padding: 20rpx;
		background: linear-gradient(180deg, #0084ff 0%, rgba(31, 143, 247, 0) 50%);

		.form {
			padding-top: 20rpx;
		}
	}
}


.card {
	margin-bottom: 20rpx;
	border-radius: 10rpx;
	padding: 26rpx 20rpx;
	background-color: #fff;
}

.card.sim {
	display: flex;
	flex-direction: column;

	.field {
		margin-top: 20rpx;
		border-top: solid 1px #D8D8D8;
		padding-top: 20rpx;

		&:nth-child(1) {
			margin-top: 0;
			border-top: none;
			padding-top: 0;
		}

		.main {
			flex: 1;
			display: flex;
			align-items: flex-start;
			font-size: 28rpx;
			font-weight: bold;

			.perfix {
				position: relative;
				padding-right: 22rpx;

				.red {
					position: absolute;
					top: 4rpx;
					right: 0;
					font-size: 42rpx;
					color: #f00;
				}

				.sub {
					padding-left: 6rpx;
					font-weight: normal;
					color: #F9A63F;
				}
			}

			.body {
				flex: 1;
				padding: 0 20rpx;

				.text {
					line-height: 2em;
					color: #1F8FF7;

					&.placeholder {
						font-weight: normal;
						color: #999;
					}
				}

				.input {
					display: block;
					width: 100%;
					border: none;
					padding: 10rpx 0;
					line-height: 2em;

					.input-placeholder {
						font-weight: normal;
						color: #999;
					}

					.uni-input-input {
						line-height: 2em;
					}
				}

				.radio-group {
					display: flex;
					align-items: center;

					.item {
						flex: 1;
						display: flex;
						align-items: center;

						.status {
							position: relative;
							width: 26rpx;
							height: 26rpx;
							margin-right: 10rpx;
							border: solid 1px #212121;
							border-radius: 50%;

							&.checked {
								border-color: #1F8FF7;
								background-color: #1F8FF7;
								background-image: url(../../../../static/fifthg/ioa/icon-check.png);
								background-position: center center;
								background-repeat: no-repeat;
								background-size: 20rpx 20rpx;
							}
						}
					}
				}
			}

			.suffix {
				align-self: stretch;

				.btn {
					border: solid 1px #0E67B9;
					border-radius: 40rpx;
					padding: 6rpx 20rpx;
					font-weight: normal;
					color: #0E67B9;
				}

				.icon-arw {
					width: 20rpx;
					height: 100%;
					background-image: url(../../../../static/fifthg/ioa/icon-arw.png);
					background-position: center center;
					background-repeat: no-repeat;
					background-size: 20rpx 20rpx;
				}
			}
		}

		.extra {
			padding-top: 10rpx;
			font-size: 28rpx;
			color: #999;
		}
	}

	.check-group {

		.item {
			display: flex;
			align-items: center;
			margin: 10rpx 0;
			border-radius: 10rpx;
			padding: 20rpx;
			background-color: #F0F8FF;

			.checkbox {
				display: flex;
				align-items: center;
			}

			.status {
				position: relative;
				width: 26rpx;
				height: 26rpx;
				margin-right: 10rpx;
				border: solid 1px #0E67B9;
				border-radius: 50%;

				&.checked {
					border-color: #1F8FF7;
					background-color: #1F8FF7;
					background-image: url(../../../../static/fifthg/ioa/icon-check.png);
					background-position: center center;
					background-repeat: no-repeat;
					background-size: 20rpx 20rpx;
				}
			}

			.label {
				color: #0E67B9;
			}
		}
	}

	.contracts {
		margin: 10rpx 0;
		border-radius: 10rpx;
		border: none;
		padding: 20rpx;
		background-color: #FFF1E1;

		.text {
			text-align: right;
			color: #F9A63F !important;
		}

		.icon-arw {
			background-image: url(../../../../static/fifthg/ioa/icon-arw-yellow.png) !important;
		}
	}

}

.next-btn {
	margin-top: 60rpx;
	padding: 20rpx 0;
	background-color: rgb(0, 132, 255);
	color: #fff;
}

.tips {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  text-decoration: underline;
}

.popup {
	width: 80%;
	border-radius: 10rpx;

	.ctn {
		padding: 40rpx;

		.tit {
			padding-bottom: 20rpx;
			font-size: 32rpx;
			font-weight: bold;
			text-align: center;
		}

		.warn {
			padding-bottom: 20rpx;
			font-size: 28rpx;
		}

		.summary {
			padding: 20rpx 0;
			font-size: 28rpx;
			font-weight: bold;
		}

		.li {
			display: flex;
			flex-direction: row;
			align-items: center;
			padding: 10rpx 0;
			font-size: 28rpx;

			.p {
				color: #7c7c7c;
			}

			.n {
				padding-left: 20rpx;

				&.conflict {
					color: #fb1515;
				}
			}
		}
	}

	.btns {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		border-top: solid 1px #f1f1f1;

		.btn {
			flex: 1;
			padding: 20rpx 0;
			font-size: 32rpx;
			text-align: center;

			&:first-child {
				border-right: solid 1px #f1f1f1;
			}

			&.blue {
				color: #1F77FF;
			}
		}
	}
}

/deep/.van-dialog__header {
	padding-top: 28rpx;
	margin-bottom: 30rpx;
	font-weight: bolder;
}

/deep/.van-dialog__confirm {
	-webkit-box-flex: 1;
	-webkit-flex: 1;
	flex: 1;
	height: 48px;
	margin: 0;
	border: 0;
}

.real-name-flow {
	padding: 20rpx;

	.flow-layout {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;

		.img-size {
			width: 40rpx;
			height: 40rpx;
			background-repeat: no-repeat;
			background-position: center;
			background-size: 40rpx 40rpx;
		}

		.fill {
			width: 100%;
			height: 1rpx;
			border-top: dotted #9A9A9A 6rpx;
			margin: 0 10rpx;
			flex: 1;
		}

		.text {
			white-space: nowrap;
			font-size: 32rpx;
		}
	}

	.error-tips {
		background-color: #EFEFEF;
		padding: 20rpx;
		border-radius: 8rpx;

		.tip {
			line-height: 34rpx;
			font-size: 24rpx;
			color: #6C6C6C;
			word-wrap: break-word;
		}
	}

	.loading {
		background-image: url(../../../../static/flowStatus/loading.png)
	}

	.fail {
		background-image: url(../../../../static/flowStatus/fail.png)
	}

	.success {
		background-image: url(../../../../static/flowStatus/success.png)
	}
}
</style>
