<!-- 开户流程的办理记录列表 -->
<template>

	<view >
		<action-bar title="办理记录" leftIcon="back"></action-bar>
		<ActionScrollView>
			<common-msg-box :cardBox="true" :msgList="records" @onItemClick="onItemClick"></common-msg-box>
		</ActionScrollView>

	</view>

</template>

<script>
	
	import CommonMsgBox from "@/components/CommonMsgBox.vue"
	import StepRecordBean from "@/common/js/bean/StepRecordBean.js"
	import {
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				stepRecords: [],
				filterRecords:[]
			};
		},
		created() {
			this.queProcesses();
		},
		components: {
			CommonMsgBox
		},

		computed: {
			...mapGetters({
				custInfo: "getCustInfo",
			}),
			records() {
				console.log('stepRecords',this.stepRecords);
				let records = this.filterRecords.map(stepBean => {
					return {
						msgInfoList: this.$util.dataProcess.getBeansMsg(StepRecordBean, stepBean)
					}
				})
				return records
			}
		},

		methods: {
			...mapActions({
				localCustInfo:'localCustInfo'
			}),
			queProcesses() {
				this.$http.post(this.$apis.queProcesses, {
					"pageNo": 1,
					"pageSize": 100,
				}).then(resp => {
					console.log(resp)
					this.stepRecords = resp.responseBody.result
					if(this.stepRecords){
						this.filterRecords = this.stepRecords.filter(record => record.pcode === 'DBKH')
						console.log('filter',this.filterRecords);
					}
				});
			},
			async onItemClick(index){
				console.log('filterRecords',this.filterRecords);
				let stepBean = this.filterRecords[index]
				// if(stepBean.statusname.indexOf("已失效") != -1){
				// 	uni.showModal({
				// 		title: '温馨提示',
				// 		content: '该办理单已失效，先重新办理！',
				// 		success: function(res) {
				// 		}
				// 	});
				// }else{
					let orderCustId = stepBean.custid;
					if(!this.custInfo && orderCustId){
						//如果还没定位客户，并且订单里面存在客户ID的，需要去定位一次
						await this.localCustInfo({custid:orderCustId})
					}else if(this.custInfo && orderCustId && this.custInfo.custid != orderCustId){
						//如果已经定位客户，并且当前的客户id与订单的id不一定的，需要去重新定位一次
						await this.localCustInfo({custid:orderCustId})
					}
					this.$util.iframeInit.send({type: 'custid', custid: orderCustId})
					this.$rauter.navigateTo({
						url: "behalfStep",
						data:{
							poid:stepBean.poid
						}
					});
				// }
			}
		}
	}
</script>

<style lang='scss'>

</style>
